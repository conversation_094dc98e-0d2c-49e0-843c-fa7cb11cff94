import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ThemeProvider extends ChangeNotifier {
  // Theme mode
  ThemeMode _themeMode = ThemeMode.light;
  ThemeMode get themeMode => _themeMode;

  // Primary color
  Color _primaryColor = const Color(0xFF6A11CB);
  Color get primaryColor => _primaryColor;

  // Secondary color
  Color _secondaryColor = const Color(0xFF2575FC);
  Color get secondaryColor => _secondaryColor;

  // Accent color
  Color _accentColor = const Color(0xFFFF4081);
  Color get accentColor => _accentColor;

  // Navigation style
  String _navigationStyle = 'bottom_tabs';
  String get navigationStyle => _navigationStyle;

  // Home layout
  String _homeLayout = 'grid';
  String get homeLayout => _homeLayout;

  // Font family
  String _fontFamily = 'Poppins';
  String get fontFamily => _fontFamily;

  // Font size scale
  double _fontSizeScale = 1.0;
  double get fontSizeScale => _fontSizeScale;

  // Button style
  String _buttonStyle = 'rounded';
  String get buttonStyle => _buttonStyle;

  // Card style
  double _cardBorderRadius = 16.0;
  double get cardBorderRadius => _cardBorderRadius;

  // Feature flags
  final Map<String, bool> _featureFlags = {
    'dark_mode': true,
    'push_notifications': true,
    'in_app_messaging': true,
    'community_forums': true,
    'asset_marketplace': true,
    'mentorship_program': false,
    'live_streaming': false,
    'ai_content_suggestions': false,
  };
  Map<String, bool> get featureFlags => _featureFlags;

  // Constructor
  ThemeProvider() {
    _loadSettings();
  }

  // Load settings from shared preferences
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    // Load theme mode
    final themeModeString = prefs.getString('theme_mode') ?? 'light';
    if (themeModeString == 'dark') {
      _themeMode = ThemeMode.dark;
    } else if (themeModeString == 'system') {
      _themeMode = ThemeMode.system;
    } else {
      _themeMode = ThemeMode.light;
    }
    
    // Load colors
    final primaryColorValue = prefs.getInt('primary_color') ?? 0xFF6A11CB;
    _primaryColor = Color(primaryColorValue);
    
    final secondaryColorValue = prefs.getInt('secondary_color') ?? 0xFF2575FC;
    _secondaryColor = Color(secondaryColorValue);
    
    final accentColorValue = prefs.getInt('accent_color') ?? 0xFFFF4081;
    _accentColor = Color(accentColorValue);
    
    // Load layout settings
    _navigationStyle = prefs.getString('navigation_style') ?? 'bottom_tabs';
    _homeLayout = prefs.getString('home_layout') ?? 'grid';
    
    // Load typography settings
    _fontFamily = prefs.getString('font_family') ?? 'Poppins';
    _fontSizeScale = prefs.getDouble('font_size_scale') ?? 1.0;
    
    // Load component settings
    _buttonStyle = prefs.getString('button_style') ?? 'rounded';
    _cardBorderRadius = prefs.getDouble('card_border_radius') ?? 16.0;
    
    // Load feature flags
    final featureFlagsMap = prefs.getStringList('feature_flags_keys');
    final featureFlagsValues = prefs.getStringList('feature_flags_values');
    
    if (featureFlagsMap != null && featureFlagsValues != null && 
        featureFlagsMap.length == featureFlagsValues.length) {
      for (int i = 0; i < featureFlagsMap.length; i++) {
        _featureFlags[featureFlagsMap[i]] = featureFlagsValues[i] == 'true';
      }
    }
    
    notifyListeners();
  }

  // Save settings to shared preferences
  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    
    // Save theme mode
    String themeModeString = 'light';
    if (_themeMode == ThemeMode.dark) {
      themeModeString = 'dark';
    } else if (_themeMode == ThemeMode.system) {
      themeModeString = 'system';
    }
    await prefs.setString('theme_mode', themeModeString);
    
    // Save colors
    await prefs.setInt('primary_color', _primaryColor.value);
    await prefs.setInt('secondary_color', _secondaryColor.value);
    await prefs.setInt('accent_color', _accentColor.value);
    
    // Save layout settings
    await prefs.setString('navigation_style', _navigationStyle);
    await prefs.setString('home_layout', _homeLayout);
    
    // Save typography settings
    await prefs.setString('font_family', _fontFamily);
    await prefs.setDouble('font_size_scale', _fontSizeScale);
    
    // Save component settings
    await prefs.setString('button_style', _buttonStyle);
    await prefs.setDouble('card_border_radius', _cardBorderRadius);
    
    // Save feature flags
    await prefs.setStringList('feature_flags_keys', _featureFlags.keys.toList());
    await prefs.setStringList('feature_flags_values', 
        _featureFlags.values.map((value) => value.toString()).toList());
  }

  // Update theme mode
  void setThemeMode(ThemeMode mode) {
    _themeMode = mode;
    _saveSettings();
    notifyListeners();
  }

  // Update color scheme
  void setColorScheme({Color? primary, Color? secondary, Color? accent}) {
    if (primary != null) _primaryColor = primary;
    if (secondary != null) _secondaryColor = secondary;
    if (accent != null) _accentColor = accent;
    _saveSettings();
    notifyListeners();
  }

  // Update navigation style
  void setNavigationStyle(String style) {
    _navigationStyle = style;
    _saveSettings();
    notifyListeners();
  }

  // Update home layout
  void setHomeLayout(String layout) {
    _homeLayout = layout;
    _saveSettings();
    notifyListeners();
  }

  // Update typography settings
  void setTypography({String? fontFamily, double? fontSizeScale}) {
    if (fontFamily != null) _fontFamily = fontFamily;
    if (fontSizeScale != null) _fontSizeScale = fontSizeScale;
    _saveSettings();
    notifyListeners();
  }

  // Update component settings
  void setComponentStyles({String? buttonStyle, double? cardBorderRadius}) {
    if (buttonStyle != null) _buttonStyle = buttonStyle;
    if (cardBorderRadius != null) _cardBorderRadius = cardBorderRadius;
    _saveSettings();
    notifyListeners();
  }

  // Update feature flag
  void setFeatureFlag(String feature, bool enabled) {
    _featureFlags[feature] = enabled;
    _saveSettings();
    notifyListeners();
  }

  // Get theme data based on current settings
  ThemeData getThemeData(bool isDark) {
    return ThemeData(
      useMaterial3: true,
      brightness: isDark ? Brightness.dark : Brightness.light,
      colorScheme: ColorScheme(
        brightness: isDark ? Brightness.dark : Brightness.light,
        primary: _primaryColor,
        onPrimary: Colors.white,
        secondary: _secondaryColor,
        onSecondary: Colors.white,
        error: Colors.red,
        onError: Colors.white,
        surface: isDark ? const Color(0xFF1E1E1E) : Colors.white,
        onSurface: isDark ? Colors.white : Colors.black,
      ),
      fontFamily: _fontFamily,
      textTheme: _getTextTheme(isDark),
      cardTheme: CardTheme(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_cardBorderRadius),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: _buttonStyle == 'rounded' 
                ? BorderRadius.circular(20) 
                : _buttonStyle == 'standard' 
                    ? BorderRadius.circular(8) 
                    : BorderRadius.zero,
          ),
        ),
      ),
    );
  }

  // Get text theme based on current settings
  TextTheme _getTextTheme(bool isDark) {
    final baseTextTheme = isDark ? Typography.whiteMountainView : Typography.blackMountainView;
    
    return baseTextTheme.apply(
      fontFamily: _fontFamily,
      displayColor: isDark ? Colors.white : Colors.black,
      bodyColor: isDark ? Colors.white : Colors.black,
      fontSizeFactor: _fontSizeScale,
    );
  }
}
