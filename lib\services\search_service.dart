import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'dart:async';

import '../models/asset_model.dart';
import '../models/user_model.dart';

/// Service for handling search functionality across the app
class SearchService {
  static final SearchService _instance = SearchService._internal();

  factory SearchService() {
    return _instance;
  }

  SearchService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Search for assets based on keywords
  Future<List<AssetModel>> searchAssets(String query) async {
    if (query.isEmpty) {
      return [];
    }

    // Normalize the query
    final normalizedQuery = query.toLowerCase().trim();
    final queryTerms = normalizedQuery.split(' ');

    try {
      // Search in title, description, and keywords
      final QuerySnapshot titleSnapshot = await _firestore
          .collection('assets')
          .where('isApproved', isEqualTo: true)
          .where('titleLower', isGreaterThanOrEqualTo: normalizedQuery)
          .where('titleLower', isLessThanOrEqualTo: '$normalizedQuery\uf8ff')
          .limit(10)
          .get();

      final QuerySnapshot keywordsSnapshot = await _firestore
          .collection('assets')
          .where('isApproved', isEqualTo: true)
          .where('keywords', arrayContainsAny: queryTerms)
          .limit(20)
          .get();

      // Combine results and remove duplicates
      final Map<String, AssetModel> uniqueAssets = {};

      // Process title results
      for (var doc in titleSnapshot.docs) {
        final asset = AssetModel.fromFirestore(doc);
        if (asset.id != null) {
          uniqueAssets[asset.id!] = asset;
        }
      }

      // Process keywords results
      for (var doc in keywordsSnapshot.docs) {
        final asset = AssetModel.fromFirestore(doc);
        if (asset.id != null && !uniqueAssets.containsKey(asset.id)) {
          uniqueAssets[asset.id!] = asset;
        }
      }

      // Get user interaction data to sort results
      final userInteractions = await getUserSearchInteractions(normalizedQuery, 'asset');

      // Sort results based on user interactions
      final sortedAssets = uniqueAssets.values.toList()
        ..sort((a, b) {
          // Check if assets have interaction data
          final aInteractions = userInteractions[a.id] ?? 0;
          final bInteractions = userInteractions[b.id] ?? 0;

          // Sort by interaction count (descending)
          if (aInteractions != bInteractions) {
            return bInteractions.compareTo(aInteractions);
          }

          // If interaction counts are equal, sort by title match
          final aTitleMatch = a.title?.toLowerCase().contains(normalizedQuery) ?? false;
          final bTitleMatch = b.title?.toLowerCase().contains(normalizedQuery) ?? false;

          if (aTitleMatch && !bTitleMatch) return -1;
          if (!aTitleMatch && bTitleMatch) return 1;

          // If title matches are equal, sort by keyword match count
          final aKeywordMatches = queryTerms.where((term) =>
              a.keywords.any((keyword) => keyword.toLowerCase().contains(term))).length;
          final bKeywordMatches = queryTerms.where((term) =>
              b.keywords.any((keyword) => keyword.toLowerCase().contains(term))).length;

          return bKeywordMatches.compareTo(aKeywordMatches);
        });

      return sortedAssets;
    } catch (e) {
      debugPrint('Error searching assets: $e');
      return [];
    }
  }

  /// Search for users based on username or name
  Future<List<UserModel>> searchUsers(String query) async {
    if (query.isEmpty) {
      return [];
    }

    // Normalize the query
    final normalizedQuery = query.toLowerCase().trim();

    try {
      // Search by username
      final QuerySnapshot usernameSnapshot = await _firestore
          .collection('users')
          .where('usernameLower', isGreaterThanOrEqualTo: normalizedQuery)
          .where('usernameLower', isLessThanOrEqualTo: '$normalizedQuery\uf8ff')
          .limit(10)
          .get();

      // Search by name
      final QuerySnapshot nameSnapshot = await _firestore
          .collection('users')
          .where('nameLower', isGreaterThanOrEqualTo: normalizedQuery)
          .where('nameLower', isLessThanOrEqualTo: '$normalizedQuery\uf8ff')
          .limit(10)
          .get();

      // Combine results and remove duplicates
      final Map<String, UserModel> uniqueUsers = {};

      // Process username results
      for (var doc in usernameSnapshot.docs) {
        final user = UserModel.fromFirestore(doc);
        uniqueUsers[user.uid] = user;
      }

      // Process name results
      for (var doc in nameSnapshot.docs) {
        final user = UserModel.fromFirestore(doc);
        if (!uniqueUsers.containsKey(user.uid)) {
          uniqueUsers[user.uid] = user;
        }
      }

      // Get user interaction data to sort results
      final userInteractions = await getUserSearchInteractions(normalizedQuery, 'user');

      // Sort results based on user interactions
      final sortedUsers = uniqueUsers.values.toList()
        ..sort((a, b) {
          // Check if users have interaction data
          final aInteractions = userInteractions[a.uid] ?? 0;
          final bInteractions = userInteractions[b.uid] ?? 0;

          // Sort by interaction count (descending)
          if (aInteractions != bInteractions) {
            return bInteractions.compareTo(aInteractions);
          }

          // If interaction counts are equal, sort by username match
          final aUsernameMatch = a.username?.toLowerCase().contains(normalizedQuery) ?? false;
          final bUsernameMatch = b.username?.toLowerCase().contains(normalizedQuery) ?? false;

          if (aUsernameMatch && !bUsernameMatch) return -1;
          if (!aUsernameMatch && bUsernameMatch) return 1;

          // If username matches are equal, sort by display name match
          final aNameMatch = a.displayName.toLowerCase().contains(normalizedQuery);
          final bNameMatch = b.displayName.toLowerCase().contains(normalizedQuery);

          if (aNameMatch && !bNameMatch) return -1;
          if (!aNameMatch && bNameMatch) return 1;

          return 0;
        });

      return sortedUsers;
    } catch (e) {
      debugPrint('Error searching users: $e');
      return [];
    }
  }

  /// Track user interaction with search results
  Future<void> trackSearchInteraction({
    required String query,
    required String resultId,
    required String resultType, // 'asset' or 'user'
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return;

      final normalizedQuery = query.toLowerCase().trim();

      // Create or update the search interaction record
      await _firestore
          .collection('searchInteractions')
          .doc('${currentUser.uid}_${normalizedQuery}_${resultType}_$resultId')
          .set({
            'userId': currentUser.uid,
            'query': normalizedQuery,
            'resultId': resultId,
            'resultType': resultType,
            'interactionCount': FieldValue.increment(1),
            'lastInteraction': FieldValue.serverTimestamp(),
          }, SetOptions(merge: true));

    } catch (e) {
      debugPrint('Error tracking search interaction: $e');
    }
  }

  /// Get user search interactions for a specific query and result type
  Future<Map<String, int>> getUserSearchInteractions(String query, String resultType) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return {};

      final normalizedQuery = query.toLowerCase().trim();

      // Get all interactions for this query and result type
      final QuerySnapshot interactionsSnapshot = await _firestore
          .collection('searchInteractions')
          .where('userId', isEqualTo: currentUser.uid)
          .where('query', isEqualTo: normalizedQuery)
          .where('resultType', isEqualTo: resultType)
          .get();

      // Create a map of resultId -> interactionCount
      final Map<String, int> interactions = {};
      for (var doc in interactionsSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        interactions[data['resultId']] = data['interactionCount'] ?? 0;
      }

      return interactions;
    } catch (e) {
      debugPrint('Error getting user search interactions: $e');
      return {};
    }
  }

  /// Get search suggestions based on user's previous searches
  Future<List<String>> getSearchSuggestions(String query, String type) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) return [];

      final normalizedQuery = query.toLowerCase().trim();
      if (normalizedQuery.isEmpty) return [];

      // Get user's previous searches that start with the query
      final QuerySnapshot suggestionsSnapshot = await _firestore
          .collection('searchInteractions')
          .where('userId', isEqualTo: currentUser.uid)
          .where('resultType', isEqualTo: type)
          .where('query', isGreaterThanOrEqualTo: normalizedQuery)
          .where('query', isLessThanOrEqualTo: '$normalizedQuery\uf8ff')
          .orderBy('query')
          .orderBy('lastInteraction', descending: true)
          .limit(5)
          .get();

      // Extract unique queries
      final Set<String> uniqueQueries = {};
      for (var doc in suggestionsSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        uniqueQueries.add(data['query'] as String);
      }

      return uniqueQueries.toList();
    } catch (e) {
      debugPrint('Error getting search suggestions: $e');
      return [];
    }
  }
}
