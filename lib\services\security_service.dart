import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';
import 'dart:math';

/// A service class to handle security-related operations
class SecurityService {
  // Singleton pattern
  static final SecurityService _instance = SecurityService._internal();
  factory SecurityService() => _instance;
  SecurityService._internal();

  // Firebase instances
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  
  // Constants
  static const String _adminVerificationCodeKey = 'adminVerificationCode';
  static const String _adminLastVerifiedKey = 'adminLastVerified';
  static const String _adminDeviceIdKey = 'adminDeviceId';
  static const int _adminVerificationExpiryHours = 24; // Admin verification expires after 24 hours
  
  // Generate a random verification code
  String _generateVerificationCode() {
    final random = Random.secure();
    final values = List<int>.generate(6, (i) => random.nextInt(10));
    return values.join();
  }
  
  // Generate a device ID
  Future<String> _generateDeviceId() async {
    final random = Random.secure();
    final values = List<int>.generate(32, (i) => random.nextInt(256));
    final deviceId = base64Url.encode(values);
    
    // Store the device ID
    await _secureStorage.write(key: _adminDeviceIdKey, value: deviceId);
    
    return deviceId;
  }
  
  // Get the device ID, generating one if it doesn't exist
  Future<String> getDeviceId() async {
    final deviceId = await _secureStorage.read(key: _adminDeviceIdKey);
    if (deviceId == null || deviceId.isEmpty) {
      return _generateDeviceId();
    }
    return deviceId;
  }
  
  // Hash a string using SHA-256
  String _hashString(String input) {
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }
  
  // Set up admin verification
  Future<String> setupAdminVerification() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }
      
      // Check if user is admin
      final userDoc = await _firestore.collection('users').doc(currentUser.uid).get();
      if (!userDoc.exists || userDoc.data()?['isAdmin'] != true) {
        throw Exception('User is not an admin');
      }
      
      // Generate a new verification code
      final verificationCode = _generateVerificationCode();
      
      // Store the verification code (hashed for security)
      await _secureStorage.write(
        key: _adminVerificationCodeKey, 
        value: _hashString(verificationCode)
      );
      
      // Store the verification timestamp
      await _secureStorage.write(
        key: _adminLastVerifiedKey, 
        value: DateTime.now().toIso8601String()
      );
      
      // Get or generate device ID
      final deviceId = await getDeviceId();
      
      // Store the admin verification in Firestore for audit purposes
      await _firestore.collection('admin_verifications').add({
        'adminId': currentUser.uid,
        'adminEmail': currentUser.email,
        'deviceId': deviceId,
        'timestamp': FieldValue.serverTimestamp(),
        'verified': true,
      });
      
      return verificationCode;
    } catch (e) {
      debugPrint('Error setting up admin verification: $e');
      rethrow;
    }
  }
  
  // Verify admin code
  Future<bool> verifyAdminCode(String code) async {
    try {
      // Get the stored verification code
      final storedHash = await _secureStorage.read(key: _adminVerificationCodeKey);
      if (storedHash == null || storedHash.isEmpty) {
        return false;
      }
      
      // Check if the verification has expired
      final lastVerifiedStr = await _secureStorage.read(key: _adminLastVerifiedKey);
      if (lastVerifiedStr != null && lastVerifiedStr.isNotEmpty) {
        final lastVerified = DateTime.parse(lastVerifiedStr);
        final now = DateTime.now();
        final difference = now.difference(lastVerified);
        
        if (difference.inHours > _adminVerificationExpiryHours) {
          // Verification has expired
          await _secureStorage.delete(key: _adminVerificationCodeKey);
          await _secureStorage.delete(key: _adminLastVerifiedKey);
          return false;
        }
      }
      
      // Verify the code
      final inputHash = _hashString(code);
      return inputHash == storedHash;
    } catch (e) {
      debugPrint('Error verifying admin code: $e');
      return false;
    }
  }
  
  // Check if admin verification is required
  Future<bool> isAdminVerificationRequired() async {
    try {
      // Check if verification code exists
      final storedHash = await _secureStorage.read(key: _adminVerificationCodeKey);
      if (storedHash == null || storedHash.isEmpty) {
        return true;
      }
      
      // Check if verification has expired
      final lastVerifiedStr = await _secureStorage.read(key: _adminLastVerifiedKey);
      if (lastVerifiedStr == null || lastVerifiedStr.isEmpty) {
        return true;
      }
      
      final lastVerified = DateTime.parse(lastVerifiedStr);
      final now = DateTime.now();
      final difference = now.difference(lastVerified);
      
      return difference.inHours > _adminVerificationExpiryHours;
    } catch (e) {
      debugPrint('Error checking if admin verification is required: $e');
      return true;
    }
  }
  
  // Log admin action
  Future<void> logAdminAction({
    required String action,
    required String targetId,
    String? targetType,
    Map<String, dynamic>? details,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }
      
      // Get device ID
      final deviceId = await getDeviceId();
      
      // Log the action
      await _firestore.collection('admin_activity_log').add({
        'action': action,
        'targetId': targetId,
        'targetType': targetType,
        'details': details,
        'adminId': currentUser.uid,
        'adminEmail': currentUser.email,
        'deviceId': deviceId,
        'timestamp': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error logging admin action: $e');
      // Don't rethrow - logging should not interrupt the main flow
    }
  }
}
