// This file contains web-specific initialization for Firebase
// It helps bridge the gap between the Flutter Firebase plugins and the web platform

import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'firebase_options.dart';

/// A utility class for initializing Firebase with web-specific handling
class FirebaseWebInit {
  /// Flag to track initialization status
  static bool _initialized = false;

  /// Check if Firebase has been initialized
  static bool get isInitialized => _initialized;

  /// Initialize Firebase for both web and non-web platforms
  static Future<FirebaseApp> initialize() async {
    // If already initialized, return the existing app
    if (_initialized) {
      try {
        return Firebase.app();
      } catch (e) {
        debugPrint('Error getting existing Firebase app: $e');
        // Continue with initialization if getting the existing app fails
      }
    }

    try {
      FirebaseApp app;

      // Initialize Firebase with the appropriate options
      if (kIsWeb) {
        debugPrint('Initializing Firebase for Web platform');

        // For web, we use a simpler approach with the web-specific options
        app = await Firebase.initializeApp(
          options: DefaultFirebaseOptions.web,
          name: 'DEFAULT',
        );

        debugPrint('Firebase Web initialized successfully with name: ${app.name}');
      } else {
        debugPrint('Initializing Firebase for ${defaultTargetPlatform.toString()} platform');

        // For non-web platforms, use the platform-specific options
        app = await Firebase.initializeApp(
          options: DefaultFirebaseOptions.currentPlatform,
        );

        debugPrint('Firebase initialized successfully for ${defaultTargetPlatform.toString()}');
      }

      // Mark as initialized
      _initialized = true;
      return app;
    } catch (e) {
      debugPrint('Error initializing Firebase: $e');
      rethrow;
    }
  }
}
