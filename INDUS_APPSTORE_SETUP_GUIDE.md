# Indus AppStore In-App Purchase Setup Guide

## 🎯 Overview

This guide will help you complete the setup of Indus AppStore in-app purchases for your Asatu app. The integration has been implemented and is ready for configuration.

## ✅ What's Already Done

### 1. **Code Integration Complete**
- ✅ Indus IAP Service implemented
- ✅ Payment Migration Service created
- ✅ Android configuration updated
- ✅ Dependencies installed
- ✅ UI components created
- ✅ Configuration files ready

### 2. **Your Provided Information**
- ✅ Developer Email: `<EMAIL>`
- ✅ App Name: `Asatu - developer community and marketplace`
- ✅ Package Name: `com.asatu.com`
- ✅ Currency: INR (₹)
- ✅ Pricing: User-defined amounts

## 📋 Next Steps Required

### Step 1: Indus AppStore Developer Console Setup

1. **Create Developer Account**
   - Visit Indus AppStore Developer Console
   - Register with email: `<EMAIL>`
   - Complete developer verification process

2. **Create App Listing**
   - App Name: `Asatu - developer community and marketplace`
   - Package Name: `com.asatu.com`
   - Upload app icons, screenshots, and description

### Step 2: Configure In-App Products

#### **Wallet Top-up Products (Required)**
Create these products in Indus AppStore Console:

| Product ID | Amount | Title | Description |
|------------|--------|-------|-------------|
| `com.asatu.wallet.50inr` | ₹50 | ₹50 Wallet Top-up | Add ₹50 to your Asatu wallet |
| `com.asatu.wallet.100inr` | ₹100 | ₹100 Wallet Top-up | Add ₹100 to your Asatu wallet |
| `com.asatu.wallet.200inr` | ₹200 | ₹200 Wallet Top-up | Add ₹200 to your Asatu wallet |
| `com.asatu.wallet.500inr` | ₹500 | ₹500 Wallet Top-up | Add ₹500 to your Asatu wallet |
| `com.asatu.wallet.1000inr` | ₹1000 | ₹1000 Wallet Top-up | Add ₹1000 to your Asatu wallet |
| `com.asatu.wallet.2000inr` | ₹2000 | ₹2000 Wallet Top-up | Add ₹2000 to your Asatu wallet |
| `com.asatu.wallet.5000inr` | ₹5000 | ₹5000 Wallet Top-up | Add ₹5000 to your Asatu wallet |

#### **Premium Subscription Products (Optional)**
| Product ID | Price | Duration | Title |
|------------|-------|----------|-------|
| `com.asatu.premium.monthly` | ₹99 | 1 Month | Asatu Premium Monthly |
| `com.asatu.premium.yearly` | ₹999 | 1 Year | Asatu Premium Yearly |

### Step 3: Testing Setup

1. **Create Test Accounts**
   - Add `<EMAIL>` as test account
   - Create additional test accounts if needed

2. **Test Product Configuration**
   - Verify all product IDs are correctly configured
   - Test purchase flow with test accounts
   - Verify receipt validation

### Step 4: App Configuration Updates

#### **Update Configuration File**
Edit `lib/config/indus_appstore_config.dart`:

```dart
// Update these values based on Indus AppStore requirements
static const String appStoreUrl = 'YOUR_ACTUAL_INDUS_STORE_URL';
static const String baseApiUrl = 'YOUR_ACTUAL_API_URL';
static const bool isTestMode = false; // Set to false for production
```

#### **Update Product IDs (if different)**
If Indus AppStore requires different product ID formats, update:
- `lib/services/indus_iap_service.dart`
- `lib/config/indus_appstore_config.dart`

### Step 5: Build and Test

1. **Build APK for Testing**
   ```bash
   flutter build apk --debug
   ```

2. **Test on Device**
   ```bash
   flutter run -d YOUR_DEVICE_ID
   ```

3. **Test Purchase Flow**
   - Navigate to wallet top-up page
   - Select amount
   - Complete purchase with test account
   - Verify wallet balance updates

## 🔧 Configuration Files

### Key Files to Review:
- `lib/config/indus_appstore_config.dart` - Main configuration
- `lib/services/indus_iap_service.dart` - IAP implementation
- `lib/services/payment_migration_service.dart` - Payment handling
- `lib/wallet_topup_page.dart` - UI for wallet top-up

## 🚀 How to Use the New System

### For Users:
1. Open app and navigate to wallet
2. Tap "Add Money" 
3. Select predefined amount or enter custom amount (₹50-₹10,000)
4. Choose payment method (Indus AppStore recommended)
5. Complete purchase through Indus AppStore
6. Wallet balance updates automatically

### For Developers:
```dart
// Initialize payment service
final paymentService = PaymentMigrationService();
await paymentService.initialize();

// Process wallet top-up
final result = await paymentService.processWalletTopUp(500.0);
if (result['success']) {
  // Handle success
} else {
  // Handle error
}
```

## 🔒 Security Features

- ✅ Server-side purchase verification
- ✅ Receipt validation
- ✅ Fraud prevention
- ✅ Secure transaction recording
- ✅ Firebase security rules integration

## 📊 Revenue Model

- **Platform Fee**: 20% (configurable)
- **Developer Share**: 80%
- **Supported Amounts**: ₹50 - ₹10,000
- **Custom Amounts**: Enabled
- **Refund Policy**: 7 days

## 🛠️ Troubleshooting

### Common Issues:

1. **Products Not Loading**
   - Verify product IDs in Indus AppStore Console
   - Check internet connection
   - Ensure app is signed with correct certificate

2. **Purchase Fails**
   - Verify test account setup
   - Check Indus AppStore account balance
   - Ensure app is in testing mode

3. **Balance Not Updating**
   - Check Firebase security rules
   - Verify transaction recording
   - Check network connectivity

## 📞 Support

For technical issues:
- Check Firebase console for errors
- Review app logs for IAP-related messages
- Contact Indus AppStore support for store-specific issues

## 🎉 Next Steps After Setup

1. **Test thoroughly** with multiple test accounts
2. **Submit app** to Indus AppStore for review
3. **Monitor transactions** in Firebase console
4. **Gradually migrate** users from Stripe to IAP
5. **Analyze performance** and optimize as needed

---

**Ready to proceed?** Please complete the Indus AppStore Developer Console setup and product configuration, then test the integration!
