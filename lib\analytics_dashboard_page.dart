import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';

class AnalyticsDashboardPage extends StatefulWidget {
  const AnalyticsDashboardPage({super.key});

  @override
  _AnalyticsDashboardPageState createState() => _AnalyticsDashboardPageState();
}

class _AnalyticsDashboardPageState extends State<AnalyticsDashboardPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();
  bool _showAppBarTitle = false;

  // Sample data for AI insights
  final Map<String, dynamic> aiInsightsData = {
    'growthSuggestions': [
      {
        'title': 'Increase Asset Downloads',
        'description': 'Your UI Kit assets are performing well. Consider creating more UI kits to grow your downloads by an estimated 35%.',
        'impact': 'High',
        'impactValue': 35,
        'category': 'Assets',
        'icon': Icons.trending_up,
        'color': const Color(0xFF00C9FF),
      },
      {
        'title': 'Optimize Post Timing',
        'description': 'Your engagement is highest on weekends. Try posting more content on Saturday mornings to increase visibility by 28%.',
        'impact': 'Medium',
        'impactValue': 28,
        'category': 'Engagement',
        'icon': Icons.schedule,
        'color': const Color(0xFF6A11CB),
      },
      {
        'title': 'Complete Your Profile',
        'description': 'Adding your skills and portfolio links can increase your profile views by 45% and mentorship requests by 60%.',
        'impact': 'High',
        'impactValue': 45,
        'category': 'Profile',
        'icon': Icons.person,
        'color': const Color(0xFFFF416C),
      },
    ],
    'fraudDetection': [
      {
        'title': 'Suspicious Download Pattern',
        'description': 'We\'ve detected unusual download patterns for your "Modern UI Kit" asset. 15% of downloads appear to be from suspicious accounts.',
        'severity': 'Medium',
        'category': 'Assets',
        'icon': Icons.warning_amber,
        'color': const Color(0xFFFF8008),
        'timestamp': '2 hours ago',
        'actionRequired': true,
      },
      {
        'title': 'Potential Fake Followers',
        'description': 'Our system has identified 23 potential bot accounts following you in the last week. This may affect your engagement metrics.',
        'severity': 'High',
        'category': 'Followers',
        'icon': Icons.security,
        'color': const Color(0xFFFF416C),
        'timestamp': '1 day ago',
        'actionRequired': true,
      },
    ],
    'optimizationTips': [
      {
        'title': 'Mentorship Pricing',
        'description': 'Based on your expertise and market rates, you could increase your mentorship pricing by 15% without affecting demand.',
        'impact': 'Medium',
        'impactValue': 15,
        'category': 'Monetization',
        'icon': Icons.school,
        'color': const Color(0xFF92FE9D),
      },
      {
        'title': 'Content Focus',
        'description': 'Your Flutter tutorials get 3x more engagement than other content. Consider focusing more on Flutter-related assets and posts.',
        'impact': 'High',
        'impactValue': 300,
        'category': 'Content Strategy',
        'icon': Icons.auto_awesome,
        'color': const Color(0xFF6A11CB),
      },
    ],
  };

  // Define vibrant gradient colors
  final List<List<Color>> gradients = [
    [const Color(0xFF6A11CB), const Color(0xFF2575FC)], // Purple to Blue
    [const Color(0xFFFF416C), const Color(0xFFFF4B2B)], // Pink to Orange
    [const Color(0xFF00C9FF), const Color(0xFF92FE9D)], // Blue to Green
    [const Color(0xFFFF8008), const Color(0xFFFFC837)], // Orange to Yellow
  ];

  // Sample data for earnings
  final Map<String, dynamic> earningsData = {
    'totalEarnings': 2450.75,
    'sources': [
      {'name': 'Assets', 'amount': 1250.50, 'percentage': 51},
      {'name': 'Mentorship', 'amount': 750.25, 'percentage': 31},
      {'name': 'Contests', 'amount': 450.00, 'percentage': 18},
    ],
    'recentTransactions': [
      {'date': '15 Jun 2024', 'description': 'Asset Download: UI Kit', 'amount': 45.00, 'type': 'credit'},
      {'date': '12 Jun 2024', 'description': 'Mentorship Session', 'amount': 120.00, 'type': 'credit'},
      {'date': '10 Jun 2024', 'description': 'Withdrawal to Bank', 'amount': 500.00, 'type': 'debit'},
      {'date': '05 Jun 2024', 'description': 'Contest Prize', 'amount': 200.00, 'type': 'credit'},
    ],
    'monthlyEarnings': [
      {'month': 'Jan', 'amount': 1200},
      {'month': 'Feb', 'amount': 1500},
      {'month': 'Mar', 'amount': 1800},
      {'month': 'Apr', 'amount': 1400},
      {'month': 'May', 'amount': 2100},
      {'month': 'Jun', 'amount': 2450},
    ],
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);

    // Listen to scroll events to animate the app bar
    _scrollController.addListener(() {
      setState(() {
        _showAppBarTitle = _scrollController.offset > 100;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA), // Light background
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
        title: AnimatedOpacity(
          opacity: _showAppBarTitle ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 200),
          child: Text(
            'Analytics',
            style: GoogleFonts.poppins(
              color: const Color(0xFF333333),
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
        ),
        actions: [
          IconButton(
            icon: Stack(
              children: [
                Icon(Icons.notifications_outlined, color: Colors.grey[800]),
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: gradients[1],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: gradients[1][0].withAlpha(100),
                          blurRadius: 6,
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 8,
                      minHeight: 8,
                    ),
                  ),
                ),
              ],
            ),
            onPressed: () {},
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: GestureDetector(
              onTap: () {},
              child: Hero(
                tag: 'analytics-profile-avatar',
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white, width: 2),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(20),
                        blurRadius: 4,
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: Container(
                      width: 32,
                      height: 32,
                      color: Colors.grey[300],
                      child: Icon(
                        Icons.person,
                        color: Colors.grey[600],
                        size: 20,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(60),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.only(left: 16, right: 16, bottom: 12),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(5),
                  blurRadius: 4,
                  spreadRadius: 0,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Container(
              height: 48,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(30),
              ),
              child: TabBar(
                controller: _tabController,
                isScrollable: true,
                indicator: BoxDecoration(
                  borderRadius: BorderRadius.circular(30),
                  gradient: LinearGradient(
                    colors: gradients[0],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: gradients[0][0].withAlpha(50),
                      blurRadius: 4,
                      spreadRadius: 0,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                labelColor: Colors.white,
                unselectedLabelColor: Colors.grey[700],
                labelStyle: GoogleFonts.poppins(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
                unselectedLabelStyle: GoogleFonts.poppins(
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
                indicatorSize: TabBarIndicatorSize.tab,
                dividerColor: Colors.transparent,
                splashBorderRadius: BorderRadius.circular(30),
                padding: const EdgeInsets.symmetric(horizontal: 8),
                labelPadding: const EdgeInsets.symmetric(horizontal: 16),
                tabs: const [
                  Tab(text: 'Overview'),
                  Tab(text: 'Assets'),
                  Tab(text: 'Engagement'),
                  Tab(text: 'Monetization'),
                  Tab(text: 'AI Insights'),
                ],
              ),
            ),
          ),
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildAssetsTab(),
          _buildEngagementTab(),
          _buildMonetizationTab(),
          _buildAIInsightsTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showWithdrawModal(context);
        },
        backgroundColor: Colors.transparent,
        elevation: 8,
        child: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              colors: gradients[1],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            boxShadow: [
              BoxShadow(
                color: gradients[1][0].withAlpha(100),
                blurRadius: 12,
                spreadRadius: 0,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: const Icon(Icons.account_balance_wallet, color: Colors.white, size: 24),
        ),
      ),
    );
  }

  // Overview Tab - Shows earnings and key metrics
  Widget _buildOverviewTab() {
    // Sample data for badges
    final List<Map<String, dynamic>> badges = [
      {
        'name': 'Rising Star',
        'description': 'Reach 500 followers',
        'progress': 75,
        'icon': Icons.star_border_rounded,
        'color': gradients[3][0],
      },
      {
        'name': 'Content Creator',
        'description': 'Publish 50 posts',
        'progress': 90,
        'icon': Icons.create_rounded,
        'color': gradients[0][0],
      },
      {
        'name': 'Asset Master',
        'description': 'Upload 20 assets',
        'progress': 60,
        'icon': Icons.inventory_2_rounded,
        'color': gradients[2][0],
      },
      {
        'name': 'Mentor',
        'description': 'Complete 10 mentorship sessions',
        'progress': 40,
        'icon': Icons.school_rounded,
        'color': gradients[1][0],
      },
      {
        'name': 'Elite',
        'description': 'Be in top 5% of creators',
        'progress': 35,
        'icon': Icons.workspace_premium_rounded,
        'color': const Color(0xFFFFD700),
      },
      {
        'name': 'Innovator',
        'description': 'Create 5 trending assets',
        'progress': 80,
        'icon': Icons.lightbulb_outline_rounded,
        'color': const Color(0xFF00BFA5),
      },
      {
        'name': 'Community Leader',
        'description': 'Help 100 community members',
        'progress': 65,
        'icon': Icons.people_alt_rounded,
        'color': const Color(0xFF7E57C2),
      },
      {
        'name': 'Code Wizard',
        'description': 'Share 30 code snippets',
        'progress': 50,
        'icon': Icons.code_rounded,
        'color': const Color(0xFF42A5F5),
      },
    ];

    return ListView(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      children: [
        // Header
        Text(
          'Analytics Dashboard',
          style: GoogleFonts.poppins(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Track your earnings and performance',
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: Colors.black54,
          ),
        ),
        const SizedBox(height: 24),

        // Total Earnings Card
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [gradients[0][0], gradients[0][1]],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: gradients[0][0].withAlpha(40),
                blurRadius: 12,
                spreadRadius: 0,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Total Earnings',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.white.withOpacity(0.9),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      'This Month',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                '\$${earningsData['totalEarnings']}',
                style: GoogleFonts.poppins(
                  fontSize: 36,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '↑ 15% from last month',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.white.withOpacity(0.9),
                ),
              ),
              const SizedBox(height: 24),

              // Earnings Breakdown
              Text(
                'Earnings Breakdown',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.white.withOpacity(0.9),
                ),
              ),
              const SizedBox(height: 16),

              // Earnings Source Bars
              ...earningsData['sources'].map<Widget>((source) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            source['name'],
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              color: Colors.white,
                            ),
                          ),
                          Text(
                            '\$${source['amount']}',
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Stack(
                        children: [
                          Container(
                            height: 8,
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                          Container(
                            height: 8,
                            width: MediaQuery.of(context).size.width * (source['percentage'] / 100) * 0.8,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              }).toList(),
            ],
          ),
        ),

        const SizedBox(height: 24),

        // Recent Transactions
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(10),
                blurRadius: 4,
                spreadRadius: 0,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Recent Transactions',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 16),

              // Transaction List
              ...earningsData['recentTransactions'].map<Widget>((transaction) {
                final bool isCredit = transaction['type'] == 'credit';

                return Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: isCredit ? gradients[2][0].withOpacity(0.1) : gradients[1][0].withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          isCredit ? Icons.arrow_downward : Icons.arrow_upward,
                          color: isCredit ? gradients[2][0] : gradients[1][0],
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              transaction['description'],
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Colors.black87,
                              ),
                            ),
                            Text(
                              transaction['date'],
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                color: Colors.black54,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Text(
                        '${isCredit ? '+' : '-'}\$${transaction['amount']}',
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: isCredit ? gradients[2][0] : gradients[1][0],
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),

              // View All Button
              Center(
                child: TextButton(
                  onPressed: () {},
                  child: Text(
                    'View All Transactions',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: gradients[0][0],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 24),

        // AI Insights Preview
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFF6A11CB), Color(0xFF2575FC)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF6A11CB).withAlpha(40),
                blurRadius: 12,
                spreadRadius: 0,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withAlpha(50),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.auto_awesome,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'AI Insights',
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.red.withAlpha(100),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.warning_amber,
                          color: Colors.white,
                          size: 14,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '2 Alerts',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                'Our AI has detected potential issues and growth opportunities for your account',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.white.withAlpha(230),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(30),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(
                                Icons.trending_up,
                                color: Colors.white,
                                size: 16,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'Growth Tips',
                                  style: GoogleFonts.poppins(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '3 suggestions to grow your account',
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              color: Colors.white.withAlpha(200),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(30),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(
                                Icons.security,
                                color: Colors.white,
                                size: 16,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'Fraud Detection',
                                  style: GoogleFonts.poppins(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '2 potential issues detected',
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              color: Colors.white.withAlpha(200),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    _tabController.animateTo(4); // Navigate to AI Insights tab
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: const Color(0xFF6A11CB),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                  child: Text(
                    'View All Insights',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 24),

        // Badge Progress Section
        Text(
          'Badge Progress',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),

        // Badge Cards
        Wrap(
          spacing: 16,
          runSpacing: 16,
          children: badges.map((badge) {
            return SizedBox(
              width: (MediaQuery.of(context).size.width - 48) / 2,
              child: _buildBadgeCard(
                name: badge['name'],
                description: badge['description'],
                progress: badge['progress'],
                icon: badge['icon'],
                color: badge['color'],
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  // Helper method to build badge cards
  Widget _buildBadgeCard({
    required String name,
    required String description,
    required int progress,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
              ),
              const Spacer(),
              Text(
                '$progress%',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            name,
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: Colors.black54,
            ),
          ),
          const SizedBox(height: 12),
          // Progress bar
          Container(
            height: 6,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(3),
            ),
            child: LayoutBuilder(
              builder: (context, constraints) {
                // Calculate width based on available space
                final maxWidth = constraints.maxWidth;
                final progressWidth = maxWidth * (progress / 100);

                return Row(
                  children: [
                    Container(
                      height: 6,
                      width: progressWidth,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [color.withAlpha(178), color], // 0.7 opacity as alpha
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                        borderRadius: BorderRadius.circular(3),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // Assets Tab - Shows asset performance metrics
  Widget _buildAssetsTab() {
    // Sample data for assets
    final List<Map<String, dynamic>> assets = [
      {
        'name': 'Modern UI Kit',
        'type': 'UI Kit',
        'downloads': 1250,
        'rating': 4.8,
        'earnings': 850.50,
        'image': 'https://picsum.photos/id/1/200/200',
      },
      {
        'name': 'E-commerce App Template',
        'type': 'App Template',
        'downloads': 780,
        'rating': 4.6,
        'earnings': 620.75,
        'image': 'https://picsum.photos/id/20/200/200',
      },
      {
        'name': 'Dashboard Components',
        'type': 'UI Components',
        'downloads': 950,
        'rating': 4.9,
        'earnings': 480.25,
        'image': 'https://picsum.photos/id/42/200/200',
      },
    ];

    // Monthly download data for chart
    final List<Map<String, dynamic>> monthlyDownloads = [
      {'month': 'Jan', 'downloads': 450},
      {'month': 'Feb', 'downloads': 520},
      {'month': 'Mar', 'downloads': 680},
      {'month': 'Apr', 'downloads': 750},
      {'month': 'May', 'downloads': 820},
      {'month': 'Jun', 'downloads': 980},
    ];

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // Header
        Text(
          'Asset Performance',
          style: GoogleFonts.poppins(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Track how your assets are performing',
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: Colors.black54,
          ),
        ),
        const SizedBox(height: 24),

        // Asset Stats Cards
        Row(
          children: [
            // Total Downloads Card
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(10),
                      blurRadius: 4,
                      spreadRadius: 0,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: gradients[0][0].withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.download_rounded,
                        color: gradients[0][0],
                        size: 20,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'Total Downloads',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Colors.black54,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '2,980',
                      style: GoogleFonts.poppins(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '↑ 12% this month',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: gradients[2][0],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 16),
            // Average Rating Card
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(10),
                      blurRadius: 4,
                      spreadRadius: 0,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: gradients[3][0].withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.star_rounded,
                        color: gradients[3][0],
                        size: 20,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'Average Rating',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Colors.black54,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '4.7',
                      style: GoogleFonts.poppins(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '↑ 0.2 from last month',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: gradients[2][0],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 24),

        // Downloads Chart Card
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(10),
                blurRadius: 4,
                spreadRadius: 0,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Downloads Over Time',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Last 6 months',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: Colors.black54,
                ),
              ),
              const SizedBox(height: 20),

              // Simple Chart Representation (placeholder for actual chart)
              SizedBox(
                height: 200,
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: monthlyDownloads.map((data) {
                      // Calculate relative height based on downloads
                      const double maxDownloads = 1000;
                      final double percentage = (data['downloads'] as int) / maxDownloads;
                      final double height = 160 * percentage; // Reduced height to prevent overflow

                      return Container(
                        margin: const EdgeInsets.symmetric(horizontal: 10),
                        child: Column(
                          mainAxisSize: MainAxisSize.min, // Use min size to prevent overflow
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Container(
                              width: 30,
                              height: height,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [gradients[0][0], gradients[0][1]],
                                  begin: Alignment.bottomCenter,
                                  end: Alignment.topCenter,
                                ),
                                borderRadius: BorderRadius.circular(6),
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              data['month'],
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                color: Colors.black54,
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 24),

        // Top Performing Assets
        Text(
          'Top Performing Assets',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),

        // Asset Cards
        ...assets.map((asset) {
          return Container(
            margin: const EdgeInsets.only(bottom: 16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(10),
                  blurRadius: 4,
                  spreadRadius: 0,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // Asset Image
                ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Container(
                    width: 80,
                    height: 80,
                    color: Colors.grey[200],
                    child: Icon(
                      Icons.image,
                      color: Colors.grey[400],
                      size: 32,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                // Asset Details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: gradients[0][0].withOpacity(0.1),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              asset['type'],
                              style: GoogleFonts.poppins(
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                                color: gradients[0][0],
                              ),
                            ),
                          ),
                          const Spacer(),
                          const Icon(
                            Icons.star,
                            color: Colors.amber,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${asset['rating']}',
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        asset['name'],
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(
                            Icons.download_rounded,
                            color: Colors.grey[600],
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${asset['downloads']} downloads',
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(width: 16),
                          Icon(
                            Icons.attach_money_rounded,
                            color: gradients[2][0],
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '\$${asset['earnings']}',
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: gradients[2][0],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }).toList(),

        // View All Button
        Center(
          child: TextButton(
            onPressed: () {},
            child: Text(
              'View All Assets',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: gradients[0][0],
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Engagement Tab - Shows account engagement metrics
  Widget _buildEngagementTab() {
    // Sample data for engagement metrics
    final Map<String, dynamic> engagementData = {
      'followers': 1250,
      'followersGrowth': 15,
      'profileViews': 3450,
      'profileViewsGrowth': 22,
      'postEngagement': 78,
      'postEngagementGrowth': 8,
      'mentorshipRequests': 24,
      'mentorshipRequestsGrowth': 30,
      'weeklyEngagement': [
        {'day': 'Mon', 'views': 420, 'interactions': 85},
        {'day': 'Tue', 'views': 380, 'interactions': 72},
        {'day': 'Wed', 'views': 510, 'interactions': 94},
        {'day': 'Thu', 'views': 480, 'interactions': 88},
        {'day': 'Fri', 'views': 640, 'interactions': 120},
        {'day': 'Sat', 'views': 580, 'interactions': 105},
        {'day': 'Sun', 'views': 440, 'interactions': 90},
      ],
      'topPosts': [
        {
          'title': 'Modern UI Design Principles',
          'type': 'Article',
          'views': 1250,
          'likes': 320,
          'comments': 45,
          'date': '2 days ago',
        },
        {
          'title': 'Flutter Animation Tutorial',
          'type': 'Tutorial',
          'views': 980,
          'likes': 245,
          'comments': 38,
          'date': '1 week ago',
        },
        {
          'title': 'State Management Comparison',
          'type': 'Poll',
          'views': 1450,
          'likes': 380,
          'comments': 72,
          'date': '2 weeks ago',
        },
      ],
    };

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // Header
        Text(
          'Account Engagement',
          style: GoogleFonts.poppins(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Track your account performance and engagement',
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: Colors.black54,
          ),
        ),
        const SizedBox(height: 24),

        // Engagement Metrics Wrap
        Wrap(
          spacing: 16,
          runSpacing: 16,
          children: [
            // Followers Card
            SizedBox(
              width: (MediaQuery.of(context).size.width - 48) / 2,
              child: _buildEngagementMetricCard(
                icon: Icons.people_alt_rounded,
                iconColor: gradients[0][0],
                title: 'Followers',
                value: engagementData['followers'].toString(),
                growth: engagementData['followersGrowth'],
              ),
            ),

            // Profile Views Card
            SizedBox(
              width: (MediaQuery.of(context).size.width - 48) / 2,
              child: _buildEngagementMetricCard(
                icon: Icons.visibility_rounded,
                iconColor: gradients[1][0],
                title: 'Profile Views',
                value: engagementData['profileViews'].toString(),
                growth: engagementData['profileViewsGrowth'],
              ),
            ),

            // Post Engagement Card
            SizedBox(
              width: (MediaQuery.of(context).size.width - 48) / 2,
              child: _buildEngagementMetricCard(
                icon: Icons.thumb_up_alt_rounded,
                iconColor: gradients[2][0],
                title: 'Post Engagement',
                value: '${engagementData['postEngagement']}%',
                growth: engagementData['postEngagementGrowth'],
              ),
            ),

            // Mentorship Requests Card
            SizedBox(
              width: (MediaQuery.of(context).size.width - 48) / 2,
              child: _buildEngagementMetricCard(
                icon: Icons.school_rounded,
                iconColor: gradients[3][0],
                title: 'Mentorship Requests',
                value: engagementData['mentorshipRequests'].toString(),
                growth: engagementData['mentorshipRequestsGrowth'],
              ),
            ),
          ],
        ),

        const SizedBox(height: 24),

        // Weekly Engagement Chart
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(10),
                blurRadius: 4,
                spreadRadius: 0,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Weekly Engagement',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Profile views and interactions',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: Colors.black54,
                ),
              ),
              const SizedBox(height: 20),

              // Simple Chart Representation (placeholder for actual chart)
              SizedBox(
                height: 200,
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: engagementData['weeklyEngagement'].map<Widget>((day) {
                      // Calculate relative heights based on views and interactions
                      const double maxViews = 700;
                      const double maxInteractions = 150;
                      final double viewsPercentage = (day['views'] as int) / maxViews;
                      final double interactionsPercentage = (day['interactions'] as int) / maxInteractions;
                      final double viewsHeight = 160 * viewsPercentage; // Reduced height to prevent overflow
                      final double interactionsHeight = 160 * interactionsPercentage; // Reduced height to prevent overflow

                      return Container(
                        margin: const EdgeInsets.symmetric(horizontal: 10),
                        child: Column(
                          mainAxisSize: MainAxisSize.min, // Use min size to prevent overflow
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            // Stacked bars
                            SizedBox(
                              width: 30,
                              child: Stack(
                                alignment: Alignment.bottomCenter,
                                children: [
                                  // Views bar (background)
                                  Container(
                                    width: 30,
                                    height: viewsHeight,
                                    decoration: BoxDecoration(
                                      color: gradients[0][0].withOpacity(0.2),
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                  ),
                                  // Interactions bar (foreground)
                                  Container(
                                    width: 20,
                                    height: interactionsHeight,
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: [gradients[0][0], gradients[0][1]],
                                        begin: Alignment.bottomCenter,
                                        end: Alignment.topCenter,
                                      ),
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              day['day'],
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                color: Colors.black54,
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Legend
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(
                          color: gradients[0][0].withOpacity(0.2),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Profile Views',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: Colors.black54,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(width: 24),
                  Row(
                    children: [
                      Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [gradients[0][0], gradients[0][1]],
                            begin: Alignment.bottomCenter,
                            end: Alignment.topCenter,
                          ),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Interactions',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: Colors.black54,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),

        const SizedBox(height: 24),

        // Top Performing Posts
        Text(
          'Top Performing Posts',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),

        // Post Cards
        ...engagementData['topPosts'].map<Widget>((post) {
          return Container(
            margin: const EdgeInsets.only(bottom: 16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(10),
                  blurRadius: 4,
                  spreadRadius: 0,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: _getPostTypeColor(post['type']).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        post['type'],
                        style: GoogleFonts.poppins(
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                          color: _getPostTypeColor(post['type']),
                        ),
                      ),
                    ),
                    const Spacer(),
                    Text(
                      post['date'],
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.black54,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  post['title'],
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildPostStatItem(
                      icon: Icons.visibility_outlined,
                      value: post['views'].toString(),
                      label: 'Views',
                    ),
                    _buildPostStatItem(
                      icon: Icons.favorite_outline,
                      value: post['likes'].toString(),
                      label: 'Likes',
                    ),
                    _buildPostStatItem(
                      icon: Icons.chat_bubble_outline,
                      value: post['comments'].toString(),
                      label: 'Comments',
                    ),
                  ],
                ),
              ],
            ),
          );
        }).toList(),

        // View All Button
        Center(
          child: TextButton(
            onPressed: () {},
            child: Text(
              'View All Posts',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: gradients[0][0],
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Helper method to build engagement metric cards
  Widget _buildEngagementMetricCard({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String value,
    required int growth,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 4,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: iconColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: iconColor,
              size: 20,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.black54,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            growth > 0 ? '↑ $growth% this month' : '↓ ${growth.abs()}% this month',
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: growth > 0 ? const Color(0xFF00C9FF) : const Color(0xFFFF416C),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build post stat items
  Widget _buildPostStatItem({
    required IconData icon,
    required String value,
    required String label,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          color: Colors.grey[600],
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: GoogleFonts.poppins(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 12,
            color: Colors.black54,
          ),
        ),
      ],
    );
  }

  // Helper method to get color based on post type
  Color _getPostTypeColor(String type) {
    switch (type) {
      case 'Article':
        return gradients[0][0];
      case 'Tutorial':
        return gradients[2][0];
      case 'Poll':
        return gradients[1][0];
      default:
        return gradients[3][0];
    }
  }

  // Monetization Tab - Shows monetization status and progress
  Widget _buildMonetizationTab() {
    // Sample data for monetization
    final Map<String, dynamic> monetizationData = {
      'isMonetized': false, // Set to true to show monetized state
      'currentFollowers': 750,
      'requiredFollowers': 1000,
      'followersPercentage': 75,
      'currentRating': 4.6,
      'requiredRating': 4.5,
      'ratingPercentage': 100,
      'currentAssetDownloads': 850,
      'requiredAssetDownloads': 1000,
      'downloadsPercentage': 85,
      'currentPostEngagement': 65,
      'requiredPostEngagement': 70,
      'engagementPercentage': 93,
      'estimatedEarnings': {
        'assets': 1250.50,
        'mentorship': 750.25,
        'contests': 450.00,
      },
      'paymentMethods': [
        {
          'type': 'Bank Account',
          'name': 'Chase Bank ****1234',
          'isDefault': true,
        },
        {
          'type': 'PayPal',
          'name': '<EMAIL>',
          'isDefault': false,
        },
      ],
    };

    // If user is monetized, show monetization stats
    if (monetizationData['isMonetized']) {
      return _buildMonetizedView(monetizationData);
    }

    // Otherwise show progress towards monetization
    return _buildMonetizationProgressView(monetizationData);
  }

  // View for users who are not yet monetized
  Widget _buildMonetizationProgressView(Map<String, dynamic> data) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // Header
        Text(
          'Monetization Progress',
          style: GoogleFonts.poppins(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Complete these requirements to monetize your account',
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: Colors.black54,
          ),
        ),
        const SizedBox(height: 24),

        // Overall Progress Card
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [gradients[0][0], gradients[0][1]],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: gradients[0][0].withOpacity(0.4),
                blurRadius: 12,
                spreadRadius: 0,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Overall Progress',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '88%',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Progress bar
              Container(
                height: 8,
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  children: [
                    Container(
                      height: 8,
                      width: MediaQuery.of(context).size.width * 0.88 * 0.8, // 88% of available width
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20),
              Text(
                'You\'re almost there! Complete the remaining requirements to start earning.',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),

        const SizedBox(height: 24),

        // Requirements Cards
        Text(
          'Monetization Requirements',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),

        // Followers Requirement
        _buildRequirementCard(
          icon: Icons.people_alt_rounded,
          iconColor: gradients[0][0],
          title: 'Followers',
          current: data['currentFollowers'],
          required: data['requiredFollowers'],
          percentage: data['followersPercentage'],
          isComplete: data['followersPercentage'] >= 100,
        ),

        const SizedBox(height: 16),

        // Rating Requirement
        _buildRequirementCard(
          icon: Icons.star_rounded,
          iconColor: gradients[3][0],
          title: 'Average Rating',
          current: data['currentRating'],
          required: data['requiredRating'],
          percentage: data['ratingPercentage'],
          isComplete: data['ratingPercentage'] >= 100,
          showDecimal: true,
        ),

        const SizedBox(height: 16),

        // Downloads Requirement
        _buildRequirementCard(
          icon: Icons.download_rounded,
          iconColor: gradients[2][0],
          title: 'Asset Downloads',
          current: data['currentAssetDownloads'],
          required: data['requiredAssetDownloads'],
          percentage: data['downloadsPercentage'],
          isComplete: data['downloadsPercentage'] >= 100,
        ),

        const SizedBox(height: 16),

        // Engagement Requirement
        _buildRequirementCard(
          icon: Icons.thumb_up_alt_rounded,
          iconColor: gradients[1][0],
          title: 'Post Engagement',
          current: data['currentPostEngagement'],
          required: data['requiredPostEngagement'],
          percentage: data['engagementPercentage'],
          isComplete: data['engagementPercentage'] >= 100,
          suffix: '%',
        ),

        const SizedBox(height: 24),

        // Estimated Earnings Card
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                spreadRadius: 0,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Estimated Monthly Earnings',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Based on your current activity',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: Colors.black54,
                ),
              ),
              const SizedBox(height: 20),

              // Pie chart representation
              SizedBox(
                height: 180,
                child: Row(
                  children: [
                    // Simple pie chart visualization
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          colors: [gradients[0][0], gradients[0][1]],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: gradients[0][0].withOpacity(0.2),
                            blurRadius: 8,
                            spreadRadius: 0,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Center(
                        child: Container(
                          width: 80,
                          height: 80,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.white,
                          ),
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  '\$2,450',
                                  style: GoogleFonts.poppins(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87,
                                  ),
                                ),
                                Text(
                                  'per month',
                                  style: GoogleFonts.poppins(
                                    fontSize: 10,
                                    color: Colors.black54,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 24),
                    // Legend
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          _buildEarningsLegendItem(
                            color: gradients[0][0],
                            label: 'Assets',
                            amount: data['estimatedEarnings']['assets'],
                            percentage: 51,
                          ),
                          const SizedBox(height: 16),
                          _buildEarningsLegendItem(
                            color: gradients[1][0],
                            label: 'Mentorship',
                            amount: data['estimatedEarnings']['mentorship'],
                            percentage: 31,
                          ),
                          const SizedBox(height: 16),
                          _buildEarningsLegendItem(
                            color: gradients[2][0],
                            label: 'Contests',
                            amount: data['estimatedEarnings']['contests'],
                            percentage: 18,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 24),

        // Payment Methods
        Text(
          'Payment Methods',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),

        // Payment Method Cards
        ...data['paymentMethods'].map<Widget>((method) {
          return Container(
            margin: const EdgeInsets.only(bottom: 16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: method['isDefault'] ? gradients[0][0] : Colors.grey[200]!,
                width: method['isDefault'] ? 2 : 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  spreadRadius: 0,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: method['type'] == 'Bank Account'
                        ? gradients[0][0].withOpacity(0.1)
                        : gradients[1][0].withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    method['type'] == 'Bank Account'
                        ? Icons.account_balance
                        : Icons.payment,
                    color: method['type'] == 'Bank Account'
                        ? gradients[0][0]
                        : gradients[1][0],
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        method['type'],
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                      ),
                      Text(
                        method['name'],
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: Colors.black54,
                        ),
                      ),
                    ],
                  ),
                ),
                if (method['isDefault'])
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: gradients[0][0].withOpacity(0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      'Default',
                      style: GoogleFonts.poppins(
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        color: gradients[0][0],
                      ),
                    ),
                  ),
              ],
            ),
          );
        }).toList(),

        // Add Payment Method Button
        Center(
          child: TextButton.icon(
            onPressed: () {},
            icon: Icon(
              Icons.add_circle_outline,
              color: gradients[0][0],
              size: 18,
            ),
            label: Text(
              'Add Payment Method',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: gradients[0][0],
              ),
            ),
          ),
        ),
      ],
    );
  }

  // View for users who are already monetized
  Widget _buildMonetizedView(Map<String, dynamic> data) {
    return Center(
      child: Text(
        'Monetized View - Coming in Part 5',
        style: GoogleFonts.poppins(
          fontSize: 16,
          color: Colors.black54,
        ),
      ),
    );
  }

  // Helper method to build requirement cards
  Widget _buildRequirementCard({
    required IconData icon,
    required Color iconColor,
    required String title,
    required dynamic current,
    required dynamic required,
    required int percentage,
    required bool isComplete,
    bool showDecimal = false,
    String suffix = '',
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: iconColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: iconColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: isComplete
                      ? const Color(0xFF00C9FF).withOpacity(0.1)
                      : Colors.grey[200],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  isComplete ? 'Complete' : '$percentage%',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: isComplete ? const Color(0xFF00C9FF) : Colors.black54,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Progress bar
          Container(
            height: 8,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              children: [
                Container(
                  height: 8,
                  width: MediaQuery.of(context).size.width * (percentage / 100) * 0.8,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: isComplete
                          ? [const Color(0xFF00C9FF), const Color(0xFF92FE9D)]
                          : [iconColor.withOpacity(0.7), iconColor],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Current: ${showDecimal ? current.toStringAsFixed(1) : current}$suffix',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.black87,
                ),
              ),
              Text(
                'Required: ${showDecimal ? required.toStringAsFixed(1) : required}$suffix',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.black54,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Helper method to build earnings legend items
  Widget _buildEarningsLegendItem({
    required Color color,
    required String label,
    required double amount,
    required int percentage,
  }) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
              Text(
                '\$${amount.toStringAsFixed(2)}',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ),
        Text(
          '$percentage%',
          style: GoogleFonts.poppins(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
      ],
    );
  }

  // Withdraw Modal Content
  Widget _buildWithdrawModalContent() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Withdraw Funds',
            style: GoogleFonts.poppins(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 24),

          // Available Balance
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [gradients[0][0], gradients[0][1]],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.account_balance_wallet,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Available Balance',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                    Text(
                      '\$1,950.75',
                      style: GoogleFonts.poppins(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Withdraw Form
          Text(
            'Withdraw Amount',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.grey[300]!,
                width: 1,
              ),
            ),
            child: TextField(
              keyboardType: TextInputType.number,
              style: GoogleFonts.poppins(
                fontSize: 16,
                color: Colors.black87,
              ),
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: 'Enter amount',
                hintStyle: GoogleFonts.poppins(
                  color: Colors.grey[400],
                ),
                prefixIcon: Icon(
                  Icons.attach_money,
                  color: Colors.grey[600],
                ),
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Payment Method Selection
          Text(
            'Select Payment Method',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: gradients[0][0],
                width: 2,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: gradients[0][0].withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.account_balance,
                    color: gradients[0][0],
                    size: 20,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Bank Account',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                      ),
                      Text(
                        'Chase Bank ****1234',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: Colors.black54,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: gradients[0][0],
                  ),
                  child: const Icon(
                    Icons.check,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ],
            ),
          ),

          const Spacer(),

          // Withdraw Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {},
              style: ElevatedButton.styleFrom(
                backgroundColor: gradients[0][0],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                'Withdraw Funds',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Withdraw Modal
  void _showWithdrawModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(20),
              blurRadius: 10,
              spreadRadius: 0,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Column(
          children: [
            Container(
              margin: const EdgeInsets.only(top: 10),
              width: 40,
              height: 5,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            Expanded(
              child: _buildWithdrawModalContent(),
            ),
          ],
        ),
      ),
    );
  }

  // AI Insights Tab - Shows AI-powered suggestions and fraud detection
  Widget _buildAIInsightsTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // Header
        Text(
          'AI Insights',
          style: GoogleFonts.poppins(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'AI-powered suggestions to help you grow and protect your account',
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: Colors.black54,
          ),
        ),
        const SizedBox(height: 24),

        // Fraud Detection Section
        if (aiInsightsData['fraudDetection'].isNotEmpty) ...[
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFFFF416C), Color(0xFFFF4B2B)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFFFF416C).withAlpha(40),
                  blurRadius: 12,
                  spreadRadius: 0,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(50),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.security,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Fraud Detection Alerts',
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  'Our AI has detected potential suspicious activity on your account',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.white.withAlpha(230),
                  ),
                ),
                const SizedBox(height: 20),

                // Fraud Detection Cards
                ...aiInsightsData['fraudDetection'].map<Widget>((alert) {
                  return Container(
                    margin: const EdgeInsets.only(bottom: 16),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white.withAlpha(30),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              alert['icon'],
                              color: Colors.white,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                alert['title'],
                                style: GoogleFonts.poppins(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: alert['severity'] == 'High'
                                    ? Colors.red.withAlpha(100)
                                    : Colors.orange.withAlpha(100),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                alert['severity'],
                                style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Text(
                          alert['description'],
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            color: Colors.white.withAlpha(230),
                          ),
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              alert['timestamp'],
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                color: Colors.white.withAlpha(180),
                              ),
                            ),
                            if (alert['actionRequired'])
                              ElevatedButton(
                                onPressed: () {},
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.white,
                                  foregroundColor: const Color(0xFFFF416C),
                                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  elevation: 0,
                                ),
                                child: Text(
                                  'Review',
                                  style: GoogleFonts.poppins(
                                    fontWeight: FontWeight.w600,
                                    fontSize: 12,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ],
            ),
          ),
          const SizedBox(height: 24),
        ],

        // Growth Suggestions Section
        Text(
          'Growth Suggestions',
          style: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),

        // Growth Suggestion Cards
        ...aiInsightsData['growthSuggestions'].map<Widget>((suggestion) {
          return _buildAISuggestionCard(
            title: suggestion['title'],
            description: suggestion['description'],
            impact: suggestion['impact'],
            impactValue: suggestion['impactValue'],
            category: suggestion['category'],
            icon: suggestion['icon'],
            color: suggestion['color'],
          );
        }).toList(),

        const SizedBox(height: 24),

        // Optimization Tips Section
        Text(
          'Optimization Tips',
          style: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 16),

        // Optimization Tip Cards
        ...aiInsightsData['optimizationTips'].map<Widget>((tip) {
          return _buildAISuggestionCard(
            title: tip['title'],
            description: tip['description'],
            impact: tip['impact'],
            impactValue: tip['impactValue'],
            category: tip['category'],
            icon: tip['icon'],
            color: tip['color'],
          );
        }).toList(),

        const SizedBox(height: 24),

        // AI Insights Info Card
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(10),
                blurRadius: 4,
                spreadRadius: 0,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: gradients[0][0].withAlpha(25),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.info_outline,
                      color: gradients[0][0],
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'About AI Insights',
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                'Our AI analyzes your account activity, content performance, and market trends to provide personalized suggestions for growth and fraud detection.',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.black54,
                  height: 1.5,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                'Suggestions are updated daily based on your latest activity and performance metrics.',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.black54,
                  height: 1.5,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Helper method to build AI suggestion cards
  Widget _buildAISuggestionCard({
    required String title,
    required String description,
    required String impact,
    required int impactValue,
    required String category,
    required IconData icon,
    required Color color,
  }) {
    // Determine impact color
    Color impactColor;
    if (impact == 'High') {
      impactColor = const Color(0xFF4CAF50);
    } else if (impact == 'Medium') {
      impactColor = const Color(0xFFFFA000);
    } else {
      impactColor = const Color(0xFF2196F3);
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 4,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withAlpha(25),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: color.withAlpha(25),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            category,
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: color,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: impactColor.withAlpha(25),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.trending_up,
                                color: impactColor,
                                size: 12,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                impact == 'High' && impactValue >= 100
                                    ? '${impactValue ~/ 100}x Impact'
                                    : '+$impactValue% Impact',
                                style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                  color: impactColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            description,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.black54,
              height: 1.5,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () {},
                style: TextButton.styleFrom(
                  foregroundColor: color,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                ),
                child: Text(
                  'Dismiss',
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(
                  backgroundColor: color,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'Apply Suggestion',
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}