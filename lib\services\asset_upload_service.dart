import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'user_content_service.dart';

/// Service for handling asset uploads with compression
class AssetUploadService {
  final UserContentService _userContentService = UserContentService();

  /// Upload an asset file to Firebase Storage and add its metadata to Firestore
  /// Uses the existing UserContentService for the actual upload
  Future<Map<String, dynamic>> uploadAsset({
    required File assetFile,
    required File thumbnailFile,
    required List<File> screenshotFiles,
    required String title,
    required String description,
    required String type,
    required String category,
    required List<String> subcategories,
    required List<String> tags,
    required double price,
    bool requiresAdminApproval = true,
  }) async {
    try {
      // Use the existing UserContentService to upload the asset
      final result = await _userContentService.uploadAsset(
        title: title,
        description: description,
        type: type,
        category: category,
        subcategories: subcategories,
        tags: tags,
        price: price,
        assetFile: assetFile,
        thumbnailFile: thumbnailFile, // Pass the thumbnail file
        screenshots: screenshotFiles,
        requiresAdminApproval: requiresAdminApproval, // Send to admin panel for approval
      );

      if (result['success'] == true) {
        debugPrint('Asset uploaded successfully with ID: ${result['assetId']}');

        // The UserContentService already updates the user's asset count
        // and adds the asset to the user's profile

        return {
          'success': true,
          'assetId': result['assetId'],
          'assetData': result['assetData'],
        };
      } else {
        return {
          'success': false,
          'error': result['error'] ?? 'Unknown error occurred',
        };
      }
    } catch (e) {
      debugPrint('Error uploading asset: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Get content type based on file extension
  String getContentType(String fileName) {
    final String ext = path.extension(fileName).toLowerCase();

    switch (ext) {
      case '.zip':
        return 'application/zip';
      case '.rar':
        return 'application/x-rar-compressed';
      case '.pdf':
        return 'application/pdf';
      case '.doc':
      case '.docx':
        return 'application/msword';
      case '.xls':
      case '.xlsx':
        return 'application/vnd.ms-excel';
      case '.ppt':
      case '.pptx':
        return 'application/vnd.ms-powerpoint';
      case '.jpg':
      case '.jpeg':
        return 'image/jpeg';
      case '.png':
        return 'image/png';
      case '.gif':
        return 'image/gif';
      case '.svg':
        return 'image/svg+xml';
      case '.mp3':
        return 'audio/mpeg';
      case '.mp4':
        return 'video/mp4';
      default:
        return 'application/octet-stream';
    }
  }
}
