import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:provider/provider.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:convert';
import 'dart:async';
import 'providers/user_provider.dart';

class ProfileSetupPage extends StatefulWidget {
  const ProfileSetupPage({Key? key}) : super(key: key);

  @override
  State<ProfileSetupPage> createState() => _ProfileSetupPageState();
}

class _ProfileSetupPageState extends State<ProfileSetupPage> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  final int _totalPages = 4;
  bool _isLoading = false;

  // Form keys for validation
  final _basicInfoFormKey = GlobalKey<FormState>();
  final _usernameFormKey = GlobalKey<FormState>();
  final _interestsFormKey = GlobalKey<FormState>();
  final _skillsFormKey = GlobalKey<FormState>();

  // Controllers
  final _fullNameController = TextEditingController();
  final _usernameController = TextEditingController();
  final _bioController = TextEditingController();
  final _locationController = TextEditingController();

  // Selected interests and skills
  final List<String> _selectedInterests = [];
  final List<String> _selectedSkills = [];
  final Map<String, double> _skillLevels = {};

  // Available interests and skills
  final List<String> _availableInterests = [
    'Android Development',
    'iOS Development',
    'Web Development',
    'Flutter',
    'React Native',
    'UI/UX Design',
    'Game Development',
    'Machine Learning',
    'Blockchain',
    'DevOps',
    'Cloud Computing',
    'Cybersecurity',
    'Data Science',
    'IoT',
    'AR/VR',
  ];

  final List<String> _availableSkills = [
    'Java',
    'Kotlin',
    'Swift',
    'Dart',
    'JavaScript',
    'TypeScript',
    'Python',
    'C#',
    'C++',
    'HTML/CSS',
    'React',
    'Angular',
    'Vue.js',
    'Node.js',
    'Firebase',
    'AWS',
    'Docker',
    'Kubernetes',
    'Git',
    'SQL',
    'NoSQL',
    'GraphQL',
    'REST API',
  ];

  // Username validation status
  bool _isCheckingUsername = false;
  bool _isUsernameAvailable = true;
  String _usernameError = '';
  Timer? _debounceTimer;

  @override
  void dispose() {
    _pageController.dispose();
    _fullNameController.dispose();
    _usernameController.dispose();
    _bioController.dispose();
    _locationController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  // Check if username is available
  Future<bool> _checkUsernameAvailability(String username) async {
    // Don't check if username is too short
    if (username.length < 4) {
      setState(() {
        _isUsernameAvailable = false;
        _usernameError = 'Username must be at least 4 characters';
        _isCheckingUsername = false;
      });
      return false;
    }

    // Check if username contains only allowed characters
    if (!RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(username)) {
      setState(() {
        _isUsernameAvailable = false;
        _usernameError = 'Username can only contain letters, numbers, and underscores';
        _isCheckingUsername = false;
      });
      return false;
    }

    setState(() {
      _isCheckingUsername = true;
    });

    try {
      // Check with Firebase
      if (!mounted) return false;
      final userProvider = Provider.of<UserProvider>(context, listen: false);

      // Get current user ID to exclude from the check
      final currentUserId = FirebaseAuth.instance.currentUser?.uid;

      // Add a debounce to avoid too many requests
      await Future.delayed(const Duration(milliseconds: 500));

      if (!mounted) return false;

      final isAvailable = await userProvider.isUsernameAvailable(username, currentUserId);

      if (!mounted) return false;
      setState(() {
        _isCheckingUsername = false;
        _isUsernameAvailable = isAvailable;
        _usernameError = _isUsernameAvailable ? '' : 'Username is already taken';
      });

      return _isUsernameAvailable;
    } catch (e) {
      debugPrint('Error checking username availability: $e');
      // Fallback to local validation if Firebase check fails
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);

      if (!mounted) return false;
      setState(() {
        _isCheckingUsername = false;
        _isUsernameAvailable = false; // Safer to assume not available if check fails
        _usernameError = 'Error checking username. Please try again.';
      });

      return false;
    }
  }

  // Save profile data
  Future<void> _saveProfileData() async {
    final prefs = await SharedPreferences.getInstance();

    final profileData = {
      'name': _fullNameController.text,
      'username': _usernameController.text,
      'bio': _bioController.text,
      'location': _locationController.text,
      'interests': _selectedInterests,
      'skills': _selectedSkills.map((skill) => {
        'name': skill,
        'level': _skillLevels[skill] ?? 0.5,
      }).toList(),
      'profileSetupCompleted': true,
    };

    await prefs.setString('profile_data', jsonEncode(profileData));
  }

  // Navigate to next page
  void _nextPage() {
    if (_currentPage < _totalPages - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeSetup();
    }
  }

  // Navigate to previous page
  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  // Complete setup and navigate to home
  Future<void> _completeSetup() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Save to local storage
      await _saveProfileData();

      // Save to Firebase
      if (!mounted) return;
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      await userProvider.updateUserProfile(
        displayName: _fullNameController.text,
        username: _usernameController.text,
        bio: _bioController.text,
        location: _locationController.text,
        interests: _selectedInterests,
        skills: _selectedSkills.map((skill) => {
          'name': skill,
          'level': _skillLevels[skill] ?? 0.5,
        }).toList(),
      );

      // Save profile setup status to both SharedPreferences and Firebase
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('profile_setup_completed', true);

      // Also update the user document directly to ensure the flag is set
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser != null) {
        await FirebaseFirestore.instance.collection('users').doc(currentUser.uid).update({
          'profileSetupCompleted': true,
          'updatedAt': FieldValue.serverTimestamp(),
        });
      }

      setState(() {
        _isLoading = false;
      });

      // Navigate to home page
      if (!mounted) return;
      Navigator.of(context).pushReplacementNamed('/home');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      // Show error message
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error saving profile: $e'),
          backgroundColor: Colors.red,
        ),
      );

      // Log error to Crashlytics
      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: Text(
          'Complete Your Profile',
          style: GoogleFonts.poppins(
            color: Colors.black87,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: _currentPage > 0
            ? IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.black87),
                onPressed: _previousPage,
              )
            : null,
      ),
      body: Column(
        children: [
          // Progress indicator
          LinearProgressIndicator(
            value: (_currentPage + 1) / _totalPages,
            backgroundColor: Colors.grey[200],
            color: const Color(0xFF4F46E5),
            minHeight: 6,
          ),

          // Page content
          Expanded(
            child: PageView(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              onPageChanged: (page) {
                setState(() {
                  _currentPage = page;
                });
              },
              children: [
                _buildBasicInfoPage(),
                _buildUsernamePage(),
                _buildSkillsPage(),
                _buildInterestsPage(),
              ],
            ),
          ),

          // Bottom navigation
          SafeArea(child: _buildBottomNavigation()),
        ],
      ),
    );
  }

  // Build basic info page
  Widget _buildBasicInfoPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Form(
        key: _basicInfoFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Basic Information',
              style: GoogleFonts.poppins(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Let\'s start with some basic information about you',
              style: GoogleFonts.poppins(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 32),

            // Full Name
            Text(
              'Full Name',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            TextFormField(
              controller: _fullNameController,
              decoration: InputDecoration(
                hintText: 'John Doe',
                hintStyle: GoogleFonts.poppins(color: Colors.grey),
                filled: true,
                fillColor: Colors.grey.shade50,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade200),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF4F46E5), width: 1.5),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.red.shade300),
                ),
                contentPadding: const EdgeInsets.all(16),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your full name';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),

            // Bio
            Text(
              'Bio',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            TextFormField(
              controller: _bioController,
              maxLines: 4,
              decoration: InputDecoration(
                hintText: 'Tell us about yourself, your experience, and what you\'re passionate about',
                hintStyle: GoogleFonts.poppins(color: Colors.grey),
                filled: true,
                fillColor: Colors.grey.shade50,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade200),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF4F46E5), width: 1.5),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.red.shade300),
                ),
                contentPadding: const EdgeInsets.all(16),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a bio';
                }
                if (value.length < 20) {
                  return 'Bio should be at least 20 characters';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),

            // Location
            Text(
              'Location',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            TextFormField(
              controller: _locationController,
              decoration: InputDecoration(
                hintText: 'City, Country',
                hintStyle: GoogleFonts.poppins(color: Colors.grey),
                filled: true,
                fillColor: Colors.grey.shade50,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade200),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF6A11CB), width: 1.5),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.red.shade300),
                ),
                contentPadding: const EdgeInsets.all(16),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your location';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  // Build username page
  Widget _buildUsernamePage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Form(
        key: _usernameFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Choose a Username',
              style: GoogleFonts.poppins(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Your username is how other members will recognize you',
              style: GoogleFonts.poppins(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 32),

            // Username
            Text(
              'Username',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            TextFormField(
              controller: _usernameController,
              decoration: InputDecoration(
                hintText: 'e.g. john_dev',
                hintStyle: GoogleFonts.poppins(color: Colors.grey),
                filled: true,
                fillColor: Colors.grey.shade50,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide.none,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade200),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF4F46E5), width: 1.5),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.red.shade300),
                ),
                contentPadding: const EdgeInsets.all(16),
                suffixIcon: _isCheckingUsername
                    ? const Padding(
                        padding: EdgeInsets.all(12),
                        child: SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4F46E5)),
                          ),
                        ),
                      )
                    : _usernameController.text.isNotEmpty
                        ? Container(
                            margin: const EdgeInsets.all(8.0),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: _isUsernameAvailable ? Colors.green : Colors.red,
                            ),
                            child: Icon(
                              _isUsernameAvailable ? Icons.check : Icons.close,
                              color: Colors.white,
                              size: 20,
                            ),
                          )
                        : null,
              ),
              onChanged: (value) {
                // Reset state when user is typing
                setState(() {
                  _isCheckingUsername = false;
                });

                // Debounce the username check
                if (_debounceTimer?.isActive ?? false) {
                  _debounceTimer!.cancel();
                }

                // Set a timer to check username availability after user stops typing
                _debounceTimer = Timer(const Duration(milliseconds: 500), () {
                  if (value.length >= 4) {
                    _checkUsernameAvailability(value);
                  } else {
                    setState(() {
                      _isUsernameAvailable = false;
                      _usernameError = value.isEmpty
                          ? 'Please enter a username'
                          : 'Username must be at least 4 characters';
                    });
                  }
                });
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter a username';
                }
                if (value.length < 4) {
                  return 'Username must be at least 4 characters';
                }
                if (!RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(value)) {
                  return 'Username can only contain letters, numbers, and underscores';
                }
                if (!_isUsernameAvailable) {
                  return _usernameError;
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Username guidelines
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.shade100),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Username Guidelines:',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w600,
                      color: Colors.blue.shade800,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildGuideline('At least 4 characters long'),
                  _buildGuideline('Can contain letters, numbers, and underscores'),
                  _buildGuideline('Cannot contain spaces or special characters'),
                  _buildGuideline('Must be unique'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build interests page
  Widget _buildInterestsPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Form(
        key: _interestsFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Select Your Interests',
              style: GoogleFonts.poppins(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Choose at least one area that interests you',
              style: GoogleFonts.poppins(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 32),

            // Selected interests
            if (_selectedInterests.isNotEmpty) ...[
              Text(
                'Selected Interests',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _selectedInterests.map((interest) {
                  return Chip(
                    label: Text(
                      interest,
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    backgroundColor: const Color(0xFF4F46E5),
                    deleteIconColor: Colors.white,
                    onDeleted: () {
                      setState(() {
                        _selectedInterests.remove(interest);
                      });
                    },
                  );
                }).toList(),
              ),
              const SizedBox(height: 24),
            ],

            // Available interests
            Text(
              'Available Interests',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _availableInterests.map((interest) {
                final isSelected = _selectedInterests.contains(interest);
                return FilterChip(
                  label: Text(
                    interest,
                    style: GoogleFonts.poppins(
                      color: isSelected ? Colors.white : Colors.black87,
                      fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                    ),
                  ),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      if (selected) {
                        _selectedInterests.add(interest);
                      } else {
                        _selectedInterests.remove(interest);
                      }
                    });
                  },
                  selectedColor: const Color(0xFF4F46E5),
                  checkmarkColor: Colors.white,
                  backgroundColor: Colors.grey.shade100,
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  // Build skills page
  Widget _buildSkillsPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Form(
        key: _skillsFormKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Add Your Skills',
              style: GoogleFonts.poppins(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Select skills and rate your proficiency level',
              style: GoogleFonts.poppins(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 32),

            // Selected skills with sliders
            if (_selectedSkills.isNotEmpty) ...[
              Text(
                'Your Skills',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 16),
              ...List.generate(_selectedSkills.length, (index) {
                final skill = _selectedSkills[index];
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          skill,
                          style: GoogleFonts.poppins(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close, size: 18),
                          onPressed: () {
                            setState(() {
                              _selectedSkills.remove(skill);
                              _skillLevels.remove(skill);
                            });
                          },
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Text(
                          'Beginner',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                        Expanded(
                          child: Slider(
                            value: _skillLevels[skill] ?? 0.5,
                            onChanged: (value) {
                              setState(() {
                                _skillLevels[skill] = value;
                              });
                            },
                            activeColor: const Color(0xFF4F46E5),
                            inactiveColor: Colors.grey[300],
                          ),
                        ),
                        Text(
                          'Expert',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                    const Divider(),
                  ],
                );
              }),
              const SizedBox(height: 24),
            ],

            // Available skills
            Text(
              'Add More Skills',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _availableSkills.map((skill) {
                final isSelected = _selectedSkills.contains(skill);
                return FilterChip(
                  label: Text(
                    skill,
                    style: GoogleFonts.poppins(
                      color: isSelected ? Colors.white : Colors.black87,
                      fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                    ),
                  ),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      if (selected) {
                        _selectedSkills.add(skill);
                        _skillLevels[skill] = 0.5; // Default to middle level
                      } else {
                        _selectedSkills.remove(skill);
                        _skillLevels.remove(skill);
                      }
                    });
                  },
                  selectedColor: const Color(0xFF4F46E5),
                  checkmarkColor: Colors.white,
                  backgroundColor: Colors.grey.shade100,
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  // Build guideline item
  Widget _buildGuideline(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.check_circle,
            size: 16,
            color: Colors.blue.shade800,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: GoogleFonts.poppins(
                fontSize: 13,
                color: Colors.blue.shade800,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build bottom navigation buttons
  Widget _buildBottomNavigation() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Back button placeholder
          _currentPage > 0
              ? const SizedBox(width: 80)
              : const SizedBox(width: 80),

          // Next/Finish button
          SizedBox(
            width: 200,
            height: 50,
            child: ElevatedButton(
              onPressed: _isLoading
                  ? null
                  : () {
                      // Validate current page before proceeding
                      if (_currentPage == 0 && !_basicInfoFormKey.currentState!.validate()) {
                        return;
                      }
                      if (_currentPage == 1 && !_usernameFormKey.currentState!.validate()) {
                        return;
                      }
                      if (_currentPage == 2 && _selectedSkills.isEmpty) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'Please select at least one skill',
                              style: GoogleFonts.poppins(),
                            ),
                            backgroundColor: Colors.red,
                          ),
                        );
                        return;
                      }
                      if (_currentPage == 3 && _selectedInterests.isEmpty) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'Please select at least one interest',
                              style: GoogleFonts.poppins(),
                            ),
                            backgroundColor: Colors.red,
                          ),
                        );
                        return;
                      }

                      _nextPage();
                    },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4F46E5),
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: _isLoading
                  ? const CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    )
                  : Text(
                      _currentPage == _totalPages - 1 ? 'Finish' : 'Next',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
