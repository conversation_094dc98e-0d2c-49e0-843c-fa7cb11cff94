import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'services/admin_service.dart';
import 'models/feedback_model.dart';
import 'services/notification_service.dart';
import 'services/security_service.dart';
import 'services/feedback_service.dart';
import 'services/post_deletion_service.dart';
import 'admin/flagged_conversations_panel.dart';
import 'admin/deleted_accounts_panel.dart';
import 'admin/bug_reports_panel.dart';

class AdminPanel extends StatefulWidget {
  const AdminPanel({Key? key}) : super(key: key);

  @override
  State<AdminPanel> createState() => _AdminPanelState();
}

class _AdminPanelState extends State<AdminPanel> with TickerProviderStateMixin {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  late AdminService _adminService;
  late NotificationService _notificationService;
  late SecurityService _securityService;
  late FeedbackService _feedbackService;
  final PostDeletionService _postDeletionService = PostDeletionService();

  late TabController _tabController;
  bool _isAuthenticated = false;
  bool _isLoading = true;
  String _errorMessage = '';

  final TextEditingController _usernameController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _warningMessageController = TextEditingController();

  final TextEditingController _rejectionReasonController = TextEditingController();

  // Support requests tab
  Widget _buildSimpleSupportRequestsTab() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Support Requests',
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Manage user support requests and bug reports',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),

          Expanded(
            child: StreamBuilder<QuerySnapshot>(
              stream: FirebaseFirestore.instance
                  .collection('support_requests')
                  .orderBy('createdAt', descending: true)
                  .limit(20) // Limit to 20 items for better performance
                  .snapshots(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (snapshot.hasError) {
                  return Center(
                    child: Text(
                      'Error loading support requests: ${snapshot.error}',
                      style: GoogleFonts.poppins(color: Colors.red),
                    ),
                  );
                }

                if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
                  return Center(
                    child: Text(
                      'No support requests found',
                      style: GoogleFonts.poppins(fontSize: 16),
                    ),
                  );
                }

                final supportRequests = snapshot.data!.docs;

                return ListView.builder(
                  itemCount: supportRequests.length,
                  itemBuilder: (context, index) {
                    final request = supportRequests[index].data() as Map<String, dynamic>;
                    final requestId = supportRequests[index].id;
                    final bool isPending = request['status'] == 'pending';

                    return Card(
                      margin: const EdgeInsets.only(bottom: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(
                          color: isPending ? Colors.orange.shade300 : Colors.green.shade300,
                          width: isPending ? 2 : 1,
                        ),
                      ),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: Colors.blue.shade100,
                          child: const Icon(
                            Icons.support_agent,
                            color: Colors.blue,
                          ),
                        ),
                        title: Text(
                          request['subject'] ?? 'No Subject',
                          style: GoogleFonts.poppins(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        subtitle: Text(
                          'From: ${request['userName'] ?? 'Unknown User'}',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                          ),
                        ),
                        trailing: Chip(
                          label: Text(
                            request['status'] ?? 'pending',
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              color: Colors.white,
                            ),
                          ),
                          backgroundColor: isPending ? Colors.orange : Colors.green,
                        ),
                        onTap: () {
                          // Show dialog with details
                          showDialog(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: Text(
                                request['subject'] ?? 'Support Request',
                                style: GoogleFonts.poppins(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              content: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'From: ${request['userName'] ?? 'Unknown User'}',
                                    style: GoogleFonts.poppins(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    request['message'] ?? 'No message',
                                    style: GoogleFonts.poppins(),
                                  ),
                                ],
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.pop(context),
                                  child: Text(
                                    'Close',
                                    style: GoogleFonts.poppins(),
                                  ),
                                ),
                                if (isPending)
                                  ElevatedButton(
                                    onPressed: () {
                                      // Show immediate feedback
                                      ScaffoldMessenger.of(context).showSnackBar(
                                        const SnackBar(
                                          content: Text('Processing...'),
                                          duration: Duration(seconds: 1),
                                        ),
                                      );

                                      Navigator.pop(context);

                                      // Mark as resolved in the background
                                      FirebaseFirestore.instance
                                          .collection('support_requests')
                                          .doc(requestId)
                                          .update({
                                        'status': 'resolved',
                                        'resolvedAt': FieldValue.serverTimestamp(),
                                      });
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.green,
                                    ),
                                    child: Text(
                                      'Mark as Resolved',
                                      style: GoogleFonts.poppins(
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          );
                        },
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 8, vsync: this); // Updated to 8 tabs

    // Add listener to load data only when tab is selected
    _tabController.addListener(_handleTabSelection);

    _adminService = AdminService();
    _notificationService = NotificationService();
    _securityService = SecurityService();
    _feedbackService = FeedbackService();

    // Only check authentication initially
    _checkAuthentication();
  }

  // Handle tab selection to load data only when needed
  void _handleTabSelection() {
    // Only proceed if the tab selection is complete (not during animation)
    if (!_tabController.indexIsChanging) {
      // Load data based on the selected tab
      switch (_tabController.index) {
        case 0: // User Management
          _loadUsers();
          break;
        case 1: // Asset Approvals
          _loadPendingAssets();
          break;
        case 2: // Feedback
          _loadFeedbackData();
          break;
        // Other tabs will load their data internally
      }
    }
  }

  // Load users data for the User Management tab
  void _loadUsers() {
    // This method is called when the User Management tab is selected
    // The data is already loaded in the _buildUserManagementTab method
    // This is just a placeholder for future optimization
  }

  // Load pending assets for the Asset Approvals tab
  void _loadPendingAssets() {
    // This method is called when the Asset Approvals tab is selected
    // The data is already loaded in the _buildAssetApprovalsTab method
    // This is just a placeholder for future optimization
  }

  // Load feedback data for the Feedback tab
  void _loadFeedbackData() {
    // This method is called when the Feedback tab is selected
    // The data is already loaded in the _buildFeedbackTab method
    // This is just a placeholder for future optimization
  }

  @override
  void dispose() {
    _tabController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    _warningMessageController.dispose();
    _rejectionReasonController.dispose();
    super.dispose();
  }

  // Format date
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        return '${difference.inMinutes} min ago';
      }
      return '${difference.inHours} hours ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  // Check if user is authenticated as admin
  Future<void> _checkAuthentication() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // First check secure storage for cached admin status
      final isAdminCached = await _secureStorage.read(key: 'isAdmin');
      if (isAdminCached == 'true') {
        setState(() {
          _isAuthenticated = true;
          _isLoading = false;
        });
        return;
      }

      // If not cached, check with Firebase
      final isAdmin = await _adminService.isCurrentUserAdmin();

      // Cache the result
      if (isAdmin) {
        await _secureStorage.write(key: 'isAdmin', value: 'true');
      }

      setState(() {
        _isAuthenticated = isAdmin;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error checking admin status: $e';
      });
    }
  }

  // Authenticate admin
  Future<void> _authenticate() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      // Get user credentials
      final username = _usernameController.text.trim();
      final password = _passwordController.text.trim();

      if (username.isEmpty || password.isEmpty) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Please enter both username and password';
        });
        return;
      }

      // Sign in with email and password
      await _auth.signInWithEmailAndPassword(
        email: username,
        password: password,
      );

      // Check if user is admin
      final isAdmin = await _adminService.isCurrentUserAdmin();

      if (isAdmin) {
        // Store authentication state securely
        await _secureStorage.write(key: 'isAdmin', value: 'true');
        setState(() {
          _isAuthenticated = true;
          _isLoading = false;
        });
      } else {
        // Not an admin
        await _auth.signOut();
        setState(() {
          _isLoading = false;
          _errorMessage = 'You do not have admin privileges';
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Authentication failed: ${e.toString()}';
      });
    }
  }

  // Sign out
  Future<void> _signOut() async {
    try {
      await _auth.signOut();
      await _secureStorage.delete(key: 'isAdmin');
      setState(() {
        _isAuthenticated = false;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error signing out: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Asatu Admin Panel',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF6A11CB),
        actions: [
          if (_isAuthenticated)
            IconButton(
              icon: const Icon(Icons.logout),
              onPressed: _signOut,
              tooltip: 'Sign Out',
            ),
        ],
      ),
      body: _isAuthenticated ? _buildAdminPanel() : _buildLoginScreen(),
    );
  }

  // Build login screen
  Widget _buildLoginScreen() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF6A11CB), Color(0xFF2575FC)],
        ),
      ),
      child: Center(
        child: Card(
          elevation: 8,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          margin: const EdgeInsets.all(32),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 400),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    'Admin Access Only',
                    style: GoogleFonts.poppins(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF6A11CB),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'This area is restricted to authorized personnel',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),
                  TextField(
                    controller: _usernameController,
                    decoration: InputDecoration(
                      labelText: 'Email',
                      prefixIcon: const Icon(Icons.person),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _passwordController,
                    obscureText: true,
                    decoration: InputDecoration(
                      labelText: 'Password',
                      prefixIcon: const Icon(Icons.lock),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                  if (_errorMessage.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    Text(
                      _errorMessage,
                      style: GoogleFonts.poppins(
                        color: Colors.red,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: _authenticate,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF6A11CB),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      'Login',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Build the main admin panel with tabs
  Widget _buildAdminPanel() {
    return Column(
      children: [
        Container(
          color: const Color(0xFF6A11CB),
          child: TabBar(
            controller: _tabController,
            indicatorColor: Colors.white,
            indicatorWeight: 3,
            isScrollable: true,
            tabs: const [
              Tab(text: 'User Management'),
              Tab(text: 'Asset Approvals'),
              Tab(text: 'Reported Posts'),
              Tab(text: 'Feedback'),
              Tab(text: 'Flagged Conversations'),
              Tab(text: 'Support Requests'),
              Tab(text: 'Deleted Accounts'),
              Tab(text: 'Bug Reports'),
            ],
          ),
        ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildUserManagementTab(),
              _buildAssetApprovalsTab(),
              _buildReportedPostsTab(),
              StreamBuilder<List<FeedbackModel>>(
                stream: _feedbackService.getLimitedFeedback(20), // Limit to 20 items for better performance
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (snapshot.hasError) {
                    return Center(
                      child: Text(
                        'Error loading feedback: ${snapshot.error}',
                        style: GoogleFonts.poppins(color: Colors.red),
                      ),
                    );
                  }

                  if (!snapshot.hasData || snapshot.data!.isEmpty) {
                    return Center(
                      child: Text(
                        'No feedback found',
                        style: GoogleFonts.poppins(fontSize: 16),
                      ),
                    );
                  }

                  final feedbackList = snapshot.data!;

                  return Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'User Feedback',
                          style: GoogleFonts.poppins(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Review and manage user feedback',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Feedback list
                        Expanded(
                          child: ListView.builder(
                            itemCount: feedbackList.length,
                            itemBuilder: (context, index) {
                              final feedback = feedbackList[index];

                              return Card(
                                margin: const EdgeInsets.only(bottom: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  side: BorderSide(
                                    color: feedback.isRead ? Colors.grey.shade300 : Colors.blue.shade300,
                                    width: feedback.isRead ? 1 : 2,
                                  ),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(16),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          CircleAvatar(
                                            backgroundImage: feedback.userPhotoUrl.isNotEmpty
                                                ? NetworkImage(feedback.userPhotoUrl)
                                                : null,
                                            child: feedback.userPhotoUrl.isEmpty
                                                ? Text(feedback.userName.isNotEmpty
                                                    ? feedback.userName[0].toUpperCase()
                                                    : 'A')
                                                : null,
                                          ),
                                          const SizedBox(width: 12),
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  feedback.userName,
                                                  style: GoogleFonts.poppins(
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 16,
                                                  ),
                                                ),
                                                if (feedback.userEmail.isNotEmpty)
                                                  Text(
                                                    feedback.userEmail,
                                                    style: GoogleFonts.poppins(
                                                      fontSize: 12,
                                                      color: Colors.grey[600],
                                                    ),
                                                  ),
                                              ],
                                            ),
                                          ),
                                          Text(
                                            _formatDate(feedback.timestamp),
                                            style: GoogleFonts.poppins(
                                              fontSize: 12,
                                              color: Colors.grey[600],
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 16),
                                      if (feedback.category != null)
                                        Chip(
                                          label: Text(
                                            feedback.category!,
                                            style: GoogleFonts.poppins(
                                              fontSize: 12,
                                              color: Colors.white,
                                            ),
                                          ),
                                          backgroundColor: Colors.blue,
                                        ),
                                      const SizedBox(height: 8),
                                      Text(
                                        feedback.message,
                                        style: GoogleFonts.poppins(
                                          fontSize: 14,
                                        ),
                                      ),
                                      if (feedback.rating != null) ...[
                                        const SizedBox(height: 8),
                                        Row(
                                          children: [
                                            Text(
                                              'Rating: ',
                                              style: GoogleFonts.poppins(
                                                fontSize: 12,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                            ...List.generate(5, (index) {
                                              return Icon(
                                                index < (feedback.rating ?? 0) ? Icons.star : Icons.star_border,
                                                color: index < (feedback.rating ?? 0) ? Colors.amber : Colors.grey,
                                                size: 16,
                                              );
                                            }),
                                          ],
                                        ),
                                      ],
                                      const SizedBox(height: 16),
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.end,
                                        children: [
                                          // Send appreciation button
                                          TextButton.icon(
                                            icon: const Icon(Icons.favorite, color: Colors.pink),
                                            label: const Text('Send Appreciation', style: TextStyle(color: Colors.pink)),
                                            onPressed: () {
                                              showDialog(
                                                context: context,
                                                builder: (context) => AlertDialog(
                                                  title: Text(
                                                    'Send Appreciation',
                                                    style: GoogleFonts.poppins(
                                                      fontWeight: FontWeight.bold,
                                                    ),
                                                  ),
                                                  content: Column(
                                                    mainAxisSize: MainAxisSize.min,
                                                    children: [
                                                      Text(
                                                        'Send a thank you note to ${feedback.userName} for their feedback:',
                                                        style: GoogleFonts.poppins(),
                                                      ),
                                                      const SizedBox(height: 16),
                                                      TextField(
                                                        maxLines: 3,
                                                        decoration: InputDecoration(
                                                          hintText: 'Thank you for your valuable feedback...',
                                                          border: OutlineInputBorder(
                                                            borderRadius: BorderRadius.circular(12),
                                                          ),
                                                        ),
                                                        controller: TextEditingController(
                                                          text: 'Thank you for your valuable feedback! We appreciate your input and will use it to improve our app.',
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  actions: [
                                                    TextButton(
                                                      onPressed: () => Navigator.pop(context),
                                                      child: Text(
                                                        'Cancel',
                                                        style: GoogleFonts.poppins(),
                                                      ),
                                                    ),
                                                    ElevatedButton(
                                                      onPressed: () async {
                                                        try {
                                                          // Get the message from the text field
                                                          const String appreciationMessage =
                                                              'Thank you for your valuable feedback! We appreciate your input and will use it to improve our app.';

                                                          // Send notification to user
                                                          await _notificationService.sendNotification(
                                                            userId: feedback.userId,
                                                            title: 'Appreciation from Asatu',
                                                            body: appreciationMessage,
                                                            type: 'appreciation',
                                                            data: {
                                                              'type': 'appreciation',
                                                              'feedbackId': feedback.id,
                                                              'message': appreciationMessage,
                                                            },
                                                          );

                                                          if (context.mounted) {
                                                            Navigator.pop(context);
                                                            ScaffoldMessenger.of(context).showSnackBar(
                                                              const SnackBar(
                                                                content: Text('Appreciation sent!'),
                                                                backgroundColor: Colors.green,
                                                              ),
                                                            );
                                                          }
                                                        } catch (e) {
                                                          if (context.mounted) {
                                                            ScaffoldMessenger.of(context).showSnackBar(
                                                              SnackBar(
                                                                content: Text('Error sending appreciation: $e'),
                                                                backgroundColor: Colors.red,
                                                              ),
                                                            );
                                                          }
                                                        }
                                                      },
                                                      style: ElevatedButton.styleFrom(
                                                        backgroundColor: Colors.pink,
                                                      ),
                                                      child: Text(
                                                        'Send',
                                                        style: GoogleFonts.poppins(
                                                          color: Colors.white,
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              );
                                            },
                                          ),
                                          const SizedBox(width: 8),
                                          if (!feedback.isRead)
                                            TextButton.icon(
                                              icon: const Icon(Icons.check),
                                              label: const Text('Mark as Read'),
                                              onPressed: () {
                                                _feedbackService.markAsRead(feedback.id);
                                              },
                                            ),
                                          const SizedBox(width: 8),
                                          TextButton.icon(
                                            icon: const Icon(Icons.delete, color: Colors.red),
                                            label: const Text('Delete', style: TextStyle(color: Colors.red)),
                                            onPressed: () {
                                              showDialog(
                                                context: context,
                                                builder: (context) => AlertDialog(
                                                  title: Text(
                                                    'Delete Feedback',
                                                    style: GoogleFonts.poppins(
                                                      fontWeight: FontWeight.bold,
                                                    ),
                                                  ),
                                                  content: Text(
                                                    'Are you sure you want to delete this feedback from ${feedback.userName}?',
                                                    style: GoogleFonts.poppins(),
                                                  ),
                                                  actions: [
                                                    TextButton(
                                                      onPressed: () => Navigator.pop(context),
                                                      child: Text(
                                                        'Cancel',
                                                        style: GoogleFonts.poppins(),
                                                      ),
                                                    ),
                                                    ElevatedButton(
                                                      onPressed: () async {
                                                        await _feedbackService.deleteFeedback(feedback.id);
                                                        if (context.mounted) {
                                                          Navigator.pop(context);
                                                          ScaffoldMessenger.of(context).showSnackBar(
                                                            const SnackBar(
                                                              content: Text('Feedback deleted'),
                                                              backgroundColor: Colors.red,
                                                            ),
                                                          );
                                                        }
                                                      },
                                                      style: ElevatedButton.styleFrom(
                                                        backgroundColor: Colors.red,
                                                      ),
                                                      child: Text(
                                                        'Delete',
                                                        style: GoogleFonts.poppins(
                                                          color: Colors.white,
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              );
                                            },
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
              const FlaggedConversationsPanel(),
              _buildSimpleSupportRequestsTab(),
              const DeletedAccountsPanel(),
              const BugReportsPanel(),
            ],
          ),
        ),
      ],
    );
  }

  // Build reported posts tab
  Widget _buildReportedPostsTab() {
    return StreamBuilder<QuerySnapshot>(
      stream: _firestore.collection('reported_posts').orderBy('createdAt', descending: true).snapshots(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Text(
              'Error loading reported posts: ${snapshot.error}',
              style: GoogleFonts.poppins(color: Colors.red),
            ),
          );
        }

        if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.report_outlined,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'No reported posts',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'All posts are following community guidelines',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          );
        }

        final reports = snapshot.data!.docs;

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Reported Posts',
                style: GoogleFonts.poppins(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Review and take action on reported posts',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 16),

              // Reports list
              Expanded(
                child: ListView.builder(
                  itemCount: reports.length,
                  itemBuilder: (context, index) {
                    final report = reports[index].data() as Map<String, dynamic>;
                    final reportId = reports[index].id;
                    final bool isPending = report['status'] == 'pending';

                    return Card(
                      margin: const EdgeInsets.only(bottom: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(
                          color: isPending ? Colors.red.shade300 : Colors.green.shade300,
                          width: isPending ? 2 : 1,
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: isPending ? Colors.red.shade100 : Colors.green.shade100,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    isPending ? 'PENDING' : 'RESOLVED',
                                    style: GoogleFonts.poppins(
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                      color: isPending ? Colors.red.shade700 : Colors.green.shade700,
                                    ),
                                  ),
                                ),
                                const Spacer(),
                                Text(
                                  _formatDate(report['createdAt'] is Timestamp
                                      ? (report['createdAt'] as Timestamp).toDate()
                                      : report['createdAt'] ?? DateTime.now()),
                                  style: GoogleFonts.poppins(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Text(
                              'Reason: ${report['reason'] ?? 'No reason provided'}',
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Post ID: ${report['postId'] ?? 'Unknown'}',
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Reported by: ${report['reportedBy'] ?? 'Anonymous'}',
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 12),
                            // Post content preview
                            FutureBuilder<Map<String, dynamic>?>(
                              future: _getPostContent(report['postId']),
                              builder: (context, snapshot) {
                                if (snapshot.connectionState == ConnectionState.waiting) {
                                  return Container(
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: Colors.grey[100],
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Row(
                                      children: [
                                        const SizedBox(
                                          width: 16,
                                          height: 16,
                                          child: CircularProgressIndicator(strokeWidth: 2),
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          'Loading post content...',
                                          style: GoogleFonts.poppins(
                                            fontSize: 12,
                                            color: Colors.grey[600],
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                }

                                if (snapshot.hasError || !snapshot.hasData) {
                                  return Container(
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: Colors.red[50],
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(color: Colors.red[200]!),
                                    ),
                                    child: Text(
                                      'Post not found or deleted',
                                      style: GoogleFonts.poppins(
                                        fontSize: 12,
                                        color: Colors.red[700],
                                        fontStyle: FontStyle.italic,
                                      ),
                                    ),
                                  );
                                }

                                final postData = snapshot.data!;
                                final String content = postData['content'] ?? '';
                                final String authorName = postData['authorName'] ?? 'Unknown';
                                final String postType = postData['type'] ?? 'regular';

                                // Build content display based on post type
                                Widget contentWidget;
                                if (postType == 'code') {
                                  final String codeContent = postData['codeContent'] ?? '';
                                  final String language = postData['language'] ?? 'text';
                                  contentWidget = Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      if (content.isNotEmpty) ...[
                                        Text(
                                          content,
                                          style: GoogleFonts.poppins(
                                            fontSize: 13,
                                            color: Colors.grey[800],
                                          ),
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        const SizedBox(height: 8),
                                      ],
                                      Container(
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          color: Colors.grey[100],
                                          borderRadius: BorderRadius.circular(4),
                                          border: Border.all(color: Colors.grey[300]!),
                                        ),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                Icon(Icons.code, size: 12, color: Colors.grey[600]),
                                                const SizedBox(width: 4),
                                                Text(
                                                  language.toUpperCase(),
                                                  style: GoogleFonts.poppins(
                                                    fontSize: 10,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.grey[600],
                                                  ),
                                                ),
                                              ],
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              codeContent.isNotEmpty ? codeContent : 'No code content',
                                              style: GoogleFonts.robotoMono(
                                                fontSize: 11,
                                                color: Colors.grey[800],
                                              ),
                                              maxLines: 3,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  );
                                } else if (postType == 'poll') {
                                  final String question = postData['question'] ?? '';
                                  final List<dynamic> options = postData['options'] ?? [];
                                  contentWidget = Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      if (content.isNotEmpty) ...[
                                        Text(
                                          content,
                                          style: GoogleFonts.poppins(
                                            fontSize: 13,
                                            color: Colors.grey[800],
                                          ),
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        const SizedBox(height: 8),
                                      ],
                                      Container(
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          color: Colors.blue[50],
                                          borderRadius: BorderRadius.circular(4),
                                          border: Border.all(color: Colors.blue[200]!),
                                        ),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                Icon(Icons.poll, size: 12, color: Colors.blue[600]),
                                                const SizedBox(width: 4),
                                                Text(
                                                  'POLL QUESTION',
                                                  style: GoogleFonts.poppins(
                                                    fontSize: 10,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.blue[600],
                                                  ),
                                                ),
                                              ],
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              question.isNotEmpty ? question : 'No question',
                                              style: GoogleFonts.poppins(
                                                fontSize: 12,
                                                fontWeight: FontWeight.w500,
                                                color: Colors.grey[800],
                                              ),
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                            if (options.isNotEmpty) ...[
                                              const SizedBox(height: 4),
                                              Text(
                                                'Options: ${options.take(2).map((o) => o['text'] ?? '').join(', ')}${options.length > 2 ? '...' : ''}',
                                                style: GoogleFonts.poppins(
                                                  fontSize: 10,
                                                  color: Colors.grey[600],
                                                ),
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ],
                                          ],
                                        ),
                                      ),
                                    ],
                                  );
                                } else {
                                  // Regular post
                                  contentWidget = Text(
                                    content.isNotEmpty ? content : 'No content available',
                                    style: GoogleFonts.poppins(
                                      fontSize: 13,
                                      color: Colors.grey[800],
                                    ),
                                    maxLines: 3,
                                    overflow: TextOverflow.ellipsis,
                                  );
                                }

                                return Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: Colors.blue[50],
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(color: Colors.blue[200]!),
                                  ),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Icon(
                                            postType == 'poll' ? Icons.poll :
                                            postType == 'code' ? Icons.code :
                                            Icons.article,
                                            size: 16,
                                            color: Colors.blue[700],
                                          ),
                                          const SizedBox(width: 4),
                                          Text(
                                            'Post by $authorName',
                                            style: GoogleFonts.poppins(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w600,
                                              color: Colors.blue[700],
                                            ),
                                          ),
                                          const Spacer(),
                                          Container(
                                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                            decoration: BoxDecoration(
                                              color: Colors.blue[100],
                                              borderRadius: BorderRadius.circular(4),
                                            ),
                                            child: Text(
                                              postType.toUpperCase(),
                                              style: GoogleFonts.poppins(
                                                fontSize: 10,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.blue[700],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 8),
                                      contentWidget,
                                      if (postData['imageUrl'] != null) ...[
                                        const SizedBox(height: 8),
                                        Row(
                                          children: [
                                            Icon(
                                              Icons.image,
                                              size: 14,
                                              color: Colors.grey[600],
                                            ),
                                            const SizedBox(width: 4),
                                            Text(
                                              'Contains image',
                                              style: GoogleFonts.poppins(
                                                fontSize: 11,
                                                color: Colors.grey[600],
                                                fontStyle: FontStyle.italic,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ],
                                  ),
                                );
                              },
                            ),
                            if (isPending) ...[
                              const SizedBox(height: 16),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  TextButton(
                                    onPressed: () => _dismissReport(reportId),
                                    child: Text(
                                      'Dismiss',
                                      style: GoogleFonts.poppins(color: Colors.grey[600]),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  ElevatedButton(
                                    onPressed: () => _removeReportedPost(report, reportId),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.red,
                                    ),
                                    child: Text(
                                      'Remove Post',
                                      style: GoogleFonts.poppins(color: Colors.white),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Dismiss a report (mark as resolved without action)
  Future<void> _dismissReport(String reportId) async {
    try {
      // Get report details before dismissing
      final reportDoc = await _firestore.collection('reported_posts').doc(reportId).get();
      if (!reportDoc.exists) {
        throw Exception('Report not found');
      }

      final reportData = reportDoc.data() as Map<String, dynamic>;
      final String reportedBy = reportData['reportedBy'] ?? '';

      // Update report status
      await _firestore.collection('reported_posts').doc(reportId).update({
        'status': 'dismissed',
        'resolvedAt': FieldValue.serverTimestamp(),
        'resolvedBy': _auth.currentUser?.uid,
      });

      // Send notification to reporter (with error handling)
      if (reportedBy.isNotEmpty) {
        try {
          debugPrint('DISMISS: Attempting to send notification to reporter: $reportedBy');

          // Check if reporter user exists before sending notification
          final reporterDoc = await _firestore.collection('users').doc(reportedBy).get();
          debugPrint('DISMISS: Reporter user exists: ${reporterDoc.exists}');

          if (reporterDoc.exists) {
            await _notificationService.sendNotification(
              userId: reportedBy,
              title: 'Report Update',
              body: 'Thank you for your report. After review, the post does not violate our community guidelines.',
              type: 'report_dismissed',
              data: {
                'reportId': reportId,
                'action': 'dismissed',
              },
            );
            debugPrint('✅ DISMISS: Notification successfully sent to reporter: $reportedBy');
          } else {
            debugPrint('⚠️ DISMISS: Reporter user not found: $reportedBy');
          }
        } catch (e) {
          debugPrint('❌ DISMISS: Error sending notification to reporter: $e');
          debugPrint('❌ DISMISS: Error stack trace: ${StackTrace.current}');
        }
      } else {
        debugPrint('⚠️ DISMISS: No reporter ID found in report data');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Report dismissed and notification sent',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error dismissing report: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error dismissing report: $e',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Remove a reported post and send notifications (with cascade delete)
  Future<void> _removeReportedPost(Map<String, dynamic> report, String reportId) async {
    try {
      final String postId = report['postId'] ?? '';
      final String reportedBy = report['reportedBy'] ?? '';

      if (postId.isEmpty) {
        throw Exception('Post ID not found in report');
      }

      // Get post details AND user details before deletion
      final postDoc = await _firestore.collection('posts').doc(postId).get();

      String postAuthorId = '';
      String postContent = '';

      if (postDoc.exists) {
        final postData = postDoc.data() as Map<String, dynamic>;
        postAuthorId = postData['authorId'] ?? '';
        postContent = postData['content'] ?? '';
        debugPrint('DELETE: Found post data - authorId: $postAuthorId, content preview: ${postContent.length > 50 ? postContent.substring(0, 50) : postContent}');
      } else {
        debugPrint('DELETE: Post document not found, but continuing with deletion process');
        // Try to get author info from report data if available
        postAuthorId = report['postAuthorId'] ?? '';
        postContent = report['postContent'] ?? 'Post content unavailable';
      }

      // STEP 1: Send notifications FIRST (before deleting the post)
      debugPrint('DELETE: Step 1 - Sending notifications before deletion');

      // Send notification to reporter
      if (reportedBy.isNotEmpty) {
        try {
          debugPrint('DELETE: Attempting to send notification to reporter: $reportedBy');

          await _notificationService.sendNotification(
            userId: reportedBy,
            title: 'Report Update',
            body: 'Your reported post has been reviewed and removed from the community.',
            type: 'report_resolved',
            data: {
              'reportId': reportId,
              'postId': postId,
              'action': 'removed',
            },
          );
          debugPrint('✅ DELETE: Notification successfully sent to reporter: $reportedBy');
        } catch (e) {
          debugPrint('❌ DELETE: Error sending notification to reporter: $e');
        }
      } else {
        debugPrint('⚠️ DELETE: No reporter ID found in report data');
      }

      // Send notification to post creator
      if (postAuthorId.isNotEmpty) {
        try {
          debugPrint('DELETE: Attempting to send notification to post author: $postAuthorId');

          final contentPreview = postContent.length > 50
              ? '${postContent.substring(0, 47)}...'
              : postContent;

          await _notificationService.sendNotification(
            userId: postAuthorId,
            title: 'Post Removed',
            body: 'Your post "$contentPreview" has been removed from the community due to guideline violations.',
            type: 'post_removed',
            data: {
              'postId': postId,
              'reason': report['reason'] ?? 'Community guideline violation',
            },
          );
          debugPrint('✅ DELETE: Notification successfully sent to post author: $postAuthorId');
        } catch (e) {
          debugPrint('❌ DELETE: Error sending notification to post author: $e');
        }
      } else {
        debugPrint('⚠️ DELETE: No post author ID found in post data');
      }

      // STEP 2: Update report status BEFORE deletion
      debugPrint('DELETE: Step 2 - Updating report status');
      await _firestore.collection('reported_posts').doc(reportId).update({
        'status': 'resolved',
        'action': 'post_removed',
        'resolvedAt': FieldValue.serverTimestamp(),
        'resolvedBy': _auth.currentUser?.uid,
      });

      // STEP 3: Delete the post LAST (after notifications are sent)
      debugPrint('DELETE: Step 3 - Deleting post after notifications sent');
      Map<String, dynamic> deletionResult;

      if (postDoc.exists) {
        deletionResult = await _postDeletionService.deletePost(postId, isAdmin: true);

        if (!deletionResult['success']) {
          debugPrint('DELETE: Post deletion failed: ${deletionResult['error']}');
          // Don't throw error since notifications were already sent
        } else {
          debugPrint('DELETE: Post deleted with cascade: ${deletionResult['deletedItems']} items removed');

          // Update report with deletion details
          await _firestore.collection('reported_posts').doc(reportId).update({
            'deletedItems': deletionResult['deletedItems'],
          });
        }
      } else {
        // Post already deleted or doesn't exist
        deletionResult = {
          'success': true,
          'deletedItems': 0,
          'message': 'Post was already deleted or not found'
        };
        debugPrint('DELETE: Post was already deleted or not found');
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Notifications sent and post removed (${deletionResult['deletedItems']} items deleted)',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error removing reported post: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error removing post: $e',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Get post content for display in reported posts
  Future<Map<String, dynamic>?> _getPostContent(String? postId) async {
    if (postId == null || postId.isEmpty) return null;

    try {
      final postDoc = await _firestore.collection('posts').doc(postId).get();
      if (postDoc.exists) {
        return postDoc.data() as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      debugPrint('Error fetching post content: $e');
      return null;
    }
  }

  // Build user management tab
  Widget _buildUserManagementTab() {
    return StreamBuilder<QuerySnapshot>(
      stream: _firestore.collection('users').snapshots(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Text(
              'Error loading users: ${snapshot.error}',
              style: GoogleFonts.poppins(color: Colors.red),
            ),
          );
        }

        if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
          return Center(
            child: Text(
              'No users found',
              style: GoogleFonts.poppins(fontSize: 16),
            ),
          );
        }

        final users = snapshot.data!.docs;

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'User Management',
                style: GoogleFonts.poppins(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Manage user accounts, ban users, or send warnings',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 16),

              // Search bar
              TextField(
                decoration: InputDecoration(
                  hintText: 'Search users by name or email',
                  prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                onChanged: (value) {
                  // Implement search functionality
                },
              ),
              const SizedBox(height: 16),

              // User list
              Expanded(
                child: ListView.builder(
                  itemCount: users.length,
                  itemBuilder: (context, index) {
                    final userData = users[index].data() as Map<String, dynamic>;
                    final userId = users[index].id;
                    final displayName = userData['displayName'] ?? 'Unknown';
                    final email = userData['email'] ?? 'No email';
                    final photoURL = userData['photoURL'];
                    final isBanned = userData['isBanned'] ?? false;
                    final isAdmin = userData['isAdmin'] ?? false;

                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ExpansionTile(
                        leading: CircleAvatar(
                          backgroundImage: photoURL != null ? NetworkImage(photoURL) : null,
                          child: photoURL == null ? const Icon(Icons.person) : null,
                        ),
                        title: Text(
                          displayName,
                          style: GoogleFonts.poppins(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        subtitle: Text(
                          email,
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                          ),
                        ),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (isBanned)
                              Chip(
                                label: Text(
                                  'Banned',
                                  style: GoogleFonts.poppins(
                                    color: Colors.white,
                                    fontSize: 12,
                                  ),
                                ),
                                backgroundColor: Colors.red,
                              ),
                            if (isAdmin)
                              Chip(
                                label: Text(
                                  'Admin',
                                  style: GoogleFonts.poppins(
                                    color: Colors.white,
                                    fontSize: 12,
                                  ),
                                ),
                                backgroundColor: Colors.blue,
                              ),
                          ],
                        ),
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Divider(),
                                const SizedBox(height: 8),
                                Text(
                                  'User Actions',
                                  style: GoogleFonts.poppins(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                Column(
                                  children: [
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                      children: [
                                        ElevatedButton.icon(
                                          icon: Icon(isBanned ? Icons.check_circle : Icons.block),
                                          label: Text(isBanned ? 'Unban User' : 'Ban User'),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: isBanned ? Colors.green : Colors.red,
                                            foregroundColor: Colors.white,
                                          ),
                                          onPressed: () => _toggleUserBan(userId, !isBanned),
                                        ),
                                        ElevatedButton.icon(
                                          icon: const Icon(Icons.warning_amber),
                                          label: const Text('Send Warning'),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.orange,
                                            foregroundColor: Colors.white,
                                          ),
                                          onPressed: () => _showSendWarningDialog(userId, displayName),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 12),
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                      children: [
                                        ElevatedButton.icon(
                                          icon: Icon(isAdmin ? Icons.person_remove : Icons.admin_panel_settings),
                                          label: Text(isAdmin ? 'Remove Admin' : 'Make Admin'),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: isAdmin ? Colors.grey : Colors.blue,
                                            foregroundColor: Colors.white,
                                          ),
                                          onPressed: () => _showAdminConfirmationDialog(userId, displayName, !isAdmin),
                                        ),
                                        ElevatedButton.icon(
                                          icon: const Icon(Icons.delete_forever),
                                          label: const Text('Delete User'),
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Colors.deepOrange,
                                            foregroundColor: Colors.white,
                                          ),
                                          onPressed: () => _showDeleteUserDialog(userId, displayName),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 12),
                                    ElevatedButton.icon(
                                      icon: const Icon(Icons.visibility),
                                      label: const Text('View Activity Log'),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.teal,
                                        foregroundColor: Colors.white,
                                      ),
                                      onPressed: () => _showUserActivityLog(userId, displayName),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Build asset approvals tab
  Widget _buildAssetApprovalsTab() {
    return StreamBuilder<List<Map<String, dynamic>>>(
      stream: _adminService.getPendingAssetApprovals(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Text(
              'Error loading assets: ${snapshot.error}',
              style: GoogleFonts.poppins(color: Colors.red),
            ),
          );
        }

        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return Center(
            child: Text(
              'No pending assets for approval',
              style: GoogleFonts.poppins(fontSize: 16),
            ),
          );
        }

        final assets = snapshot.data!;

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Asset Approvals',
                style: GoogleFonts.poppins(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Review and approve or reject user-submitted assets',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 16),

              // Asset list
              Expanded(
                child: ListView.builder(
                  itemCount: assets.length,
                  itemBuilder: (context, index) {
                    final asset = assets[index];
                    final assetId = asset['id'] as String;
                    final title = asset['title'] as String;
                    final type = asset['type'] as String;
                    final creator = asset['creator'] as String;
                    final creatorId = asset['creatorId'] as String;
                    final description = asset['description'] as String;
                    final price = asset['price'] as double;
                    final thumbnailUrl = asset['thumbnailUrl'] as String?;

                    return Card(
                      margin: const EdgeInsets.only(bottom: 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Asset header
                          ListTile(
                            leading: thumbnailUrl != null
                                ? ClipRRect(
                                    borderRadius: BorderRadius.circular(8),
                                    child: Image.network(
                                      thumbnailUrl,
                                      width: 60,
                                      height: 60,
                                      fit: BoxFit.cover,
                                      errorBuilder: (context, error, stackTrace) {
                                        return Container(
                                          width: 60,
                                          height: 60,
                                          color: Colors.grey[300],
                                          child: const Icon(Icons.image_not_supported),
                                        );
                                      },
                                    ),
                                  )
                                : Container(
                                    width: 60,
                                    height: 60,
                                    color: Colors.grey[300],
                                    child: const Icon(Icons.image),
                                  ),
                            title: Text(
                              title,
                              style: GoogleFonts.poppins(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            subtitle: Text(
                              '$type by $creator',
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                              ),
                            ),
                            trailing: Chip(
                              label: Text(
                                price > 0 ? '\$${price.toStringAsFixed(2)}' : 'Free',
                                style: GoogleFonts.poppins(
                                  color: Colors.white,
                                  fontSize: 12,
                                ),
                              ),
                              backgroundColor: price > 0 ? Colors.green : Colors.blue,
                            ),
                          ),

                          // Asset description
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            child: Text(
                              description,
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                              ),
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),

                          // Action buttons
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                Expanded(
                                  child: ElevatedButton.icon(
                                    icon: const Icon(Icons.check_circle),
                                    label: const Text('Approve'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.green,
                                      foregroundColor: Colors.white,
                                    ),
                                    onPressed: () => _approveAsset(assetId, creatorId),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: ElevatedButton.icon(
                                    icon: const Icon(Icons.cancel),
                                    label: const Text('Reject'),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.red,
                                      foregroundColor: Colors.white,
                                    ),
                                    onPressed: () => _showRejectAssetDialog(assetId, title, creatorId),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Toggle user ban status
  Future<void> _toggleUserBan(String userId, bool isBanned) async {
    try {
      await _adminService.banUser(userId, isBanned);

      // Send notification to user
      await _notificationService.sendNotification(
        userId: userId,
        title: isBanned ? 'Account Banned' : 'Account Unbanned',
        body: isBanned
            ? 'Your account has been banned. Please contact support for more information.'
            : 'Your account has been unbanned. You can now use all features of the app.',
        type: 'account_status',
        data: {'status': isBanned ? 'banned' : 'active'},
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isBanned ? 'User banned successfully' : 'User unbanned successfully',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: isBanned ? Colors.red : Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error updating user status: $e',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Show send warning dialog
  void _showSendWarningDialog(String userId, String userName) {
    _warningMessageController.clear();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Send Warning to $userName',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: TextField(
          controller: _warningMessageController,
          decoration: const InputDecoration(
            hintText: 'Enter warning message',
            labelText: 'Warning Message',
          ),
          maxLines: 3,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final message = _warningMessageController.text.trim();
              if (message.isNotEmpty) {
                _sendWarningToUser(userId, message);
                Navigator.pop(context);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Send Warning'),
          ),
        ],
      ),
    );
  }

  // Send warning to user
  Future<void> _sendWarningToUser(String userId, String message) async {
    try {
      // Add warning to user's warnings collection
      await _firestore.collection('users').doc(userId).collection('warnings').add({
        'message': message,
        'sentBy': _auth.currentUser?.uid,
        'sentAt': FieldValue.serverTimestamp(),
        'read': false,
      });

      // Send notification to user
      await _notificationService.sendNotification(
        userId: userId,
        title: 'Warning from Admin',
        body: message,
        type: 'warning',
        data: {'message': message},
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Warning sent successfully',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error sending warning: $e',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Approve asset
  Future<void> _approveAsset(String assetId, String creatorId) async {
    try {
      await _adminService.approveAsset(assetId);

      // Send notification to user
      await _notificationService.sendNotification(
        userId: creatorId,
        title: 'Asset Approved',
        body: 'Your asset has been approved and is now available on the platform.',
        type: 'asset_approval',
        data: {'assetId': assetId, 'status': 'approved'},
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Asset approved successfully',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error approving asset: $e',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Show reject asset dialog
  void _showRejectAssetDialog(String assetId, String assetTitle, String creatorId) {
    _rejectionReasonController.clear();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Reject Asset: $assetTitle',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: TextField(
          controller: _rejectionReasonController,
          decoration: const InputDecoration(
            hintText: 'Enter reason for rejection',
            labelText: 'Rejection Reason',
          ),
          maxLines: 3,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final reason = _rejectionReasonController.text.trim();
              if (reason.isNotEmpty) {
                _rejectAsset(assetId, reason, creatorId);
                Navigator.pop(context);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Reject Asset'),
          ),
        ],
      ),
    );
  }

  // Reject asset
  Future<void> _rejectAsset(String assetId, String reason, String creatorId) async {
    try {
      await _adminService.rejectAsset(assetId, reason);

      // Send notification to user
      await _notificationService.sendNotification(
        userId: creatorId,
        title: 'Asset Rejected',
        body: 'Your asset has been rejected. Reason: $reason',
        type: 'asset_approval',
        data: {'assetId': assetId, 'status': 'rejected', 'reason': reason},
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Asset rejected successfully',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error rejecting asset: $e',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Show admin confirmation dialog
  Future<void> _showAdminConfirmationDialog(String userId, String userName, bool makeAdmin) async {
    // Check if verification is required
    final isVerificationRequired = await _securityService.isAdminVerificationRequired();

    if (isVerificationRequired) {
      // Generate a new verification code if needed
      final verificationCode = await _securityService.setupAdminVerification();

      // Show the verification code to the admin
      if (!mounted) return;

      await showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: Text(
            'Security Verification',
            style: GoogleFonts.poppins(
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'For security reasons, you need to verify your admin status before performing sensitive actions.',
                style: GoogleFonts.poppins(),
              ),
              const SizedBox(height: 16),
              Text(
                'Your verification code is:',
                style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  verificationCode,
                  style: GoogleFonts.robotoMono(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 4,
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'This code will be valid for 24 hours. Keep it secure and do not share it with anyone.',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),
          actions: [
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
              child: const Text('I\'ve Saved My Code'),
            ),
          ],
        ),
      );
    }

    // Now show the admin action confirmation dialog
    final TextEditingController codeController = TextEditingController();

    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          makeAdmin ? 'Make $userName Admin?' : 'Remove Admin Status from $userName?',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              makeAdmin
                ? 'This will grant $userName full administrative privileges. This is a security-sensitive action.'
                : 'This will remove administrative privileges from $userName.',
              style: GoogleFonts.poppins(),
            ),
            const SizedBox(height: 16),
            Text(
              'Enter your admin verification code to confirm:',
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: codeController,
              decoration: const InputDecoration(
                labelText: 'Admin Verification Code',
                border: OutlineInputBorder(),
              ),
              obscureText: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final enteredCode = codeController.text.trim();

              // Verify code synchronously to avoid BuildContext issues
              final isValid = await _securityService.verifyAdminCode(enteredCode);

              // Check if the widget is still mounted after the async operation
              if (!mounted) return;

              if (isValid) {
                // Pop the dialog
                if (context.mounted) {
                  Navigator.pop(context);
                }

                // Perform the admin action
                await _toggleAdminStatus(userId, makeAdmin);
              } else {
                // Show error message
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Invalid verification code'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: makeAdmin ? Colors.blue : Colors.grey,
              foregroundColor: Colors.white,
            ),
            child: Text(makeAdmin ? 'Make Admin' : 'Remove Admin'),
          ),
        ],
      ),
    );
  }



  // Toggle admin status
  Future<void> _toggleAdminStatus(String userId, bool isAdmin) async {
    try {
      // Use security service to log this action
      await _securityService.logAdminAction(
        action: isAdmin ? 'Make User Admin' : 'Remove Admin Status',
        targetId: userId,
        targetType: 'user',
        details: {
          'newStatus': isAdmin,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );

      // Update user's admin status
      await _adminService.makeUserAdmin(userId, isAdmin);

      // Send notification to user
      await _notificationService.sendNotification(
        userId: userId,
        title: isAdmin ? 'Admin Status Granted' : 'Admin Status Removed',
        body: isAdmin
            ? 'You have been granted administrative privileges.'
            : 'Your administrative privileges have been removed.',
        type: 'admin_status',
        data: {'isAdmin': isAdmin},
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isAdmin ? 'User is now an admin' : 'Admin status removed',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: isAdmin ? Colors.blue : Colors.grey,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error updating admin status: $e',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Show delete user dialog
  void _showDeleteUserDialog(String userId, String userName) {
    final TextEditingController confirmController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Delete User: $userName?',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
            color: Colors.red,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'This action is irreversible. All user data, assets, and posts will be permanently deleted.',
              style: GoogleFonts.poppins(),
            ),
            const SizedBox(height: 16),
            Text(
              'Type "DELETE" to confirm:',
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: confirmController,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              if (confirmController.text.trim() == 'DELETE') {
                Navigator.pop(context);
                _deleteUser(userId);
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please type DELETE to confirm'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete User'),
          ),
        ],
      ),
    );
  }

  // Delete user
  Future<void> _deleteUser(String userId) async {
    try {
      // Log this action
      await _firestore.collection('admin_activity_log').add({
        'action': 'Delete User',
        'targetUserId': userId,
        'performedBy': _auth.currentUser?.uid,
        'performedByEmail': _auth.currentUser?.email,
        'timestamp': FieldValue.serverTimestamp(),
      });

      // Delete user
      await _adminService.deleteUser(userId);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'User deleted successfully',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error deleting user: $e',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Show user activity log
  void _showUserActivityLog(String userId, String userName) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => UserActivityLogPage(userId: userId, userName: userName),
      ),
    );
  }
}

// User Activity Log Page
class UserActivityLogPage extends StatelessWidget {
  final String userId;
  final String userName;

  const UserActivityLogPage({
    Key? key,
    required this.userId,
    required this.userName,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          '$userName - Activity Log',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: const Color(0xFF6A11CB),
      ),
      body: StreamBuilder<QuerySnapshot>(
        stream: FirebaseFirestore.instance
            .collection('user_activity_log')
            .where('userId', isEqualTo: userId)
            .orderBy('timestamp', descending: true)
            .limit(100)
            .snapshots(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          if (snapshot.hasError) {
            return Center(
              child: Text(
                'Error loading activity log: ${snapshot.error}',
                style: GoogleFonts.poppins(color: Colors.red),
              ),
            );
          }

          if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
            return Center(
              child: Text(
                'No activity log found for this user',
                style: GoogleFonts.poppins(fontSize: 16),
              ),
            );
          }

          final activities = snapshot.data!.docs;

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: activities.length,
            itemBuilder: (context, index) {
              final activity = activities[index].data() as Map<String, dynamic>;
              final timestamp = (activity['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now();
              final action = activity['action'] as String? ?? 'Unknown Action';
              final details = activity['details'] as String? ?? '';

              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  leading: _getActivityIcon(action),
                  title: Text(
                    action,
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${_formatDate(timestamp)} at ${_formatTime(timestamp)}',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      if (details.isNotEmpty)
                        Text(
                          details,
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                          ),
                        ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  // Get icon for activity type
  Widget _getActivityIcon(String action) {
    IconData iconData;
    Color iconColor;

    if (action.contains('Login') || action.contains('Sign In')) {
      iconData = Icons.login;
      iconColor = Colors.green;
    } else if (action.contains('Logout') || action.contains('Sign Out')) {
      iconData = Icons.logout;
      iconColor = Colors.orange;
    } else if (action.contains('Upload') || action.contains('Asset')) {
      iconData = Icons.upload_file;
      iconColor = Colors.blue;
    } else if (action.contains('Profile')) {
      iconData = Icons.person;
      iconColor = Colors.purple;
    } else if (action.contains('Purchase') || action.contains('Payment')) {
      iconData = Icons.payment;
      iconColor = Colors.green;
    } else if (action.contains('Download')) {
      iconData = Icons.download;
      iconColor = Colors.teal;
    } else if (action.contains('Warning') || action.contains('Report')) {
      iconData = Icons.warning;
      iconColor = Colors.red;
    } else {
      iconData = Icons.info;
      iconColor = Colors.grey;
    }

    // Use a fixed light background color instead of trying to derive from the icon color
    return CircleAvatar(
      backgroundColor: Colors.grey[200],
      child: Icon(iconData, color: iconColor),
    );
  }

  // Format date
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // Format time
  String _formatTime(DateTime date) {
    return '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }



  // End of simple support requests tab


}
