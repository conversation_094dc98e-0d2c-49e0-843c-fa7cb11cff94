import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'models/feedback_model.dart';
import 'services/feedback_service.dart';

// Build feedback tab
Widget buildFeedbackTab(BuildContext context, FeedbackService feedbackService) {
  return StreamBuilder<List<FeedbackModel>>(
    stream: feedbackService.getAllFeedback(),
    builder: (context, snapshot) {
      if (snapshot.connectionState == ConnectionState.waiting) {
        return const Center(child: CircularProgressIndicator());
      }

      if (snapshot.hasError) {
        return Center(
          child: Text(
            'Error loading feedback: ${snapshot.error}',
            style: GoogleFonts.poppins(color: Colors.red),
          ),
        );
      }

      if (!snapshot.hasData || snapshot.data!.isEmpty) {
        return Center(
          child: Text(
            'No feedback found',
            style: GoogleFonts.poppins(fontSize: 16),
          ),
        );
      }

      final feedbackList = snapshot.data!;

      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'User Feedback',
              style: GoogleFonts.poppins(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Review and manage user feedback',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 16),

            // Feedback list
            Expanded(
              child: ListView.builder(
                itemCount: feedbackList.length,
                itemBuilder: (context, index) {
                  final feedback = feedbackList[index];
                  
                  return Card(
                    margin: const EdgeInsets.only(bottom: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                      side: BorderSide(
                        color: feedback.isRead ? Colors.grey.shade300 : Colors.blue.shade300,
                        width: feedback.isRead ? 1 : 2,
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              CircleAvatar(
                                backgroundImage: feedback.userPhotoUrl.isNotEmpty 
                                    ? NetworkImage(feedback.userPhotoUrl) 
                                    : null,
                                child: feedback.userPhotoUrl.isEmpty 
                                    ? Text(feedback.userName.isNotEmpty 
                                        ? feedback.userName[0].toUpperCase() 
                                        : 'A')
                                    : null,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      feedback.userName,
                                      style: GoogleFonts.poppins(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16,
                                      ),
                                    ),
                                    if (feedback.userEmail.isNotEmpty)
                                      Text(
                                        feedback.userEmail,
                                        style: GoogleFonts.poppins(
                                          fontSize: 12,
                                          color: Colors.grey[600],
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                              Text(
                                formatDate(feedback.timestamp),
                                style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 16),
                          if (feedback.category != null)
                            Chip(
                              label: Text(
                                feedback.category!,
                                style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  color: Colors.white,
                                ),
                              ),
                              backgroundColor: Colors.blue,
                            ),
                          const SizedBox(height: 8),
                          Text(
                            feedback.message,
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              if (!feedback.isRead)
                                TextButton.icon(
                                  icon: const Icon(Icons.check),
                                  label: const Text('Mark as Read'),
                                  onPressed: () {
                                    feedbackService.markAsRead(feedback.id);
                                  },
                                ),
                              const SizedBox(width: 8),
                              TextButton.icon(
                                icon: const Icon(Icons.delete, color: Colors.red),
                                label: const Text('Delete', style: TextStyle(color: Colors.red)),
                                onPressed: () {
                                  showDeleteFeedbackDialog(context, feedbackService, feedback);
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      );
    },
  );
}

// Format date for feedback
String formatDate(DateTime date) {
  final now = DateTime.now();
  final difference = now.difference(date);
  
  if (difference.inDays == 0) {
    if (difference.inHours == 0) {
      return '${difference.inMinutes} min ago';
    }
    return '${difference.inHours} hours ago';
  } else if (difference.inDays < 7) {
    return '${difference.inDays} days ago';
  } else {
    return '${date.day}/${date.month}/${date.year}';
  }
}

// Show delete feedback confirmation dialog
void showDeleteFeedbackDialog(BuildContext context, FeedbackService feedbackService, FeedbackModel feedback) {
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: Text(
        'Delete Feedback',
        style: GoogleFonts.poppins(
          fontWeight: FontWeight.bold,
        ),
      ),
      content: Text(
        'Are you sure you want to delete this feedback from ${feedback.userName}?',
        style: GoogleFonts.poppins(),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            'Cancel',
            style: GoogleFonts.poppins(),
          ),
        ),
        ElevatedButton(
          onPressed: () async {
            await feedbackService.deleteFeedback(feedback.id);
            if (context.mounted) {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Feedback deleted'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
          ),
          child: Text(
            'Delete',
            style: GoogleFonts.poppins(
              color: Colors.white,
            ),
          ),
        ),
      ],
    ),
  );
}
