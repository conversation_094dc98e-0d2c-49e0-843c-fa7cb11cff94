import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';

/// Widget for inputting custom wallet top-up amounts
class CustomAmountInputWidget extends StatefulWidget {
  final Function(double amount)? onAmountSelected;
  final double? initialAmount;
  final double minAmount;
  final double maxAmount;
  final String currency;

  const CustomAmountInputWidget({
    super.key,
    this.onAmountSelected,
    this.initialAmount,
    this.minAmount = 50.0,
    this.maxAmount = 10000.0,
    this.currency = '₹',
  });

  @override
  State<CustomAmountInputWidget> createState() => _CustomAmountInputWidgetState();
}

class _CustomAmountInputWidgetState extends State<CustomAmountInputWidget> {
  final TextEditingController _amountController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  double? _selectedAmount;
  String? _errorMessage;

  // Predefined quick amounts (INR)
  final List<double> _quickAmounts = [50, 100, 200, 500, 1000, 2000, 5000];

  @override
  void initState() {
    super.initState();
    if (widget.initialAmount != null) {
      _selectedAmount = widget.initialAmount;
      _amountController.text = widget.initialAmount!.toInt().toString();
    }
    _amountController.addListener(_onAmountChanged);
  }

  @override
  void dispose() {
    _amountController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onAmountChanged() {
    final text = _amountController.text;
    if (text.isEmpty) {
      setState(() {
        _selectedAmount = null;
        _errorMessage = null;
      });
      return;
    }

    final amount = double.tryParse(text);
    if (amount == null) {
      setState(() {
        _errorMessage = 'Please enter a valid amount';
        _selectedAmount = null;
      });
      return;
    }

    if (amount < widget.minAmount) {
      setState(() {
        _errorMessage = 'Minimum amount is ${widget.currency}${widget.minAmount.toInt()}';
        _selectedAmount = null;
      });
      return;
    }

    if (amount > widget.maxAmount) {
      setState(() {
        _errorMessage = 'Maximum amount is ${widget.currency}${widget.maxAmount.toInt()}';
        _selectedAmount = null;
      });
      return;
    }

    setState(() {
      _selectedAmount = amount;
      _errorMessage = null;
    });

    widget.onAmountSelected?.call(amount);
  }

  void _selectQuickAmount(double amount) {
    _amountController.text = amount.toInt().toString();
    _focusNode.unfocus();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.account_balance_wallet,
                color: const Color(0xFF6A11CB),
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Add Money to Wallet',
                  style: GoogleFonts.poppins(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Quick amount buttons
          Text(
            'Quick Select',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _quickAmounts.map((amount) => _buildQuickAmountButton(amount)).toList(),
          ),
          
          const SizedBox(height: 20),

          // Custom amount input
          Text(
            'Or Enter Custom Amount',
            style: GoogleFonts.poppins(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),

          // Amount input field
          Container(
            decoration: BoxDecoration(
              border: Border.all(
                color: _errorMessage != null ? Colors.red : Colors.grey[300]!,
                width: _errorMessage != null ? 2 : 1,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: TextField(
              controller: _amountController,
              focusNode: _focusNode,
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(6), // Max 6 digits
              ],
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
              decoration: InputDecoration(
                hintText: 'Enter amount',
                hintStyle: GoogleFonts.poppins(
                  fontSize: 16,
                  color: Colors.grey[500],
                ),
                prefixIcon: Container(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    widget.currency,
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF6A11CB),
                    ),
                  ),
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 16,
                ),
              ),
            ),
          ),

          // Error message
          if (_errorMessage != null) ...[
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: Colors.red,
              ),
            ),
          ],

          // Amount info
          if (_selectedAmount != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFF6A11CB).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: const Color(0xFF6A11CB),
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'You will add ${widget.currency}${_selectedAmount!.toInt()} to your wallet',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: const Color(0xFF6A11CB),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],

          // Amount range info
          const SizedBox(height: 12),
          Text(
            'Amount range: ${widget.currency}${widget.minAmount.toInt()} - ${widget.currency}${widget.maxAmount.toInt()}',
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickAmountButton(double amount) {
    final isSelected = _selectedAmount == amount;
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _selectQuickAmount(amount),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: isSelected ? const Color(0xFF6A11CB) : Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSelected ? const Color(0xFF6A11CB) : Colors.grey[300]!,
            ),
          ),
          child: Text(
            '${widget.currency}${amount.toInt()}',
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: isSelected ? Colors.white : Colors.black87,
            ),
          ),
        ),
      ),
    );
  }
}
