import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'models/challenge_model.dart';
import 'services/challenge_service.dart';
import 'challenge_submissions_page.dart';

class ChallengeDetailPage extends StatefulWidget {
  final String challengeId;
  final ChallengeModel? challenge;

  const ChallengeDetailPage({
    super.key,
    required this.challengeId,
    this.challenge,
  });

  @override
  State<ChallengeDetailPage> createState() => _ChallengeDetailPageState();
}

class _ChallengeDetailPageState extends State<ChallengeDetailPage> {
  final ChallengeService _challengeService = ChallengeService();
  ChallengeModel? _challenge;
  bool _isLoading = true;
  String? _errorMessage;
  Timer? _countdownTimer;
  Duration? _timeRemaining;

  @override
  void initState() {
    super.initState();
    _challenge = widget.challenge;
    if (_challenge == null) {
      _loadChallenge();
    } else {
      _isLoading = false;
      _startCountdownTimer();
    }
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    super.dispose();
  }

  void _startCountdownTimer() {
    if (_challenge == null) return;

    _updateTimeRemaining();
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        _updateTimeRemaining();
      } else {
        timer.cancel();
      }
    });
  }

  void _updateTimeRemaining() {
    if (_challenge == null) return;

    final now = DateTime.now();
    final deadline = _challenge!.deadline;

    if (now.isBefore(deadline)) {
      setState(() {
        _timeRemaining = deadline.difference(now);
      });

      // Check if deadline is approaching (24 hours or less)
      if (_timeRemaining!.inHours <= 24 && _timeRemaining!.inHours > 0) {
        _sendDeadlineNotification();
      }
    } else {
      setState(() {
        _timeRemaining = Duration.zero;
      });
      _countdownTimer?.cancel();
    }
  }

  void _sendDeadlineNotification() {
    // This would typically be handled by a background service
    // For now, we'll just check if the creator should be notified
    final currentUserId = FirebaseAuth.instance.currentUser?.uid;
    final isCreator = currentUserId == _challenge?.creatorId;

    if (isCreator && _challenge?.status == 'active') {
      // In a real app, this would be handled by cloud functions
      // For demo purposes, we'll show a local notification-style message
      debugPrint('Deadline approaching for challenge: ${_challenge?.title}');
    }
  }

  Future<void> _loadChallenge() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final challenge = await _challengeService.getChallengeById(widget.challengeId);
      if (challenge != null) {
        setState(() {
          _challenge = challenge;
          _isLoading = false;
        });
        _startCountdownTimer();
      } else {
        setState(() {
          _errorMessage = 'Challenge not found';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading challenge: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: Text('Challenge Details', style: GoogleFonts.poppins()),
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_errorMessage != null) {
      return Scaffold(
        appBar: AppBar(
          title: Text('Challenge Details', style: GoogleFonts.poppins()),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
              const SizedBox(height: 16),
              Text(
                _errorMessage!,
                style: GoogleFonts.poppins(fontSize: 16, color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadChallenge,
                child: Text('Retry', style: GoogleFonts.poppins()),
              ),
            ],
          ),
        ),
      );
    }

    final challenge = _challenge!;
    final currentUserId = FirebaseAuth.instance.currentUser?.uid;
    final isCreator = currentUserId == challenge.creatorId;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text('Challenge Details', style: GoogleFonts.poppins()),
        backgroundColor: Colors.white,
        elevation: 0,
        actions: [
          if (isCreator && challenge.isActive)
            PopupMenuButton<String>(
              onSelected: (value) {
                if (value == 'cancel') {
                  _showCancelDialog();
                }
              },
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'cancel',
                  child: Row(
                    children: [
                      const Icon(Icons.cancel, color: Colors.red),
                      const SizedBox(width: 8),
                      Text('Cancel Challenge', style: GoogleFonts.poppins()),
                    ],
                  ),
                ),
              ],
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildChallengeHeader(challenge),
            const SizedBox(height: 24),
            _buildChallengeDetails(challenge),
            const SizedBox(height: 24),
            _buildRequirements(challenge),
            const SizedBox(height: 24),
            _buildActionButtons(challenge, isCreator),
            const SizedBox(height: 100), // Space for floating action button
          ],
        ),
      ),
      floatingActionButton: _buildFloatingActionButton(challenge, isCreator),
    );
  }

  Widget _buildChallengeHeader(ChallengeModel challenge) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: _getCategoryColor(challenge.category).withAlpha(20),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  challenge.category,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: _getCategoryColor(challenge.category),
                  ),
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: challenge.isActive ? Colors.green.withAlpha(20) : Colors.grey.withAlpha(20),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  challenge.status.toUpperCase(),
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: challenge.isActive ? Colors.green : Colors.grey,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            challenge.title,
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            challenge.description,
            style: GoogleFonts.poppins(
              fontSize: 16,
              color: Colors.grey[600],
              height: 1.5,
            ),
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              _buildInfoChip(
                icon: Icons.monetization_on,
                label: '\$${challenge.prizeAmount.toStringAsFixed(0)}',
                color: Colors.green,
              ),
              const SizedBox(width: 12),
              _buildInfoChip(
                icon: Icons.people,
                label: '${challenge.submissionCount} submissions',
                color: Colors.blue,
              ),
              const SizedBox(width: 12),
              _buildCountdownChip(challenge),
            ],
          ),
        ],
      ),
    ).animate().fadeIn(duration: 500.ms).slideY(begin: 0.2, end: 0);
  }

  Widget _buildCountdownChip(ChallengeModel challenge) {
    if (_timeRemaining == null) {
      return _buildInfoChip(
        icon: Icons.schedule,
        label: '${challenge.daysRemaining} days left',
        color: challenge.daysRemaining <= 3 ? Colors.red : Colors.orange,
      );
    }

    final days = _timeRemaining!.inDays;
    final hours = _timeRemaining!.inHours % 24;
    final minutes = _timeRemaining!.inMinutes % 60;
    final seconds = _timeRemaining!.inSeconds % 60;

    String timeText;
    Color color;

    if (_timeRemaining!.inSeconds <= 0) {
      timeText = 'Expired';
      color = Colors.red;
    } else if (days > 0) {
      timeText = '${days}d ${hours}h left';
      color = days <= 3 ? Colors.red : Colors.orange;
    } else if (hours > 0) {
      timeText = '${hours}h ${minutes}m left';
      color = Colors.red;
    } else {
      timeText = '${minutes}m ${seconds}s left';
      color = Colors.red;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withAlpha(20),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.schedule, size: 16, color: color),
          const SizedBox(width: 4),
          Text(
            timeText,
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withAlpha(20),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChallengeDetails(ChallengeModel challenge) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Challenge Details',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          _buildDetailRow('Created by', challenge.creatorName),
          _buildDetailRow('Created on', _formatDate(challenge.createdAt)),
          _buildDetailRow('Deadline', _formatDate(challenge.deadline)),
          _buildDetailRow('Prize Pool', '\$${challenge.prizeAmount.toStringAsFixed(0)}'),
          if (challenge.tags.isNotEmpty) ...[
            const SizedBox(height: 12),
            Text(
              'Tags',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: challenge.tags.map((tag) => Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  tag,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.grey[700],
                  ),
                ),
              )).toList(),
            ),
          ],
        ],
      ),
    ).animate().fadeIn(duration: 500.ms, delay: 200.ms).slideY(begin: 0.2, end: 0);
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRequirements(ChallengeModel challenge) {
    if (challenge.requirements.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Requirements',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          ...challenge.requirements.asMap().entries.map((entry) {
            final index = entry.key;
            final requirement = entry.value;
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: const Color(0xFF4F46E5).withAlpha(20),
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Text(
                        '${index + 1}',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF4F46E5),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      requirement,
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.grey[700],
                        height: 1.4,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    ).animate().fadeIn(duration: 500.ms, delay: 400.ms).slideY(begin: 0.2, end: 0);
  }

  Widget _buildActionButtons(ChallengeModel challenge, bool isCreator) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Actions',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          if (isCreator) ...[
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ChallengeSubmissionsPage(
                        challengeId: challenge.id,
                        challenge: challenge,
                      ),
                    ),
                  );
                },
                icon: const Icon(Icons.list),
                label: Text(
                  'View Submissions (${challenge.submissionCount})',
                  style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF4F46E5),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ] else ...[
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: challenge.isActive ? () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ChallengeSubmissionsPage(
                        challengeId: challenge.id,
                        challenge: challenge,
                      ),
                    ),
                  );
                } : null,
                icon: const Icon(Icons.upload),
                label: Text(
                  challenge.isActive ? 'Submit Entry' : 'Challenge Ended',
                  style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF4F46E5),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ChallengeSubmissionsPage(
                        challengeId: challenge.id,
                        challenge: challenge,
                      ),
                    ),
                  );
                },
                icon: const Icon(Icons.visibility),
                label: Text(
                  'View All Submissions',
                  style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
                ),
                style: OutlinedButton.styleFrom(
                  foregroundColor: const Color(0xFF4F46E5),
                  side: const BorderSide(color: Color(0xFF4F46E5)),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    ).animate().fadeIn(duration: 500.ms, delay: 600.ms).slideY(begin: 0.2, end: 0);
  }

  Widget? _buildFloatingActionButton(ChallengeModel challenge, bool isCreator) {
    if (!challenge.isActive || isCreator) return null;

    return FloatingActionButton.extended(
      onPressed: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ChallengeSubmissionsPage(
              challengeId: challenge.id,
              challenge: challenge,
            ),
          ),
        );
      },
      icon: const Icon(Icons.upload),
      label: Text(
        'Submit Entry',
        style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
      ),
      backgroundColor: const Color(0xFF4F46E5),
      foregroundColor: Colors.white,
    );
  }

  void _showCancelDialog() async {
    final challenge = _challenge!;

    // Check if there are submissions
    final hasSubmissions = challenge.submissionCount > 0;

    if (hasSubmissions) {
      // Show error dialog if there are submissions
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Row(
            children: [
              const Icon(Icons.error, color: Colors.red),
              const SizedBox(width: 8),
              Text(
                'Cannot Cancel Challenge',
                style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          content: Text(
            'This challenge cannot be cancelled because it already has ${challenge.submissionCount} submission${challenge.submissionCount == 1 ? '' : 's'}. You can only cancel challenges with no submissions.',
            style: GoogleFonts.poppins(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('OK', style: GoogleFonts.poppins()),
            ),
          ],
        ),
      );
      return;
    }

    // Show confirmation dialog if no submissions
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.warning, color: Colors.orange),
            const SizedBox(width: 8),
            Text(
              'Cancel Challenge',
              style: GoogleFonts.poppins(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Are you sure you want to cancel this challenge?',
              style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withAlpha(20),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.withAlpha(50)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.monetization_on, color: Colors.green, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        'Full Refund Guaranteed',
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.w600,
                          color: Colors.green[800],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Since there are no submissions, your entire prize amount of \$${challenge.prizeAmount.toStringAsFixed(2)} will be transferred back to your Asatu wallet without any fee deduction.',
                    style: GoogleFonts.poppins(
                      fontSize: 13,
                      color: Colors.green[700],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'This action cannot be undone.',
              style: GoogleFonts.poppins(
                fontSize: 13,
                color: Colors.red[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Keep Challenge', style: GoogleFonts.poppins()),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _cancelChallenge();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(
              'Cancel Challenge',
              style: GoogleFonts.poppins(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _cancelChallenge() async {
    try {
      await _challengeService.cancelChallenge(widget.challengeId);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Challenge cancelled successfully',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error cancelling challenge: $e',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Color _getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'ui design':
      case 'ui/ux':
        return Colors.purple;
      case 'code implementation':
      case 'coding':
        return Colors.green;
      case 'bug fix':
        return Colors.red;
      case 'feature development':
        return Colors.blue;
      case 'performance optimization':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
