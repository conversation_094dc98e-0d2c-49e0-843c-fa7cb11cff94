import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'dart:io';
import 'dart:async';
import 'dart:math';
import 'package:uuid/uuid.dart';
import 'ai_moderation_service.dart';
import 'notification_service.dart';

/// A service class to handle messaging functionality
class MessagingService {
  // Singleton pattern
  static final MessagingService _instance = MessagingService._internal();
  factory MessagingService() => _instance;
  MessagingService._internal();

  // Firebase instances
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // Services
  final AIModerationService _moderationService = AIModerationService();
  final NotificationService _notificationService = NotificationService();

  // Get current user ID
  String? get currentUserId => _auth.currentUser?.uid;

  // Get all conversations for the current user
  Stream<List<Map<String, dynamic>>> getConversations() {
    if (currentUserId == null) {
      debugPrint('MessagingService - getConversations: currentUserId is null, returning empty list');
      return Stream.value([]);
    }

    debugPrint('MessagingService - getConversations: Querying for user $currentUserId');

    try {
      // Get conversations without ordering to avoid requiring a composite index
      // Add error handling for the stream
      return _firestore
          .collection('conversations')
          .where('participants', arrayContains: currentUserId)
          .snapshots()
          .handleError((error) {
            debugPrint('MessagingService - getConversations: Error in stream: $error');
            // Return an empty list on error
            return [];
          })
          .asyncMap((snapshot) async {
            debugPrint('MessagingService - getConversations: Got ${snapshot.docs.length} conversations');
            final List<Map<String, dynamic>> conversations = [];

            for (final doc in snapshot.docs) {
              try {
                final data = doc.data();
                final String conversationId = doc.id;

                debugPrint('MessagingService - Processing conversation: $conversationId with data: ${data.toString().substring(0, min(100, data.toString().length))}...');

                // Get the other participant's ID
                final List<String> participants = List<String>.from(data['participants'] ?? []);
                debugPrint('MessagingService - Participants: $participants');

                if (participants.isEmpty) {
                  debugPrint('MessagingService - Skipping conversation $conversationId: participants list is empty');
                  continue;
                }

                final String otherUserId = participants.firstWhere(
                  (id) => id != currentUserId,
                  orElse: () => '',
                );

                if (otherUserId.isEmpty) {
                  debugPrint('MessagingService - Skipping conversation $conversationId: otherUserId is empty');
                  continue;
                }

                // Get the other user's data
                debugPrint('MessagingService - Getting user data for: $otherUserId');
                final otherUserDoc = await _firestore.collection('users').doc(otherUserId).get();
                if (!otherUserDoc.exists) {
                  debugPrint('MessagingService - Skipping conversation $conversationId: otherUser document does not exist');
                  continue;
                }

                final otherUserData = otherUserDoc.data() ?? {};
                debugPrint('MessagingService - Got user data for: ${otherUserData['displayName']}');

                // Get unread count
                final int unreadCount = await _getUnreadCount(conversationId);

                // Check if user is online based on lastActive timestamp
                bool isOnline = false;
                final lastActive = otherUserData['lastActive'] as Timestamp?;
                if (lastActive != null) {
                  final now = Timestamp.now();
                  final difference = now.seconds - lastActive.seconds;
                  isOnline = difference < 300; // 5 minutes = 300 seconds
                }

                // Create conversation map
                final Map<String, dynamic> conversation = {
                  'id': conversationId,
                  'name': otherUserData['displayName'] ?? 'Unknown User',
                  'avatar': otherUserData['photoURL'] ?? '',
                  'lastMessage': data['lastMessage'] ?? '',
                  'time': _formatTimestamp(data['lastMessageTimestamp']),
                  'rawTimestamp': data['lastMessageTimestamp'], // Store the raw timestamp for sorting
                  'unread': unreadCount,
                  'isOnline': isOnline,
                  'type': data['type'] ?? 'normal',
                  'otherUserId': otherUserId,
                  'isNew': data['isNew'] ?? false,
                  'welcomeMessageSent': data['welcomeMessageSent'] ?? false,
                };

                // Add hire request specific data if applicable
                if (data['type'] == 'hire') {
                  conversation['status'] = data['status'] ?? 'pending';
                  conversation['project'] = data['project'] ?? '';
                  conversation['budget'] = data['budget'] ?? '';
                  conversation['duration'] = data['duration'] ?? '';
                  conversation['hireRequestId'] = data['hireRequestId'];
                }

                conversations.add(conversation);
                debugPrint('MessagingService - Added conversation: ${conversation['name']}');
              } catch (e) {
                debugPrint('MessagingService - Error processing conversation: $e');
                // Continue to next conversation
                continue;
              }
            }

            debugPrint('MessagingService - Sorting ${conversations.length} conversations by timestamp');

            // Sort conversations by timestamp in memory instead of using Firestore's orderBy
            conversations.sort((a, b) {
              // Try to use raw timestamps first
              final Timestamp? timestampA = a['rawTimestamp'] as Timestamp?;
              final Timestamp? timestampB = b['rawTimestamp'] as Timestamp?;

              // If both have timestamps, compare them (newest first)
              if (timestampA != null && timestampB != null) {
                return timestampB.compareTo(timestampA);
              }

              // If only one has a timestamp, prioritize that one
              if (timestampA != null) return -1;
              if (timestampB != null) return 1;

              // Fallback to string-based comparison if no timestamps available
              final String timeA = a['time'] as String? ?? '';
              final String timeB = b['time'] as String? ?? '';

              // Special case for "Just now" which should be first
              if (timeA == 'Just now' && timeB != 'Just now') return -1;
              if (timeB == 'Just now' && timeA != 'Just now') return 1;

              // Special case for time formats
              if (timeA.contains('m ago') && !timeB.contains('m ago')) return -1;
              if (timeB.contains('m ago') && !timeA.contains('m ago')) return 1;

              if (timeA.contains('h ago') && !timeB.contains('h ago') && !timeB.contains('m ago')) return -1;
              if (timeB.contains('h ago') && !timeA.contains('h ago') && !timeA.contains('m ago')) return 1;

              if (timeA.contains('d ago') && !timeB.contains('d ago') && !timeB.contains('h ago') && !timeB.contains('m ago')) return -1;
              if (timeB.contains('d ago') && !timeA.contains('d ago') && !timeA.contains('h ago') && !timeA.contains('m ago')) return 1;

              // If both are the same format, compare the numbers
              if (timeA.contains('m ago') && timeB.contains('m ago')) {
                final int minutesA = int.tryParse(timeA.split('m').first.trim()) ?? 0;
                final int minutesB = int.tryParse(timeB.split('m').first.trim()) ?? 0;
                return minutesA.compareTo(minutesB);
              }

              if (timeA.contains('h ago') && timeB.contains('h ago')) {
                final int hoursA = int.tryParse(timeA.split('h').first.trim()) ?? 0;
                final int hoursB = int.tryParse(timeB.split('h').first.trim()) ?? 0;
                return hoursA.compareTo(hoursB);
              }

              if (timeA.contains('d ago') && timeB.contains('d ago')) {
                final int daysA = int.tryParse(timeA.split('d').first.trim()) ?? 0;
                final int daysB = int.tryParse(timeB.split('d').first.trim()) ?? 0;
                return daysA.compareTo(daysB);
              }

              // Default to alphabetical sorting of the time strings (not ideal but better than nothing)
              return timeA.compareTo(timeB);
            });

            debugPrint('MessagingService - Returning ${conversations.length} sorted conversations');
            return conversations;
          });
    } catch (e) {
      debugPrint('MessagingService - Error in getConversations: $e');
      // Return an empty list in case of error
      return Stream.value([]);
    }
  }

  // Get messages for a specific conversation
  Stream<List<Map<String, dynamic>>> getMessages(String conversationId) {
    return _firestore
        .collection('conversations')
        .doc(conversationId)
        .collection('messages')
        .orderBy('timestamp', descending: false) // Oldest messages first
        .snapshots()
        .map((snapshot) {
          final List<Map<String, dynamic>> messages = [];
          debugPrint('MessagingService - getMessages: Got ${snapshot.docs.length} messages');

          for (final doc in snapshot.docs) {
            final data = doc.data();

            final Map<String, dynamic> message = {
              'id': doc.id,
              'sender': data['senderId'],
              'text': data['text'] ?? '',
              'time': _formatMessageTime(data['timestamp']),
              'timestamp': data['timestamp'],
              'isRead': data['isRead'] ?? false,
              'isDelivered': data['isDelivered'] ?? true, // Default to true for backward compatibility
              'type': data['type'] ?? 'text',
              'flagged': data['flagged'] ?? false,
              'flagReason': data['flagReason'],
              'flagSeverity': data['flagSeverity'],
            };

            // Add reply information if this is a reply
            if (data.containsKey('replyTo')) {
              message['replyTo'] = data['replyTo'];
              debugPrint('MessagingService - Found reply information in message: ${doc.id}');
            }

            // Add custom offer data if present
            if (data['isCustomOffer'] == true) {
              message['isCustomOffer'] = true;
              message['offerPrice'] = data['offerPrice'];
              message['offerDeliveryTime'] = data['offerDeliveryTime'];
              message['offerStatus'] = data['offerStatus'];
            }

            // Add hire request data if present
            if (data['isHireRequest'] == true) {
              message['isHireRequest'] = true;
              message['planName'] = data['planName'];
              message['projectName'] = data['projectName'];
              message['deadline'] = data['deadline'];
              message['budget'] = data['budget'];
              message['status'] = data['status'] ?? 'pending';
            }

            // Add file URL if message has attachment
            if (data['fileUrl'] != null) {
              message['fileUrl'] = data['fileUrl'];
              message['fileName'] = data['fileName'];
              message['fileType'] = data['fileType'];
            }

            // Add system message data if present
            if (data['type'] == 'system') {
              message['systemAction'] = data['systemAction'];
              if (data['systemAction'] == 'hire_request_declined') {
                message['declineReason'] = data['declineReason'];
              }
            }

            messages.add(message);
          }

          // Mark messages as read
          markMessagesAsRead(conversationId);

          return messages;
        });
  }

  // Send a message
  Future<Map<String, dynamic>> sendMessage({
    required String conversationId,
    required String text,
    File? file,
    String? receiverId,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      if (currentUserId == null) {
        debugPrint('MessagingService - Error: User not authenticated');
        return {'success': false, 'error': 'User not authenticated'};
      }

      debugPrint('MessagingService - Sending message to ${receiverId ?? 'unknown'}, conversation: ${conversationId.isEmpty ? 'new' : conversationId}');

      // Create or get conversation
      String actualConversationId = conversationId;
      if (conversationId.isEmpty && receiverId != null) {
        debugPrint('MessagingService - Creating new conversation with user: $receiverId');

        // Check if conversation already exists
        final QuerySnapshot existingConversations = await _firestore
            .collection('conversations')
            .where('participants', arrayContains: currentUserId)
            .get();

        // Look for a conversation with the receiver
        for (final doc in existingConversations.docs) {
          final List<dynamic> participants = doc.get('participants') as List<dynamic>;
          if (participants.contains(receiverId)) {
            actualConversationId = doc.id;
            debugPrint('MessagingService - Found existing conversation: $actualConversationId');
            break;
          }
        }

        // If no existing conversation found, create a new one
        if (actualConversationId.isEmpty) {
          debugPrint('MessagingService - No existing conversation found, creating new one');

          // Check if this is a welcome message
          final bool isWelcomeMessage = text.trim() == 'Hello! I would like to connect with you.';

          final newConversationRef = await _firestore.collection('conversations').add({
            'participants': [currentUserId, receiverId],
            'createdAt': FieldValue.serverTimestamp(),
            'lastMessageTimestamp': FieldValue.serverTimestamp(),
            'lastMessage': text,
            'type': 'normal',
            'unreadCount': {
              receiverId: 1,
              currentUserId: 0,
            },
            'isNew': false, // Set isNew to false to prevent welcome message from being sent again
            'welcomeMessageSent': isWelcomeMessage, // Flag to track if welcome message was sent
          });

          actualConversationId = newConversationRef.id;
          debugPrint('MessagingService - Created new conversation: $actualConversationId');
        }
      }

      // Check for moderation flags
      final moderationResult = await _moderationService.moderateMessage(text);
      debugPrint('MessagingService - Moderation result: ${moderationResult['flagged'] ? 'Flagged' : 'Clean'}');

      // Upload file if provided
      String? fileUrl;
      String? fileName;
      String? fileType;

      if (file != null) {
        debugPrint('MessagingService - Uploading file for message');
        final uploadResult = await _uploadFile(file, actualConversationId);
        fileUrl = uploadResult['fileUrl'];
        fileName = uploadResult['fileName'];
        fileType = uploadResult['fileType'];
        debugPrint('MessagingService - File uploaded: $fileName');
      }

      // Create message document
      final messageData = {
        'senderId': currentUserId,
        'text': text,
        'timestamp': FieldValue.serverTimestamp(),
        'isRead': false,
        'isDelivered': false, // Initially set to false, will be updated when delivered
        'type': file != null ? 'file' : 'text',
      };

      // Add file data if available
      if (fileUrl != null) {
        messageData['fileUrl'] = fileUrl;
        messageData['fileName'] = fileName;
        messageData['fileType'] = fileType;
      }

      // Add any additional data provided
      if (additionalData != null) {
        messageData.addAll(additionalData);
      }

      // Add moderation flags if message is flagged
      if (moderationResult['flagged']) {
        messageData['flagged'] = true;
        messageData['flagReason'] = moderationResult['reason'];
        messageData['flagSeverity'] = moderationResult['severity'];

        // Report to admin if moderate or severe
        if (moderationResult['severity'] != 'minor') {
          _reportFlaggedMessageToAdmin(
            actualConversationId,
            text,
            moderationResult['reason'],
            moderationResult['severity'],
            receiverId,
          );
        }
      }

      // Use a transaction to ensure atomicity
      return await _firestore.runTransaction<Map<String, dynamic>>(
        (transaction) async {
          // Get the conversation document
          final DocumentSnapshot conversationDoc =
              await transaction.get(_firestore.collection('conversations').doc(actualConversationId));

          // Create a new message reference
          final DocumentReference messageRef = _firestore
              .collection('conversations')
              .doc(actualConversationId)
              .collection('messages')
              .doc();

          // Add message to conversation
          transaction.set(messageRef, messageData);

          // Update conversation with last message
          Map<String, dynamic> updateData = {
            'lastMessage': text,
            'lastMessageTimestamp': FieldValue.serverTimestamp(),
          };

          // Update unread count for receiver
          if (receiverId != null) {
            if (conversationDoc.exists) {
              final Map<String, dynamic> data = conversationDoc.data() as Map<String, dynamic>;
              Map<String, dynamic> unreadCount;

              // Handle different types of unreadCount
              if (data.containsKey('unreadCount') && data['unreadCount'] is Map) {
                unreadCount = Map<String, dynamic>.from(data['unreadCount']);
              } else {
                // If unreadCount is not a map or doesn't exist, create a new one
                unreadCount = {};
              }

              // Always use string keys for consistency
              final String receiverIdStr = receiverId.toString();
              final String currentUserIdStr = currentUserId.toString();

              // Increment unread count for receiver
              unreadCount[receiverIdStr] = (unreadCount[receiverIdStr] ?? 0) + 1;

              // Ensure current user's unread count exists (set to 0 if not already set)
              if (!unreadCount.containsKey(currentUserIdStr)) {
                unreadCount[currentUserIdStr] = 0;
              }

              updateData['unreadCount'] = unreadCount;

              debugPrint('MessagingService - Updated unread count for receiver $receiverIdStr: ${unreadCount[receiverIdStr]}');
            } else {
              // New conversation - always use string keys
              updateData['unreadCount'] = {
                receiverId.toString(): 1,
                currentUserId.toString(): 0,
              };

              debugPrint('MessagingService - Created new unread count map for new conversation');
            }
          }

          transaction.update(
            _firestore.collection('conversations').doc(actualConversationId),
            updateData
          );

          debugPrint('MessagingService - Message added to conversation: ${messageRef.id}');

          // Send notification to receiver
          if (receiverId != null) {
            // Get sender name for notification
            final DocumentSnapshot senderDoc =
                await _firestore.collection('users').doc(currentUserId).get();

            String senderName = 'Someone';
            if (senderDoc.exists) {
              final Map<String, dynamic> senderData = senderDoc.data() as Map<String, dynamic>;
              senderName = senderData['displayName'] ?? 'Someone';
            }

            // Send notification outside transaction (not critical for data consistency)
            _notificationService.sendNotification(
              userId: receiverId,
              title: 'New Message from $senderName',
              body: text.length > 50 ? '${text.substring(0, 47)}...' : text,
              type: 'message',
              data: {
                'conversationId': actualConversationId,
                'senderId': currentUserId,
                'senderName': senderName,
              },
            );

            // Update the receiver's unread message count in their user document
            try {
              final DocumentSnapshot receiverDoc = await _firestore.collection('users').doc(receiverId).get();
              if (receiverDoc.exists) {
                final Map<String, dynamic> receiverData = receiverDoc.data() as Map<String, dynamic>;

                // Get current unread counts
                int unreadMessageCount = 0;
                int unreadConversationCount = 0;

                if (receiverData.containsKey('unreadMessageCount') && receiverData['unreadMessageCount'] is num) {
                  unreadMessageCount = (receiverData['unreadMessageCount'] as num).toInt();
                }

                if (receiverData.containsKey('unreadConversationCount') && receiverData['unreadConversationCount'] is num) {
                  unreadConversationCount = (receiverData['unreadConversationCount'] as num).toInt();
                }

                // Increment counts
                unreadMessageCount += 1;

                // Check if this is the first unread message in this conversation
                final DocumentSnapshot conversationDoc = await _firestore
                    .collection('conversations')
                    .doc(actualConversationId)
                    .get();

                if (conversationDoc.exists) {
                  final Map<String, dynamic> conversationData = conversationDoc.data() as Map<String, dynamic>;
                  final Map<String, dynamic>? unreadCountMap =
                      conversationData['unreadCount'] is Map ?
                      Map<String, dynamic>.from(conversationData['unreadCount']) : null;

                  // If this is the first unread message (count was 0 before), increment conversation count
                  if (unreadCountMap != null &&
                      unreadCountMap.containsKey(receiverId.toString()) &&
                      (unreadCountMap[receiverId.toString()] as num).toInt() == 1) {
                    unreadConversationCount += 1;
                  }
                }

                // Update the receiver's user document
                await _firestore.collection('users').doc(receiverId).update({
                  'unreadMessageCount': unreadMessageCount,
                  'unreadConversationCount': unreadConversationCount,
                });

                debugPrint('MessagingService - Updated receiver unread counts: messages=$unreadMessageCount, conversations=$unreadConversationCount');
              }
            } catch (e) {
              debugPrint('MessagingService - Error updating receiver unread counts: $e');
            }

            debugPrint('MessagingService - Notification sent to user: $receiverId');
          }

          return {
            'success': true,
            'messageId': messageRef.id,
            'conversationId': actualConversationId,
            'flagged': moderationResult['flagged'],
          };
        },
        timeout: const Duration(seconds: 10),
      );
    } catch (e) {
      debugPrint('MessagingService - Error sending message: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  // Upload a file to Firebase Storage
  Future<Map<String, dynamic>> _uploadFile(File file, String conversationId) async {
    try {
      final String fileName = file.path.split('/').last;
      final String fileExtension = fileName.contains('.') ? fileName.split('.').last : '';
      final String fileType = _getFileType(fileExtension);

      // Generate a unique file name
      final String uniqueFileName = '${const Uuid().v4()}.$fileExtension';

      // Create reference to the file location
      final Reference ref = _storage
          .ref()
          .child('conversations')
          .child(conversationId)
          .child(uniqueFileName);

      // Upload file
      await ref.putFile(file);

      // Get download URL
      final String downloadUrl = await ref.getDownloadURL();

      return {
        'fileUrl': downloadUrl,
        'fileName': fileName,
        'fileType': fileType,
      };
    } catch (e) {
      debugPrint('Error uploading file: $e');
      rethrow;
    }
  }

  // Get file type based on extension
  String _getFileType(String extension) {
    final imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    final documentExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];
    final videoExtensions = ['mp4', 'mov', 'avi', 'wmv', 'flv', 'webm'];
    final audioExtensions = ['mp3', 'wav', 'ogg', 'm4a', 'aac'];

    final lowerExt = extension.toLowerCase();

    if (imageExtensions.contains(lowerExt)) return 'image';
    if (documentExtensions.contains(lowerExt)) return 'document';
    if (videoExtensions.contains(lowerExt)) return 'video';
    if (audioExtensions.contains(lowerExt)) return 'audio';

    return 'other';
  }

  // Get unread count for a conversation
  Future<int> _getUnreadCount(String conversationId) async {
    if (currentUserId == null) return 0;

    debugPrint('MessagingService - _getUnreadCount: Getting unread count for conversation $conversationId');

    try {
      // First check if the conversation document has an unreadCount map
      final DocumentSnapshot conversationDoc = await _firestore
          .collection('conversations')
          .doc(conversationId)
          .get();

      if (conversationDoc.exists) {
        final data = conversationDoc.data() as Map<String, dynamic>;

        if (data.containsKey('unreadCount') && data['unreadCount'] is Map) {
          final Map<String, dynamic> unreadCountMap = Map<String, dynamic>.from(data['unreadCount']);
          final String currentUserIdStr = currentUserId.toString();

          if (unreadCountMap.containsKey(currentUserIdStr)) {
            final dynamic countValue = unreadCountMap[currentUserIdStr];
            if (countValue is int) {
              debugPrint('MessagingService - _getUnreadCount: Found unread count in map: $countValue');
              return countValue;
            } else if (countValue is num) {
              debugPrint('MessagingService - _getUnreadCount: Found unread count in map (num): ${countValue.toInt()}');
              return countValue.toInt();
            }
          }
        }
      }

      // If no unreadCount map or value found, count unread messages directly
      final QuerySnapshot snapshot = await _firestore
          .collection('conversations')
          .doc(conversationId)
          .collection('messages')
          .where('senderId', isNotEqualTo: currentUserId)
          .where('isRead', isEqualTo: false)
          .get();

      final int count = snapshot.docs.length;
      debugPrint('MessagingService - _getUnreadCount: Counted unread messages directly: $count');

      // Update the conversation document with the accurate count
      if (conversationDoc.exists && count > 0) {
        try {
          final data = conversationDoc.data() as Map<String, dynamic>;
          Map<String, dynamic> unreadCountMap;

          if (data.containsKey('unreadCount') && data['unreadCount'] is Map) {
            unreadCountMap = Map<String, dynamic>.from(data['unreadCount']);
          } else {
            unreadCountMap = {};
          }

          unreadCountMap[currentUserId.toString()] = count;

          await _firestore.collection('conversations').doc(conversationId).update({
            'unreadCount': unreadCountMap,
          });

          debugPrint('MessagingService - _getUnreadCount: Updated unreadCount map with accurate count: $count');
        } catch (e) {
          debugPrint('MessagingService - _getUnreadCount: Error updating unreadCount map: $e');
        }
      }

      return count;
    } catch (e) {
      debugPrint('MessagingService - _getUnreadCount: Error getting unread count: $e');
      return 0;
    }
  }

  // Get total unread message count across all conversations
  Stream<int> getTotalUnreadMessageCount() {
    if (currentUserId == null) {
      return Stream.value(0);
    }

    debugPrint('MessagingService - getTotalUnreadMessageCount: Getting unread count for user $currentUserId');

    // Use a more reliable approach by directly checking conversations
    return _firestore
        .collection('conversations')
        .where('participants', arrayContains: currentUserId)
        .snapshots()
        .map((snapshot) {
          int unreadConversations = 0;

          for (final doc in snapshot.docs) {
            final data = doc.data();
            bool isUnread = false;

            // Check if the conversation has an unreadCount map
            if (data.containsKey('unreadCount')) {
              try {
                // Handle different types of unreadCount
                if (data['unreadCount'] is Map) {
                  final Map<String, dynamic> unreadCountMap = Map<String, dynamic>.from(data['unreadCount']);
                  final String currentUserIdStr = currentUserId.toString();

                  // Check for unread messages for the current user
                  if ((unreadCountMap.containsKey(currentUserIdStr) &&
                       unreadCountMap[currentUserIdStr] is num &&
                       (unreadCountMap[currentUserIdStr] as num) > 0) ||
                      (unreadCountMap.containsKey(currentUserId) &&
                       unreadCountMap[currentUserId] is num &&
                       (unreadCountMap[currentUserId] as num) > 0)) {
                    isUnread = true;
                  }
                } else if (data['unreadCount'] is int && data['unreadCount'] > 0) {
                  // Legacy format: single integer for unread count
                  isUnread = true;
                }
              } catch (e) {
                debugPrint('MessagingService - getTotalUnreadMessageCount: Error processing unread count: $e');
              }
            }

            // Also check for hire requests
            if (data['type'] == 'hire' && data['status'] == 'pending') {
              // For hire requests, check if this user is the developer (recipient)
              if (data['developerId'] == currentUserId) {
                // Check if this is a new hire request that hasn't been seen
                if (data['isNewHireRequest'] == true) {
                  isUnread = true;
                }
              }
            }

            if (isUnread) {
              unreadConversations += 1;
            }
          }

          debugPrint('MessagingService - getTotalUnreadMessageCount: Found $unreadConversations unread conversations');
          return unreadConversations;
        });
  }

  // Mark messages as read (public method)
  Future<void> markMessagesAsRead(String conversationId) async {
    if (currentUserId == null) return;

    debugPrint('MessagingService - markMessagesAsRead: Marking messages as read for conversation $conversationId, user $currentUserId');

    try {
      // First, get the conversation document to check if it exists
      final DocumentSnapshot conversationDocCheck = await _firestore
          .collection('conversations')
          .doc(conversationId)
          .get();

      if (!conversationDocCheck.exists) {
        debugPrint('MessagingService - markMessagesAsRead: Conversation document not found');
        return;
      }

      // Get all unread messages from other users before the transaction
      final QuerySnapshot unreadSnapshot = await _firestore
          .collection('conversations')
          .doc(conversationId)
          .collection('messages')
          .where('senderId', isNotEqualTo: currentUserId)
          .where('isRead', isEqualTo: false)
          .get();

      final int unreadCount = unreadSnapshot.docs.length;
      debugPrint('MessagingService - markMessagesAsRead: Found $unreadCount unread messages');

      // If there are no unread messages, just update the timestamp
      if (unreadCount == 0) {
        debugPrint('MessagingService - markMessagesAsRead: No unread messages to mark as read');

        // Still update the timestamp to force UI refresh
        await _firestore.collection('users').doc(currentUserId).update({
          'lastMessageReadAt': FieldValue.serverTimestamp(),
        });

        return;
      }

      // Use a batch to mark all messages as read
      final WriteBatch batch = _firestore.batch();

      // Mark all messages as read and delivered
      for (final doc in unreadSnapshot.docs) {
        batch.update(doc.reference, {
          'isRead': true,
          'isDelivered': true,
          'readAt': FieldValue.serverTimestamp(),
        });
      }

      // Get the conversation document
      final DocumentReference conversationRef = _firestore.collection('conversations').doc(conversationId);

      // Update unreadCount map in the conversation document
      final data = conversationDocCheck.data() as Map<String, dynamic>;
      Map<String, dynamic> unreadCountMap = {};

      if (data.containsKey('unreadCount') && data['unreadCount'] is Map) {
        unreadCountMap = Map<String, dynamic>.from(data['unreadCount']);
      }

      // Reset unread count only for current user
      unreadCountMap[currentUserId.toString()] = 0;

      // Update conversation document - only update the unreadCount map and timestamps
      batch.update(conversationRef, {
        'unreadCount': unreadCountMap,
        'lastReadAt': FieldValue.serverTimestamp(),
        'lastUpdated': FieldValue.serverTimestamp(),
      });

      // Commit the batch
      await batch.commit();
      debugPrint('MessagingService - markMessagesAsRead: Batch committed successfully');

      // After batch completes, update the user's total unread counts
      await updateUserUnreadMessageCount();

      // Force UI refresh by updating a field in the user document with a unique timestamp
      // Use millisecondsSinceEpoch to ensure it's always different and triggers UI updates
      await _firestore.collection('users').doc(currentUserId).update({
        'lastMessageReadAt': FieldValue.serverTimestamp(),
        'lastUnreadCountRefresh': DateTime.now().millisecondsSinceEpoch,
      });

      debugPrint('MessagingService - markMessagesAsRead: Successfully marked messages as read');
    } catch (e) {
      debugPrint('MessagingService - markMessagesAsRead: Error marking messages as read: $e');
    }
  }

  // Update the user's total unread message count in their user document (public method)
  Future<void> updateUserUnreadMessageCount() async {
    if (currentUserId == null) return;

    try {
      debugPrint('MessagingService - updateUserUnreadMessageCount: Updating unread counts for user $currentUserId');

      // Get all conversations for the current user directly (no transaction)
      final QuerySnapshot conversationsSnapshot = await _firestore
          .collection('conversations')
          .where('participants', arrayContains: currentUserId)
          .get();

      debugPrint('MessagingService - updateUserUnreadMessageCount: Found ${conversationsSnapshot.docs.length} conversations');

      // Calculate total unread count
      int totalUnread = 0;
      int unreadConversations = 0;

      // Process each conversation
      for (final doc in conversationsSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        int conversationUnreadCount = 0;

        // Check for unreadCount map (preferred method)
        if (data.containsKey('unreadCount') && data['unreadCount'] is Map) {
          final Map<String, dynamic> unreadCountMap = Map<String, dynamic>.from(data['unreadCount']);
          final String currentUserIdStr = currentUserId.toString();

          if (unreadCountMap.containsKey(currentUserIdStr)) {
            final dynamic countValue = unreadCountMap[currentUserIdStr];
            if (countValue is int) {
              conversationUnreadCount = countValue;
            } else if (countValue is num) {
              conversationUnreadCount = countValue.toInt();
            } else if (countValue is String) {
              conversationUnreadCount = int.tryParse(countValue) ?? 0;
            }
          }
        }
        // Check legacy unread field
        else if (data.containsKey('unread') && data['unread'] is num) {
          conversationUnreadCount = (data['unread'] as num).toInt();
        }

        // If conversation has unread messages, update counts
        if (conversationUnreadCount > 0) {
          totalUnread += conversationUnreadCount;
          unreadConversations += 1;
          debugPrint('MessagingService - _updateUserUnreadMessageCount: Conversation ${doc.id} has $conversationUnreadCount unread messages');
        }
      }

      // Also check for hire requests that need to be counted
      final QuerySnapshot hireRequestsSnapshot = await _firestore
          .collection('conversations')
          .where('participants', arrayContains: currentUserId)
          .where('type', isEqualTo: 'hire')
          .where('status', isEqualTo: 'pending')
          .get();

      debugPrint('MessagingService - _updateUserUnreadMessageCount: Found ${hireRequestsSnapshot.docs.length} pending hire requests');

      // Count hire requests where this user is the developer
      for (final doc in hireRequestsSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        if (data['developerId'] == currentUserId && data['isNewHireRequest'] == true) {
          unreadConversations++;
          totalUnread++; // Count each hire request as 1 unread message
          debugPrint('MessagingService - _updateUserUnreadMessageCount: Found unread hire request ${doc.id}');
        }
      }

      // Update the user document with the new counts
      await _firestore.collection('users').doc(currentUserId).update({
        'unreadMessageCount': totalUnread,
        'unreadConversationCount': unreadConversations,
        'unreadNotifications': totalUnread, // For compatibility with other parts of the app
        'lastUnreadCountUpdate': FieldValue.serverTimestamp(),
      });

      debugPrint('MessagingService - _updateUserUnreadMessageCount: Updated user document with totalUnread=$totalUnread, unreadConversations=$unreadConversations');

      // Force UI refresh by updating a field in the user document with a unique timestamp
      await _firestore.collection('users').doc(currentUserId).update({
        'lastUnreadCountRefresh': DateTime.now().millisecondsSinceEpoch,
      });

      debugPrint('MessagingService - _updateUserUnreadMessageCount: Forced UI refresh with timestamp update');
    } catch (e) {
      debugPrint('MessagingService - _updateUserUnreadMessageCount: Error updating unread count: $e');
    }
  }

  // Format timestamp for display
  String _formatTimestamp(dynamic timestamp) {
    if (timestamp == null) return 'Just now';

    final DateTime dateTime = timestamp is Timestamp
        ? timestamp.toDate()
        : DateTime.now();

    final DateTime now = DateTime.now();
    final Duration difference = now.difference(dateTime);

    if (difference.inDays > 7) {
      // Format as date
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    } else if (difference.inDays > 0) {
      // Format as days ago
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      // Format as hours ago
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      // Format as minutes ago
      return '${difference.inMinutes}m ago';
    } else {
      // Just now
      return 'Just now';
    }
  }

  // Format message time
  String _formatMessageTime(dynamic timestamp) {
    if (timestamp == null) return '';

    final DateTime dateTime = timestamp is Timestamp
        ? timestamp.toDate()
        : DateTime.now();

    // Format as HH:MM
    final String hour = dateTime.hour.toString().padLeft(2, '0');
    final String minute = dateTime.minute.toString().padLeft(2, '0');

    return '$hour:$minute';
  }

  // Delete a conversation
  Future<Map<String, dynamic>> deleteConversation(String conversationId) async {
    try {
      if (currentUserId == null) {
        return {'success': false, 'error': 'User not authenticated'};
      }

      debugPrint('MessagingService - deleteConversation: Deleting conversation $conversationId');

      // First, get the conversation to check if the current user is a participant
      final DocumentSnapshot conversationDoc = await _firestore
          .collection('conversations')
          .doc(conversationId)
          .get();

      if (!conversationDoc.exists) {
        return {'success': false, 'error': 'Conversation not found'};
      }

      final data = conversationDoc.data() as Map<String, dynamic>;
      final List<dynamic> participants = data['participants'] ?? [];

      // Check if the current user is a participant
      if (!participants.contains(currentUserId)) {
        return {'success': false, 'error': 'You are not a participant in this conversation'};
      }

      // Get all messages in the conversation
      final QuerySnapshot messagesSnapshot = await _firestore
          .collection('conversations')
          .doc(conversationId)
          .collection('messages')
          .get();

      // Create a batch to delete all messages
      final WriteBatch batch = _firestore.batch();
      for (final doc in messagesSnapshot.docs) {
        batch.delete(doc.reference);
      }

      // Delete the conversation document
      batch.delete(_firestore.collection('conversations').doc(conversationId));

      // Commit the batch
      await batch.commit();

      // Update the user's unread message count
      await updateUserUnreadMessageCount();

      debugPrint('MessagingService - deleteConversation: Successfully deleted conversation $conversationId with ${messagesSnapshot.docs.length} messages');

      return {'success': true};
    } catch (e) {
      debugPrint('MessagingService - deleteConversation: Error deleting conversation: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  // Public method to report flagged message to admin
  Future<void> reportFlaggedMessage({
    required String conversationId,
    required String messageText,
    required String reason,
    required String severity,
    String? receiverId,
  }) async {
    await _reportFlaggedMessageToAdmin(
      conversationId,
      messageText,
      reason,
      severity,
      receiverId,
    );
  }

  // Report flagged message to admin (internal implementation)
  Future<void> _reportFlaggedMessageToAdmin(
    String conversationId,
    String messageText,
    String reason,
    String severity,
    String? receiverId,
  ) async {
    try {
      if (currentUserId == null) return;

      // Get user data for both participants
      final senderDoc = await _firestore.collection('users').doc(currentUserId).get();
      final receiverDoc = receiverId != null
          ? await _firestore.collection('users').doc(receiverId).get()
          : null;

      final senderData = senderDoc.data() ?? {};
      final receiverData = receiverDoc?.data() ?? {};

      // Create flagged conversation report
      await _firestore.collection('flagged_conversations').add({
        'conversationId': conversationId,
        'messageText': messageText,
        'flagReason': reason,
        'flagSeverity': severity,
        'timestamp': FieldValue.serverTimestamp(),
        'status': 'pending', // pending, dismissed, warned, suspended, banned
        'sender': {
          'id': currentUserId,
          'name': senderData['displayName'] ?? 'Unknown',
          'email': senderData['email'] ?? '',
        },
        'receiver': receiverId != null ? {
          'id': receiverId,
          'name': receiverData['displayName'] ?? 'Unknown',
          'email': receiverData['email'] ?? '',
        } : null,
        'adminFeedback': null,
        'adminActionTimestamp': null,
        'adminId': null,
      });

      // Notify admins
      final adminSnapshot = await _firestore
          .collection('users')
          .where('isAdmin', isEqualTo: true)
          .get();

      for (final adminDoc in adminSnapshot.docs) {
        await _notificationService.sendNotification(
          userId: adminDoc.id,
          title: 'Flagged Conversation',
          body: 'A conversation has been flagged for $reason (${severity.toUpperCase()})',
          type: 'flagged_conversation',
          data: {
            'conversationId': conversationId,
            'severity': severity,
          },
        );
      }
    } catch (e) {
      debugPrint('Error reporting flagged message: $e');
    }
  }
}
