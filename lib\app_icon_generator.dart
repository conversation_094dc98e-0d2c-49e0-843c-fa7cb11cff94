import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';

/// This is a utility class to generate app icons programmatically
/// Run this file separately to generate the icon files
void main() async {
  // Create a 1024x1024 icon (standard size for app stores)
  final recorder = ui.PictureRecorder();
  final canvas = Canvas(recorder);
  
  // Draw the icon
  const size = Size(1024, 1024);
  drawAppIcon(canvas, size);
  
  // Convert to an image
  final picture = recorder.endRecording();
  final img = await picture.toImage(size.width.toInt(), size.height.toInt());
  final byteData = await img.toByteData(format: ui.ImageByteFormat.png);
  
  if (byteData != null) {
    final buffer = byteData.buffer.asUint8List();
    
    // Save the main icon
    final mainIconFile = File('assets/icons/app_icon.png');
    await mainIconFile.writeAsBytes(buffer);
    
    // Save the foreground icon (for adaptive icons)
    final foregroundIconFile = File('assets/icons/app_icon_foreground.png');
    await foregroundIconFile.writeAsBytes(buffer);
    
    print('Icons generated successfully!');
  } else {
    print('Failed to generate icon.');
  }
}

/// Draws our custom app icon that matches the splash screen logo
void drawAppIcon(Canvas canvas, Size size) {
  final center = Offset(size.width / 2, size.height / 2);
  final radius = size.width * 0.45; // Slightly smaller than half width
  
  // Fill the background with white
  final bgPaint = Paint()
    ..color = Colors.white
    ..style = PaintingStyle.fill;
  canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), bgPaint);
  
  // Draw a gradient background circle
  final gradientPaint = Paint()
    ..shader = ui.Gradient.linear(
      Offset(center.dx - radius, center.dy - radius),
      Offset(center.dx + radius, center.dy + radius),
      [
        const Color(0xFF6A11CB), // Purple
        const Color(0xFF2575FC), // Blue
      ],
    )
    ..style = PaintingStyle.fill;
  canvas.drawCircle(center, radius, gradientPaint);
  
  // Draw the outer ring
  final ringPaint = Paint()
    ..color = Colors.white.withAlpha(100)
    ..style = PaintingStyle.stroke
    ..strokeWidth = size.width * 0.02;
  canvas.drawCircle(center, radius * 0.85, ringPaint);
  
  // Draw the inner circle
  final innerCirclePaint = Paint()
    ..color = Colors.white.withAlpha(80)
    ..style = PaintingStyle.fill;
  canvas.drawCircle(center, radius * 0.7, innerCirclePaint);
  
  // Draw the "A" letter
  drawALetter(canvas, center, radius * 0.6);
  
  // Draw the connection dots
  drawConnectionDots(canvas, center, radius * 0.7);
}

/// Draws the stylized "A" letter
void drawALetter(Canvas canvas, Offset center, double size) {
  final paint = Paint()
    ..color = const Color(0xFF1A237E) // Deep indigo
    ..style = PaintingStyle.fill;
  
  final path = Path();
  
  // Calculate points for the "A" shape
  final topPoint = Offset(center.dx, center.dy - size * 0.5);
  final bottomRightPoint = Offset(center.dx + size * 0.5, center.dy + size * 0.5);
  final bottomLeftPoint = Offset(center.dx - size * 0.5, center.dy + size * 0.5);
  final indentRightPoint = Offset(center.dx + size * 0.35, center.dy + size * 0.5);
  final indentLeftPoint = Offset(center.dx - size * 0.35, center.dy + size * 0.5);
  final middleRightPoint = Offset(center.dx + size * 0.25, center.dy + size * 0.1);
  final middleLeftPoint = Offset(center.dx - size * 0.25, center.dy + size * 0.1);
  
  // Draw "A" shape
  path.moveTo(topPoint.dx, topPoint.dy); // Top point
  path.lineTo(bottomRightPoint.dx, bottomRightPoint.dy); // Bottom right
  path.lineTo(indentRightPoint.dx, indentRightPoint.dy); // Indent bottom right
  path.lineTo(middleRightPoint.dx, middleRightPoint.dy); // Middle right
  path.lineTo(middleLeftPoint.dx, middleLeftPoint.dy); // Middle left
  path.lineTo(indentLeftPoint.dx, indentLeftPoint.dy); // Indent bottom left
  path.lineTo(bottomLeftPoint.dx, bottomLeftPoint.dy); // Bottom left
  path.close();
  
  // Draw the shape
  canvas.drawPath(path, paint);
  
  // Add a horizontal line for the crossbar of "A"
  final crossbarPaint = Paint()
    ..color = Colors.white
    ..style = PaintingStyle.stroke
    ..strokeWidth = size * 0.05;
    
  canvas.drawLine(
    Offset(center.dx - size * 0.3, center.dy + size * 0.2),
    Offset(center.dx + size * 0.3, center.dy + size * 0.2),
    crossbarPaint,
  );
}

/// Draws the connection dots around the "A" letter
void drawConnectionDots(Canvas canvas, Offset center, double radius) {
  // Top left dot
  drawDot(
    canvas, 
    Offset(center.dx - radius * 0.4, center.dy - radius * 0.3),
    radius * 0.08,
    const Color(0xFF1565C0), // Blue
  );
  
  // Top right dot
  drawDot(
    canvas, 
    Offset(center.dx + radius * 0.4, center.dy - radius * 0.1),
    radius * 0.08,
    const Color(0xFFD32F2F), // Red
  );
  
  // Bottom dot
  drawDot(
    canvas, 
    Offset(center.dx, center.dy + radius * 0.4),
    radius * 0.08,
    const Color(0xFF388E3C), // Green
  );
  
  // Draw connecting lines
  final linePaint = Paint()
    ..color = Colors.white.withAlpha(150)
    ..style = PaintingStyle.stroke
    ..strokeWidth = radius * 0.02;
    
  // Connect the dots
  canvas.drawLine(
    Offset(center.dx - radius * 0.4, center.dy - radius * 0.3),
    Offset(center.dx + radius * 0.4, center.dy - radius * 0.1),
    linePaint,
  );
  
  canvas.drawLine(
    Offset(center.dx - radius * 0.4, center.dy - radius * 0.3),
    Offset(center.dx, center.dy + radius * 0.4),
    linePaint,
  );
  
  canvas.drawLine(
    Offset(center.dx + radius * 0.4, center.dy - radius * 0.1),
    Offset(center.dx, center.dy + radius * 0.4),
    linePaint,
  );
}

/// Helper method to draw a colored dot with a glow effect
void drawDot(Canvas canvas, Offset position, double radius, Color color) {
  // Draw glow
  final glowPaint = Paint()
    ..color = color.withAlpha(100)
    ..style = PaintingStyle.fill
    ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 10);
  canvas.drawCircle(position, radius * 1.5, glowPaint);
  
  // Draw dot
  final dotPaint = Paint()
    ..color = color
    ..style = PaintingStyle.fill;
  canvas.drawCircle(position, radius, dotPaint);
}
