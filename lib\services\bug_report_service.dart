import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'dart:io';
import '../models/bug_report_model.dart';
import 'notification_service.dart';

class BugReportService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final NotificationService _notificationService = NotificationService();

  // Collection reference
  CollectionReference get _bugReportsCollection =>
      _firestore.collection('bug_reports');

  // Submit a bug report
  Future<String> submitBugReport({
    required String title,
    required String description,
    required String category,
    String? screenshotPath,
    Map<String, dynamic>? deviceInfo,
  }) async {
    try {
      // Get current user
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Upload screenshot if available
      String? screenshotUrl;
      if (screenshotPath != null) {
        final storageRef = _storage
            .ref()
            .child('bug_reports')
            .child('${user.uid}_${DateTime.now().millisecondsSinceEpoch}.jpg');

        // Upload file
        final File imageFile = File(screenshotPath);
        final uploadTask = storageRef.putFile(
          imageFile,
          SettableMetadata(contentType: 'image/jpeg'),
        );

        // Wait for upload to complete
        await uploadTask;

        // Get download URL
        screenshotUrl = await storageRef.getDownloadURL();
      }

      // Create bug report model
      final bugReport = BugReport(
        userId: user.uid,
        userName: user.displayName,
        userEmail: user.email,
        title: title,
        description: description,
        category: category,
        status: 'pending',
        createdAt: DateTime.now(),
        screenshotUrl: screenshotUrl,
        deviceInfo: deviceInfo ?? {
          'platform': 'Flutter',
          'appVersion': '1.0.0',
        },
      );

      // Add to Firestore
      final docRef = await _bugReportsCollection.add(bugReport.toMap());

      // Send notification to admins
      final adminSnapshot = await _firestore.collection('users')
          .where('isAdmin', isEqualTo: true)
          .get();

      for (final adminDoc in adminSnapshot.docs) {
        final adminId = adminDoc.id;
        await _notificationService.sendNotification(
          userId: adminId,
          title: 'New Bug Report',
          body: 'A new bug report has been submitted: "$title"',
          type: 'bug_report',
          data: {
            'bugReportId': docRef.id,
          },
        );
      }

      // Send confirmation notification to user
      await _notificationService.sendNotification(
        userId: user.uid,
        title: 'Bug Report Received',
        body: 'Thank you for reporting this issue. Our team will investigate it.',
        type: 'system',
        data: {
          'bugReportId': docRef.id,
        },
      );

      return docRef.id;
    } catch (e) {
      rethrow;
    }
  }

  // Get all bug reports (for admin)
  Stream<List<BugReport>> getAllBugReports() {
    return _bugReportsCollection
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return BugReport.fromMap(data, doc.id);
      }).toList();
    });
  }

  // Get limited bug reports with pagination (for admin)
  Stream<List<BugReport>> getLimitedBugReports(int limit) {
    return _bugReportsCollection
        .orderBy('createdAt', descending: true)
        .limit(limit)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return BugReport.fromMap(data, doc.id);
      }).toList();
    });
  }

  // Get bug reports by status
  Stream<List<BugReport>> getBugReportsByStatus(String status) {
    return _bugReportsCollection
        .where('status', isEqualTo: status)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return BugReport.fromMap(data, doc.id);
      }).toList();
    });
  }

  // Get bug reports for current user
  Stream<List<BugReport>> getUserBugReports() {
    final user = _auth.currentUser;
    if (user == null) {
      throw Exception('User not authenticated');
    }

    return _bugReportsCollection
        .where('userId', isEqualTo: user.uid)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return BugReport.fromMap(data, doc.id);
      }).toList();
    });
  }

  // Update bug report status
  Future<void> updateBugReportStatus(String reportId, String status) async {
    try {
      await _bugReportsCollection.doc(reportId).update({
        'status': status,
        'updatedAt': FieldValue.serverTimestamp(),
        'updatedBy': _auth.currentUser?.uid,
      });

      // Get the bug report to send notification to the user
      final reportDoc = await _bugReportsCollection.doc(reportId).get();
      final reportData = reportDoc.data() as Map<String, dynamic>;
      final userId = reportData['userId'];
      final title = reportData['title'] ?? 'Bug Report';

      // Send notification to user
      String notificationTitle;
      String notificationBody;

      switch (status) {
        case 'in_progress':
          notificationTitle = 'Your Bug Report is Being Addressed';
          notificationBody = 'We\'re currently working on the issue you reported: "$title"';
          break;
        case 'resolved':
          notificationTitle = 'Your Bug Report has been Resolved';
          notificationBody = 'We\'ve resolved the issue you reported: "$title"';
          break;
        case 'closed':
          notificationTitle = 'Your Bug Report has been Closed';
          notificationBody = 'We\'ve closed your bug report: "$title"';
          break;
        default:
          notificationTitle = 'Bug Report Status Update';
          notificationBody = 'The status of your bug report "$title" has been updated';
      }

      await _notificationService.sendNotification(
        userId: userId,
        title: notificationTitle,
        body: notificationBody,
        type: 'bug_report',
        data: {
          'bugReportId': reportId,
          'status': status,
        },
      );
    } catch (e) {
      rethrow;
    }
  }

  // Add response to bug report
  Future<void> addResponseToBugReport(String reportId, String message) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Add response to bug report
      await _bugReportsCollection.doc(reportId).collection('responses').add({
        'message': message,
        'sentBy': user.uid,
        'sentByName': user.displayName ?? 'Admin',
        'sentAt': FieldValue.serverTimestamp(),
      });

      // Update the report to show it has responses
      await _bugReportsCollection.doc(reportId).update({
        'hasResponses': true,
        'lastResponseAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Get the bug report to send notification to the user
      final reportDoc = await _bugReportsCollection.doc(reportId).get();
      final reportData = reportDoc.data() as Map<String, dynamic>;
      final userId = reportData['userId'];
      final title = reportData['title'] ?? 'Bug Report';

      // Send notification to user
      await _notificationService.sendNotification(
        userId: userId,
        title: 'Response to Your Bug Report',
        body: 'We\'ve responded to your bug report: "$title"',
        type: 'bug_report',
        data: {
          'bugReportId': reportId,
        },
      );
    } catch (e) {
      rethrow;
    }
  }

  // Get responses for a bug report
  Stream<List<Map<String, dynamic>>> getBugReportResponses(String reportId) {
    return _bugReportsCollection
        .doc(reportId)
        .collection('responses')
        .orderBy('sentAt', descending: false)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    });
  }

  // Delete bug report
  Future<void> deleteBugReport(String reportId) async {
    try {
      // Get the bug report to check if it has a screenshot
      final reportDoc = await _bugReportsCollection.doc(reportId).get();
      final reportData = reportDoc.data() as Map<String, dynamic>;
      final screenshotUrl = reportData['screenshotUrl'] as String?;

      // Delete screenshot from storage if it exists
      if (screenshotUrl != null && screenshotUrl.isNotEmpty) {
        try {
          final storageRef = _storage.refFromURL(screenshotUrl);
          await storageRef.delete();
        } catch (e) {
          // Ignore errors when deleting storage files
          print('Error deleting screenshot: $e');
        }
      }

      // Delete responses subcollection
      final responsesSnapshot = await _bugReportsCollection
          .doc(reportId)
          .collection('responses')
          .get();
      
      for (final doc in responsesSnapshot.docs) {
        await _bugReportsCollection
            .doc(reportId)
            .collection('responses')
            .doc(doc.id)
            .delete();
      }

      // Delete the bug report document
      await _bugReportsCollection.doc(reportId).delete();
    } catch (e) {
      rethrow;
    }
  }
}
