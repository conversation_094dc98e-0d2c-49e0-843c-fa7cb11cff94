rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if user is admin
    function isAdmin() {
      return request.auth != null &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }

    // Helper function to check if user is temp admin
    function isTempAdmin() {
      return request.auth != null &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.tempAdmin == true;
    }

    // Admin users have full access to all collections
    // This rule must come first to override more specific rules
    match /{document=**} {
      allow read, write: if isAdmin();
    }

    // Basic user profile data
    match /users/{userId} {
      // Allow all authenticated users to read user profiles for search functionality
      allow read: if request.auth != null;

      // Users can create their initial document
      allow create: if request.auth != null && request.auth.uid == userId;

      // Users can update their own data, but cannot modify financial fields
      // Also allow incrementing follower/following counts
      allow update: if request.auth != null &&
                     (
                       // Regular users can't modify financial fields
                       (request.auth.uid == userId &&
                        !request.resource.data.diff(resource.data).affectedKeys()
                          .hasAny(['balance', 'totalEarnings', 'pendingWithdrawals', 'isAdmin'])) ||
                       // Allow follower count updates
                       (request.resource.data.diff(resource.data).affectedKeys().hasOnly(['followersCount']) &&
                        (request.resource.data.followersCount == resource.data.followersCount + 1 ||
                         request.resource.data.followersCount == resource.data.followersCount - 1)) ||
                       // Allow following count updates
                       (request.resource.data.diff(resource.data).affectedKeys().hasOnly(['followingCount']) &&
                        (request.resource.data.followingCount == resource.data.followingCount + 1 ||
                         request.resource.data.followingCount == resource.data.followingCount - 1)) ||
                       // Allow temporary admin status for payment processing
                       (request.auth.uid == userId &&
                        request.resource.data.diff(resource.data).affectedKeys().hasAny(['isAdmin', 'tempAdmin'])) ||
                       // Allow balance updates by the user themselves
                       (request.auth.uid == userId &&
                        request.resource.data.diff(resource.data).affectedKeys().hasAny(['balance', 'totalEarnings']) &&
                        (isTempAdmin() || isAdmin())) ||
                       // Admins can update any fields
                       isAdmin() ||
                       // Temp admins can update financial fields
                       isTempAdmin()
                     );
    }

    // User settings
    match /user_settings/{userId} {
      allow read: if request.auth != null &&
                   (request.auth.uid == userId || isAdmin());
      allow write: if request.auth != null &&
                    (request.auth.uid == userId || isAdmin());
    }

    // User profiles - public info but controlled updates
    match /user_profiles/{userId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null &&
                    (request.auth.uid == userId || isAdmin());
    }

    // Reviews collection - allow clients to create reviews and anyone to read them
    match /reviews/{reviewId} {
      // Allow any authenticated user to read reviews
      allow read: if request.auth != null;

      // Allow any authenticated user to create reviews
      allow create: if request.auth != null;

      // Allow updates by the review creator
      allow update: if request.auth != null &&
                    resource.data.reviewerId == request.auth.uid;

      // Allow deletion by the review creator
      allow delete: if request.auth != null &&
                    resource.data.reviewerId == request.auth.uid;
    }

    // Conversations and messages - allow participants to read/write
    match /conversations/{conversationId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null;
      allow update: if request.auth != null;
      allow delete: if request.auth != null;

      // Allow access to messages subcollection with expanded permissions for custom offers and hire requests
      match /messages/{messageId} {
        allow read: if request.auth != null;
        allow create: if request.auth != null;
        allow update: if request.auth != null;
        allow delete: if request.auth != null;
      }
    }

    // Financial transactions - users can read their own, admins can read all and write
    match /transactions/{transactionId} {
      allow read: if request.auth != null &&
                   (resource.data.userId == request.auth.uid || isAdmin() || isTempAdmin());
      allow write: if request.auth != null && (isAdmin() || isTempAdmin());
    }

    // Withdrawal requests - users can create and read their own, only admin can update status
    match /withdrawals/{withdrawalId} {
      allow read: if request.auth != null &&
                   (resource.data.userId == request.auth.uid || isAdmin());

      // Users can create withdrawal requests but cannot set status to approved
      allow create: if request.auth != null &&
                     request.resource.data.userId == request.auth.uid &&
                     request.resource.data.status == "pending";

      // Only admin can update withdrawal status
      allow update: if request.auth != null && isAdmin();
    }

    // Assets collection - users can read approved assets, create their own
    match /assets/{assetId} {
      // Anyone can read approved assets or their own assets
      allow read: if request.auth != null &&
                   (resource.data.status == "approved" ||
                    resource.data.authorId == request.auth.uid);

      // Users can create their own assets
      allow create: if request.auth != null &&
                     request.resource.data.authorId == request.auth.uid;

      // Users can update their own assets if not yet approved
      allow update: if request.auth != null &&
                     resource.data.authorId == request.auth.uid &&
                     resource.data.status != "approved";
    }

    // Notifications collection
    match /notifications/{notificationId} {
      // Users can read their own notifications
      allow read: if request.auth != null &&
                   resource.data.userId == request.auth.uid;

      // Allow creating notifications for any authenticated user
      allow create: if request.auth != null;

      // Users can update their own notifications (e.g., mark as read)
      allow update: if request.auth != null &&
                     resource.data.userId == request.auth.uid;

      // Users can delete their own notifications
      allow delete: if request.auth != null &&
                     resource.data.userId == request.auth.uid;
    }

    // Admin activity log is already covered by the admin rule above

    // Payment intents collection - users can create and read their own
    match /payment_intents/{paymentIntentId} {
      allow read: if request.auth != null &&
                   (resource.data.userId == request.auth.uid || isAdmin());

      // Users can create payment intents
      allow create: if request.auth != null &&
                     request.resource.data.userId == request.auth.uid;

      // Users can update their own payment intents (e.g., to cancel)
      allow update: if request.auth != null &&
                     resource.data.userId == request.auth.uid &&
                     request.resource.data.status != "succeeded";
    }

    // Stripe payments collection - users can create and read their own
    match /stripe_payments/{paymentId} {
      allow read: if request.auth != null &&
                   (resource.data.userId == request.auth.uid || isAdmin());

      // Users can create stripe payments
      allow create: if request.auth != null &&
                     request.resource.data.userId == request.auth.uid;

      // Only admins can update stripe payments
      allow update: if request.auth != null && isAdmin();
    }

    // Balance requests collection - users can create and read their own
    match /balance_requests/{requestId} {
      allow read: if request.auth != null &&
                   (resource.data.userId == request.auth.uid || isAdmin());

      // Users can create balance requests
      allow create: if request.auth != null &&
                     request.resource.data.userId == request.auth.uid;

      // Only admins can update balance requests
      allow update: if request.auth != null && isAdmin();
    }

    // Hire requests collection - allow users to create and manage hire requests
    match /hire_requests/{requestId} {
      // Allow any authenticated user to read hire requests they're involved in
      allow read: if request.auth != null;

      // Allow any authenticated user to create hire requests
      allow create: if request.auth != null;

      // Allow updates by any authenticated user
      allow update: if request.auth != null;

      // Allow deletion by any authenticated user
      allow delete: if request.auth != null;
    }

    // Hire plans collection - allow users to read all hire plans, create/update their own
    match /hire_plans/{planId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null &&
                     request.resource.data.userId == request.auth.uid;
      allow update, delete: if request.auth != null &&
                             resource.data.userId == request.auth.uid;
    }

    // Bank details collection - users can manage their own bank details
    match /bank_details/{bankDetailsId} {
      // Users can read their own bank details
      allow read: if request.auth != null &&
                   resource.data.userId == request.auth.uid;

      // Users can create their own bank details
      allow create: if request.auth != null &&
                     request.resource.data.userId == request.auth.uid;

      // Users can update their own bank details
      allow update: if request.auth != null &&
                     resource.data.userId == request.auth.uid;

      // Users can delete their own bank details
      allow delete: if request.auth != null &&
                     resource.data.userId == request.auth.uid;
    }

    // Payment methods collection - users can manage their own payment methods
    match /payment_methods/{paymentMethodId} {
      // Users can read their own payment methods
      allow read: if request.auth != null &&
                   resource.data.userId == request.auth.uid;

      // Users can create their own payment methods
      allow create: if request.auth != null &&
                     request.resource.data.userId == request.auth.uid;

      // Users can update their own payment methods
      allow update: if request.auth != null &&
                     resource.data.userId == request.auth.uid;

      // Users can delete their own payment methods
      allow delete: if request.auth != null &&
                     resource.data.userId == request.auth.uid;
    }

    // Challenges collection - users can read all challenges, create/update their own
    match /challenges/{challengeId} {
      // Anyone authenticated can read challenges
      allow read: if request.auth != null;

      // Users can create challenges
      allow create: if request.auth != null &&
                     request.resource.data.creatorId == request.auth.uid;

      // Only challenge creator can update their challenges
      allow update: if request.auth != null &&
                     resource.data.creatorId == request.auth.uid;

      // Only challenge creator can delete their challenges
      allow delete: if request.auth != null &&
                     resource.data.creatorId == request.auth.uid;
    }

    // Challenge submissions collection - users can read all submissions, create/update their own
    match /challenge_submissions/{submissionId} {
      // Anyone authenticated can read submissions
      allow read: if request.auth != null;

      // Users can create submissions
      allow create: if request.auth != null &&
                     request.resource.data.participantId == request.auth.uid;

      // Only submission creator can update their submissions
      allow update: if request.auth != null &&
                     resource.data.participantId == request.auth.uid;

      // Only submission creator can delete their submissions
      allow delete: if request.auth != null &&
                     resource.data.participantId == request.auth.uid;
    }

    // Default deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
