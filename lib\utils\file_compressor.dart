import 'dart:io';
import 'package:archive/archive_io.dart';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;

/// Utility class for compressing files
class FileCompressor {
  /// Compresses a single file into a zip archive with maximum compression
  /// Returns the compressed file
  static Future<File> compressSingleFile(File file, {int compressionLevel = 9}) async {
    try {
      final String fileName = path.basename(file.path);
      final String directory = path.dirname(file.path);
      final String outputPath = '$directory/${path.basenameWithoutExtension(file.path)}_compressed.zip';

      // Get original file size for logging
      final int originalSize = await file.length();
      debugPrint('FileCompressor - Original file size: ${formatFileSize(originalSize)}');

      // For very large files, use even more aggressive compression
      if (originalSize > 10 * 1024 * 1024) { // > 10MB
        compressionLevel = 9; // Maximum compression level
      }

      // Create a zip encoder with maximum compression
      final encoder = ZipFileEncoder();
      encoder.create(outputPath);

      // Create an archive
      final archive = Archive();

      // Read the file bytes
      final List<int> fileBytes = await file.readAsBytes();

      // Add the file to the archive with maximum compression
      final archiveFile = ArchiveFile(
        fileName,
        fileBytes.length,
        fileBytes
      );
      archive.addFile(archiveFile);

      // Write the archive to the output file with maximum compression
      final outputFile = File(outputPath);
      final List<int> compressedBytes = ZipEncoder().encode(archive, level: compressionLevel)!;
      await outputFile.writeAsBytes(compressedBytes);

      // Get compressed file size for logging
      final int compressedSize = await outputFile.length();
      final double compressionRatio = originalSize / compressedSize;
      debugPrint('FileCompressor - Compressed file size: ${formatFileSize(compressedSize)}, ratio: ${compressionRatio.toStringAsFixed(2)}x');

      return outputFile;
    } catch (e) {
      debugPrint('Error compressing file: $e');
      // If compression fails, return the original file
      return file;
    }
  }

  /// Compresses multiple files into a single zip archive with maximum compression
  /// Returns the compressed file
  /// The progressCallback is called with a value between 0.0 and 1.0 to indicate progress
  static Future<File> compressMultipleFiles(
    List<File> files, {
    String? customFileName,
    Function(double progress, String message)? progressCallback,
    int compressionLevel = 9, // Maximum compression by default
  }) async {
    try {
      if (files.isEmpty) {
        throw Exception('No files to compress');
      }

      // Use the directory of the first file for output
      final String directory = path.dirname(files.first.path);
      final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final String outputFileName = customFileName ?? 'compressed_files_$timestamp.zip';
      final String outputPath = '$directory/$outputFileName';

      // Calculate total size for progress reporting
      int totalBytes = 0;
      for (final file in files) {
        totalBytes += await file.length();
      }

      // For very large total size, use maximum compression
      if (totalBytes > 20 * 1024 * 1024) { // > 20MB
        compressionLevel = 9; // Ensure maximum compression
        debugPrint('FileCompressor - Large total size detected (${formatFileSize(totalBytes)}), using maximum compression');
      }

      // Report initial progress
      progressCallback?.call(0.0, 'Starting compression...');
      debugPrint('FileCompressor - Total original size: ${formatFileSize(totalBytes)}');

      // Create an archive with maximum compression
      final archive = Archive();

      // Add all files to the archive with progress reporting
      int processedBytes = 0;
      for (int i = 0; i < files.length; i++) {
        final file = files[i];
        final fileSize = await file.length();
        final fileName = path.basename(file.path);

        // Report progress before adding file
        final double fileProgress = processedBytes / totalBytes;
        progressCallback?.call(
          fileProgress,
          'Compressing file ${i + 1} of ${files.length}: $fileName'
        );

        // Read file bytes
        final List<int> fileBytes = await file.readAsBytes();

        // Add file to archive
        final archiveFile = ArchiveFile(
          fileName,
          fileBytes.length,
          fileBytes
        );
        archive.addFile(archiveFile);

        // Update processed bytes
        processedBytes += fileSize;

        // Report progress after adding file
        final double newProgress = processedBytes / totalBytes;
        progressCallback?.call(
          newProgress,
          'Compressed file ${i + 1} of ${files.length}: $fileName'
        );
      }

      // Write the archive to the output file with maximum compression
      progressCallback?.call(0.9, 'Finalizing compression...');
      final outputFile = File(outputPath);
      final List<int> compressedBytes = ZipEncoder().encode(archive, level: compressionLevel)!;
      await outputFile.writeAsBytes(compressedBytes);

      // Get compressed file size for logging
      final int compressedSize = await outputFile.length();
      final double compressionRatio = totalBytes / compressedSize;

      final String compressionInfo = 'Compressed ${files.length} files from ${formatFileSize(totalBytes)} to ${formatFileSize(compressedSize)} (${compressionRatio.toStringAsFixed(2)}x smaller)';
      debugPrint('FileCompressor - $compressionInfo');

      // Report completion
      progressCallback?.call(1.0, compressionInfo);

      return outputFile;
    } catch (e) {
      debugPrint('Error compressing multiple files: $e');
      progressCallback?.call(0.0, 'Error: $e');
      // If compression fails, return the first file
      return files.first;
    }
  }

  /// Checks if a file is already compressed based on its extension
  static bool isCompressedFile(File file) {
    final String extension = path.extension(file.path).toLowerCase();
    return ['.zip', '.rar', '.7z', '.gz', '.tar', '.bz2'].contains(extension);
  }

  /// Gets the size of a file in bytes
  static Future<int> getFileSize(File file) async {
    try {
      return await file.length();
    } catch (e) {
      debugPrint('Error getting file size: $e');
      return 0;
    }
  }

  /// Formats file size to a human-readable string
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
}
