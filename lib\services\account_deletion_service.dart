import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'notification_service.dart';

class AccountDeletionService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final NotificationService _notificationService = NotificationService();

  /// Process account deletion after 30 days
  /// This should be called by a scheduled Cloud Function or background service
  Future<void> processScheduledDeletions() async {
    try {
      // Get all pending deletions that are past the 30-day period
      final now = DateTime.now();
      final cutoffDate = now.subtract(const Duration(days: 30));

      final pendingDeletions = await _firestore
          .collection('deleted_accounts')
          .where('status', isEqualTo: 'pending')
          .where('deletionRequestedAt', isLessThan: Timestamp.fromDate(cutoffDate))
          .get();

      debugPrint('Found ${pendingDeletions.docs.length} accounts to delete');

      for (final doc in pendingDeletions.docs) {
        final deletionData = doc.data();
        final userId = deletionData['userId'] as String;

        try {
          // Send final notification and process fund transfer before deletion
          await _sendFinalNotificationAndTransferFunds(userId, deletionData);

          // Delete all user data
          await _deleteUserCompletely(userId, doc.id);
          debugPrint('Successfully deleted account: $userId');
        } catch (e) {
          debugPrint('Error deleting account $userId: $e');
          // Mark as failed for manual review
          await doc.reference.update({
            'status': 'failed',
            'failureReason': e.toString(),
            'failedAt': FieldValue.serverTimestamp(),
          });
        }
      }
    } catch (e) {
      debugPrint('Error processing scheduled deletions: $e');
    }
  }

  /// Completely delete a user and all associated data
  Future<void> _deleteUserCompletely(String userId, String deletionRecordId) async {
    final batch = _firestore.batch();

    try {
      // 1. Delete user posts and related data
      await _deleteUserPosts(userId, batch);

      // 2. Delete user comments and replies
      await _deleteUserComments(userId, batch);

      // 3. Delete user assets
      await _deleteUserAssets(userId, batch);

      // 4. Delete user messages and conversations
      await _deleteUserMessages(userId, batch);

      // 5. Delete user hire requests
      await _deleteUserHireRequests(userId, batch);

      // 6. Delete user saved posts
      await _deleteUserSavedPosts(userId, batch);

      // 7. Delete user followers/following relationships
      await _deleteUserRelationships(userId, batch);

      // 8. Delete user profile and settings
      await _deleteUserProfile(userId, batch);

      // 9. Delete user from Firebase Auth (this needs to be done via Admin SDK in production)
      await _deleteUserAuth(userId);

      // 10. Update deletion record to completed
      batch.update(
        _firestore.collection('deleted_accounts').doc(deletionRecordId),
        {
          'status': 'completed',
          'completedAt': FieldValue.serverTimestamp(),
          'deletedItemsCount': await _getDeletedItemsCount(userId),
        },
      );

      // Commit all deletions
      await batch.commit();

      debugPrint('User $userId completely deleted');

    } catch (e) {
      debugPrint('Error in complete user deletion: $e');
      rethrow;
    }
  }

  /// Delete all user posts and related data (likes, comments on posts)
  Future<void> _deleteUserPosts(String userId, WriteBatch batch) async {
    final posts = await _firestore
        .collection('posts')
        .where('authorId', isEqualTo: userId)
        .get();

    for (final post in posts.docs) {
      // Delete post likes
      final likes = await _firestore
          .collection('post_likes')
          .where('postId', isEqualTo: post.id)
          .get();

      for (final like in likes.docs) {
        batch.delete(like.reference);
      }

      // Delete post comments (handled separately in _deleteUserComments)
      // Delete poll votes if it's a poll post
      if (post.data()['type'] == 'poll') {
        final votes = await _firestore
            .collection('poll_votes')
            .where('postId', isEqualTo: post.id)
            .get();

        for (final vote in votes.docs) {
          batch.delete(vote.reference);
        }
      }

      // Delete the post itself
      batch.delete(post.reference);
    }
  }

  /// Delete all user comments and replies
  Future<void> _deleteUserComments(String userId, WriteBatch batch) async {
    final comments = await _firestore
        .collection('comments')
        .where('authorId', isEqualTo: userId)
        .get();

    for (final comment in comments.docs) {
      // Delete comment likes
      final likes = await _firestore
          .collection('comment_likes')
          .where('commentId', isEqualTo: comment.id)
          .get();

      for (final like in likes.docs) {
        batch.delete(like.reference);
      }

      // Delete replies to this comment
      final replies = await _firestore
          .collection('comment_replies')
          .where('commentId', isEqualTo: comment.id)
          .get();

      for (final reply in replies.docs) {
        batch.delete(reply.reference);
      }

      // Delete the comment itself
      batch.delete(comment.reference);
    }

    // Also delete replies made by this user
    final userReplies = await _firestore
        .collection('comment_replies')
        .where('authorId', isEqualTo: userId)
        .get();

    for (final reply in userReplies.docs) {
      batch.delete(reply.reference);
    }
  }

  /// Delete all user assets and files from storage
  Future<void> _deleteUserAssets(String userId, WriteBatch batch) async {
    final assets = await _firestore
        .collection('assets')
        .where('authorId', isEqualTo: userId)
        .get();

    for (final asset in assets.docs) {
      final assetData = asset.data();

      // Delete files from Firebase Storage
      if (assetData['downloadUrl'] != null) {
        try {
          await _storage.refFromURL(assetData['downloadUrl']).delete();
        } catch (e) {
          debugPrint('Error deleting asset file: $e');
        }
      }

      // Delete preview images if any
      if (assetData['previewImages'] != null) {
        final previewImages = assetData['previewImages'] as List;
        for (final imageUrl in previewImages) {
          try {
            await _storage.refFromURL(imageUrl).delete();
          } catch (e) {
            debugPrint('Error deleting preview image: $e');
          }
        }
      }

      // Delete asset document
      batch.delete(asset.reference);
    }
  }

  /// Delete all user messages and conversations
  Future<void> _deleteUserMessages(String userId, WriteBatch batch) async {
    // Delete messages sent by user
    final sentMessages = await _firestore
        .collection('messages')
        .where('senderId', isEqualTo: userId)
        .get();

    for (final message in sentMessages.docs) {
      batch.delete(message.reference);
    }

    // Delete conversations where user is a participant
    final conversations = await _firestore
        .collection('conversations')
        .where('participants', arrayContains: userId)
        .get();

    for (final conversation in conversations.docs) {
      // If conversation has only this user, delete it completely
      final participants = conversation.data()['participants'] as List;
      if (participants.length == 1) {
        batch.delete(conversation.reference);
      } else {
        // Remove user from participants list
        batch.update(conversation.reference, {
          'participants': FieldValue.arrayRemove([userId]),
        });
      }
    }
  }

  /// Delete all user hire requests
  Future<void> _deleteUserHireRequests(String userId, WriteBatch batch) async {
    // Delete hire requests where user is the developer
    final developerRequests = await _firestore
        .collection('hire_requests')
        .where('developerId', isEqualTo: userId)
        .get();

    for (final request in developerRequests.docs) {
      batch.delete(request.reference);
    }

    // Delete hire requests where user is the client
    final clientRequests = await _firestore
        .collection('hire_requests')
        .where('clientId', isEqualTo: userId)
        .get();

    for (final request in clientRequests.docs) {
      batch.delete(request.reference);
    }

    // Delete user's hire plans
    final hirePlans = await _firestore
        .collection('hire_plans')
        .where('userId', isEqualTo: userId)
        .get();

    for (final plan in hirePlans.docs) {
      batch.delete(plan.reference);
    }
  }

  /// Delete user saved posts
  Future<void> _deleteUserSavedPosts(String userId, WriteBatch batch) async {
    final savedPosts = await _firestore
        .collection('saved_posts')
        .where('userId', isEqualTo: userId)
        .get();

    for (final savedPost in savedPosts.docs) {
      batch.delete(savedPost.reference);
    }
  }

  /// Delete user relationships (followers/following)
  Future<void> _deleteUserRelationships(String userId, WriteBatch batch) async {
    // Delete where user is following others
    final following = await _firestore
        .collection('user_follows')
        .where('followerId', isEqualTo: userId)
        .get();

    for (final follow in following.docs) {
      batch.delete(follow.reference);
    }

    // Delete where others are following user
    final followers = await _firestore
        .collection('user_follows')
        .where('followingId', isEqualTo: userId)
        .get();

    for (final follow in followers.docs) {
      batch.delete(follow.reference);
    }
  }

  /// Delete user profile and settings
  Future<void> _deleteUserProfile(String userId, WriteBatch batch) async {
    // Delete user profile
    batch.delete(_firestore.collection('user_profiles').doc(userId));

    // Delete user settings
    batch.delete(_firestore.collection('user_settings').doc(userId));

    // Delete user notifications
    final notifications = await _firestore
        .collection('notifications')
        .where('userId', isEqualTo: userId)
        .get();

    for (final notification in notifications.docs) {
      batch.delete(notification.reference);
    }

    // Delete bank details
    final bankDetails = await _firestore
        .collection('bank_details')
        .where('userId', isEqualTo: userId)
        .get();

    for (final bank in bankDetails.docs) {
      batch.delete(bank.reference);
    }

    // Delete payment methods
    final paymentMethods = await _firestore
        .collection('payment_methods')
        .where('userId', isEqualTo: userId)
        .get();

    for (final payment in paymentMethods.docs) {
      batch.delete(payment.reference);
    }

    // Finally delete the main user document
    batch.delete(_firestore.collection('users').doc(userId));
  }

  /// Delete user from Firebase Auth (requires Admin SDK in production)
  Future<void> _deleteUserAuth(String userId) async {
    // Note: In production, this should be done via Firebase Admin SDK
    // For now, we'll just mark the user as deleted in Firestore
    // The actual Firebase Auth user deletion should be handled by a Cloud Function
    debugPrint('User Auth deletion should be handled by Admin SDK: $userId');
  }

  /// Get count of deleted items for reporting
  Future<int> _getDeletedItemsCount(String userId) async {
    // This is a simplified count - in production you might want more detailed metrics
    return 1; // Placeholder
  }

  /// Send final deletion notification and transfer funds
  Future<void> _sendFinalNotificationAndTransferFunds(String userId, Map<String, dynamic> deletionData) async {
    try {
      final userEmail = deletionData['userEmail'] as String?;
      final userName = deletionData['userName'] as String? ?? 'User';
      final availableBalance = (deletionData['availableBalance'] as num?)?.toDouble() ?? 0.0;
      final bankDetails = deletionData['bankDetails'] as Map<String, dynamic>? ?? {};

      // Send final email notification
      if (userEmail != null) {
        await _firestore.collection('mail').add({
          'to': [userEmail],
          'template': {
            'name': 'account-deletion-completed',
            'data': {
              'userName': userName,
              'deletionDate': DateTime.now().toIso8601String(),
              'transferredAmount': availableBalance,
              'bankAccount': bankDetails['accountNumber'] ?? 'N/A',
              'supportEmail': '<EMAIL>',
            },
          },
        });
      }

      // Process fund transfer if there's available balance
      if (availableBalance > 0 && bankDetails.isNotEmpty) {
        await _processFundTransfer(userId, availableBalance, bankDetails);
      }

      debugPrint('Final notification sent and funds processed for user: $userId');
    } catch (e) {
      debugPrint('Error sending final notification: $e');
    }
  }

  /// Process fund transfer to user's bank account
  Future<void> _processFundTransfer(String userId, double amount, Map<String, dynamic> bankDetails) async {
    try {
      // Create a withdrawal request for the final fund transfer
      await _firestore.collection('withdrawal_requests').add({
        'userId': userId,
        'amount': amount,
        'bankDetails': bankDetails,
        'type': 'account_deletion_transfer',
        'status': 'pending',
        'requestedAt': FieldValue.serverTimestamp(),
        'notes': 'Final fund transfer due to account deletion',
      });

      debugPrint('Fund transfer request created for user $userId: \$${amount.toStringAsFixed(2)}');
    } catch (e) {
      debugPrint('Error processing fund transfer: $e');
    }
  }

  /// Check for accounts that need deletion processing (can be called by a cron job)
  Future<void> checkAndProcessDeletions() async {
    try {
      debugPrint('Starting scheduled account deletion check...');
      await processScheduledDeletions();
      debugPrint('Scheduled account deletion check completed.');
    } catch (e) {
      debugPrint('Error in scheduled deletion check: $e');
    }
  }

  /// Get deletion statistics for admin dashboard
  Future<Map<String, dynamic>> getDeletionStatistics() async {
    try {
      final pending = await _firestore
          .collection('deleted_accounts')
          .where('status', isEqualTo: 'pending')
          .count()
          .get();

      final completed = await _firestore
          .collection('deleted_accounts')
          .where('status', isEqualTo: 'completed')
          .count()
          .get();

      final failed = await _firestore
          .collection('deleted_accounts')
          .where('status', isEqualTo: 'failed')
          .count()
          .get();

      final canceled = await _firestore
          .collection('deleted_accounts')
          .where('status', isEqualTo: 'canceled')
          .count()
          .get();

      return {
        'pending': pending.count ?? 0,
        'completed': completed.count ?? 0,
        'failed': failed.count ?? 0,
        'canceled': canceled.count ?? 0,
        'total': (pending.count ?? 0) + (completed.count ?? 0) + (failed.count ?? 0) + (canceled.count ?? 0),
      };
    } catch (e) {
      debugPrint('Error getting deletion statistics: $e');
      return {
        'pending': 0,
        'completed': 0,
        'failed': 0,
        'canceled': 0,
        'total': 0,
      };
    }
  }
}
