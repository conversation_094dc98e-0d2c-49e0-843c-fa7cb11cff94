import 'package:cloud_firestore/cloud_firestore.dart';

class BugReport {
  final String? id;
  final String userId;
  final String? userName;
  final String? userEmail;
  final String title;
  final String description;
  final String category;
  final String status;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final String? screenshotUrl;
  final Map<String, dynamic>? deviceInfo;
  final bool hasResponses;

  BugReport({
    this.id,
    required this.userId,
    this.userName,
    this.userEmail,
    required this.title,
    required this.description,
    required this.category,
    this.status = 'pending',
    DateTime? createdAt,
    this.updatedAt,
    this.screenshotUrl,
    this.deviceInfo,
    this.hasResponses = false,
  }) : createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'userName': userName,
      'userEmail': userEmail,
      'title': title,
      'description': description,
      'category': category,
      'status': status,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'screenshotUrl': screenshotUrl,
      'deviceInfo': deviceInfo,
      'hasResponses': hasResponses,
    };
  }

  factory BugReport.fromMap(Map<String, dynamic> map, String id) {
    return BugReport(
      id: id,
      userId: map['userId'] ?? '',
      userName: map['userName'],
      userEmail: map['userEmail'],
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      category: map['category'] ?? 'General',
      status: map['status'] ?? 'pending',
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (map['updatedAt'] as Timestamp?)?.toDate(),
      screenshotUrl: map['screenshotUrl'],
      deviceInfo: map['deviceInfo'] as Map<String, dynamic>?,
      hasResponses: map['hasResponses'] ?? false,
    );
  }

  BugReport copyWith({
    String? id,
    String? userId,
    String? userName,
    String? userEmail,
    String? title,
    String? description,
    String? category,
    String? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? screenshotUrl,
    Map<String, dynamic>? deviceInfo,
    bool? hasResponses,
  }) {
    return BugReport(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userEmail: userEmail ?? this.userEmail,
      title: title ?? this.title,
      description: description ?? this.description,
      category: category ?? this.category,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      screenshotUrl: screenshotUrl ?? this.screenshotUrl,
      deviceInfo: deviceInfo ?? this.deviceInfo,
      hasResponses: hasResponses ?? this.hasResponses,
    );
  }
}
