import 'package:flutter/material.dart';
import 'dart:math' as math;

/// A custom widget that displays the Asatu app logo
/// This matches the design used in the splash screen
class AppLogo extends StatefulWidget {
  final double size;
  final bool isAnimated;
  final Color primaryColor;
  final bool isGradient;

  const AppLogo({
    Key? key,
    this.size = 80,
    this.isAnimated = false,
    this.primaryColor = const Color(0xFF6A11CB),
    this.isGradient = true,
  }) : super(key: key);

  @override
  State<AppLogo> createState() => _AppLogoState();
}

class _AppLogoState extends State<AppLogo> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 4),
    );

    if (widget.isAnimated) {
      _animationController.repeat();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Stack(
            alignment: Alignment.center,
            children: [
              // Background circle
              Container(
                width: widget.size,
                height: widget.size,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: widget.isGradient ? null : widget.primaryColor.withAlpha(30),
                  gradient: widget.isGradient
                      ? const LinearGradient(
                          colors: [
                            Color(0xFF6A11CB), // Purple
                            Color(0xFF2575FC), // Blue
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        )
                      : null,
                ),
              ),

              // Outer rotating ring
              if (widget.isAnimated)
                Transform.rotate(
                  angle: _animationController.value * 2 * math.pi,
                  child: Container(
                    width: widget.size * 0.85,
                    height: widget.size * 0.85,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white.withAlpha(100),
                        width: widget.size * 0.02,
                      ),
                    ),
                  ),
                ),

              // Inner circle
              Container(
                width: widget.size * 0.7,
                height: widget.size * 0.7,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white.withAlpha(40),
                ),
              ),

              // "A" letter
              CustomPaint(
                size: Size(widget.size * 0.5, widget.size * 0.5),
                painter: ALetterPainter(),
              ),

              // Connection dots
              Positioned(
                top: widget.size * 0.25,
                left: widget.size * 0.25,
                child: _buildDot(Colors.blue.shade700, widget.size * 0.08),
              ),
              Positioned(
                top: widget.size * 0.4,
                right: widget.size * 0.25,
                child: _buildDot(Colors.red.shade700, widget.size * 0.08),
              ),
              Positioned(
                bottom: widget.size * 0.25,
                left: widget.size * 0.35,
                child: _buildDot(Colors.green.shade700, widget.size * 0.08),
              ),

              // Connection lines
              CustomPaint(
                size: Size(widget.size * 0.7, widget.size * 0.7),
                painter: ConnectionLinesPainter(
                  progress: widget.isAnimated ? _animationController.value : 0.5,
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildDot(Color color, double size) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: color.withAlpha(100),
            blurRadius: size * 0.8,
            spreadRadius: size * 0.2,
          ),
        ],
      ),
    );
  }
}

/// Custom painter for the "A" letter shape
class ALetterPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    final path = Path();

    // Draw "A" shape
    path.moveTo(size.width * 0.5, 0); // Top point
    path.lineTo(size.width, size.height); // Bottom right
    path.lineTo(size.width * 0.7, size.height); // Indent bottom right
    path.lineTo(size.width * 0.5, size.height * 0.6); // Middle right
    path.lineTo(size.width * 0.3, size.height); // Middle left
    path.lineTo(0, size.height); // Bottom left
    path.close();

    // Draw the shape
    canvas.drawPath(path, paint);

    // Add a horizontal line for the crossbar of "A"
    final crossbarPaint = Paint()
      ..color = const Color(0xFF6A11CB)
      ..style = PaintingStyle.stroke
      ..strokeWidth = size.height * 0.08;

    canvas.drawLine(
      Offset(size.width * 0.25, size.height * 0.7),
      Offset(size.width * 0.75, size.height * 0.7),
      crossbarPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// Custom painter for the connecting lines between dots
class ConnectionLinesPainter extends CustomPainter {
  final double progress;

  ConnectionLinesPainter({required this.progress});

  @override
  void paint(Canvas canvas, Size size) {
    // Calculate alpha based on progress for pulsing effect
    final alpha = (128 + 127 * math.sin(progress * math.pi)).toInt();

    final paint = Paint()
      ..color = Colors.white.withAlpha(alpha)
      ..style = PaintingStyle.stroke
      ..strokeWidth = size.width * 0.02;

    // Draw connecting lines between the dots
    canvas.drawLine(
      Offset(size.width * 0.3, size.height * 0.3),
      Offset(size.width * 0.7, size.height * 0.5),
      paint,
    );

    canvas.drawLine(
      Offset(size.width * 0.3, size.height * 0.3),
      Offset(size.width * 0.4, size.height * 0.7),
      paint,
    );

    canvas.drawLine(
      Offset(size.width * 0.7, size.height * 0.5),
      Offset(size.width * 0.4, size.height * 0.7),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant ConnectionLinesPainter oldDelegate) =>
      oldDelegate.progress != progress;
}
