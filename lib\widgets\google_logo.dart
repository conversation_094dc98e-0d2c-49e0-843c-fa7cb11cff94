import 'package:flutter/material.dart';

class Google<PERSON>ogo extends StatelessWidget {
  final double size;

  const GoogleLogo({Key? key, this.size = 24.0}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: Size(size, size),
      painter: GoogleLogoPainter(),
    );
  }
}

class GoogleLogoPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final double width = size.width;
    final double height = size.height;
    
    // Define colors
    const Color red = Color(0xFFEA4335);
    const Color blue = Color(0xFF4285F4);
    const Color green = Color(0xFF34A853);
    const Color yellow = Color(0xFFFBBC05);
    
    final Paint paint = Paint()
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;
    
    // Create paths for each color segment
    final Path bluePath = Path();
    final Path redPath = Path();
    final Path yellowPath = Path();
    final Path greenPath = Path();
    
    // Center point
    final double centerX = width / 2;
    final double centerY = height / 2;
    
    // Radius
    final double outerRadius = width / 2;
    final double innerRadius = outerRadius * 0.6;
    
    // Blue segment (right)
    bluePath.moveTo(centerX, centerY);
    bluePath.lineTo(centerX + outerRadius * 0.7, centerY - outerRadius * 0.3);
    bluePath.arcTo(
      Rect.fromCircle(center: Offset(centerX, centerY), radius: outerRadius),
      -Math.pi / 6,
      Math.pi / 3,
      false,
    );
    bluePath.lineTo(centerX, centerY);
    bluePath.close();
    
    // Red segment (top left)
    redPath.moveTo(centerX, centerY);
    redPath.lineTo(centerX - outerRadius * 0.6, centerY - outerRadius * 0.4);
    redPath.arcTo(
      Rect.fromCircle(center: Offset(centerX, centerY), radius: outerRadius),
      Math.pi * 5 / 6,
      Math.pi / 3,
      false,
    );
    redPath.lineTo(centerX, centerY);
    redPath.close();
    
    // Yellow segment (bottom left)
    yellowPath.moveTo(centerX, centerY);
    yellowPath.lineTo(centerX - outerRadius * 0.6, centerY + outerRadius * 0.4);
    yellowPath.arcTo(
      Rect.fromCircle(center: Offset(centerX, centerY), radius: outerRadius),
      Math.pi / 2,
      Math.pi / 3,
      false,
    );
    yellowPath.lineTo(centerX, centerY);
    yellowPath.close();
    
    // Green segment (bottom right)
    greenPath.moveTo(centerX, centerY);
    greenPath.lineTo(centerX + outerRadius * 0.6, centerY + outerRadius * 0.4);
    greenPath.arcTo(
      Rect.fromCircle(center: Offset(centerX, centerY), radius: outerRadius),
      Math.pi * 7 / 6,
      Math.pi / 3,
      false,
    );
    greenPath.lineTo(centerX, centerY);
    greenPath.close();
    
    // Draw each segment
    paint.color = blue;
    canvas.drawPath(bluePath, paint);
    
    paint.color = red;
    canvas.drawPath(redPath, paint);
    
    paint.color = yellow;
    canvas.drawPath(yellowPath, paint);
    
    paint.color = green;
    canvas.drawPath(greenPath, paint);
    
    // Draw white center
    paint.color = Colors.white;
    canvas.drawCircle(Offset(centerX, centerY), innerRadius, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Math utilities
class Math {
  static const double pi = 3.1415926535897932;
}
