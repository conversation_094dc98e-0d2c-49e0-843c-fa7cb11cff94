import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

/// A service to securely handle sensitive configuration data
class SecureConfigService {
  // Singleton pattern
  static final SecureConfigService _instance = SecureConfigService._internal();
  factory SecureConfigService() => _instance;
  SecureConfigService._internal();

  // Secure storage
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
  );

  // Storage keys
  static const String _apiKeyKey = 'firebase_api_key';
  static const String _projectIdKey = 'firebase_project_id';
  static const String _appIdKey = 'firebase_app_id';
  static const String _messagingSenderIdKey = 'firebase_messaging_sender_id';
  static const String _storageBucketKey = 'firebase_storage_bucket';

  // Initialize with Firebase config
  Future<void> initializeWithFirebaseConfig(Map<String, dynamic> config) async {
    try {
      // Extract and store sensitive data securely
      final projectInfo = config['project_info'] as Map<String, dynamic>?;
      final client = (config['client'] as List?)?.first as Map<String, dynamic>?;
      final clientInfo = client?['client_info'] as Map<String, dynamic>?;
      final apiKey = (client?['api_key'] as List?)?.first as Map<String, dynamic>?;

      if (projectInfo != null) {
        await _secureStorage.write(
          key: _projectIdKey,
          value: projectInfo['project_id'] as String?,
        );

        await _secureStorage.write(
          key: _storageBucketKey,
          value: projectInfo['storage_bucket'] as String?,
        );

        await _secureStorage.write(
          key: _messagingSenderIdKey,
          value: projectInfo['project_number'] as String?,
        );
      }

      if (clientInfo != null) {
        await _secureStorage.write(
          key: _appIdKey,
          value: clientInfo['mobilesdk_app_id'] as String?,
        );
      }

      if (apiKey != null) {
        await _secureStorage.write(
          key: _apiKeyKey,
          value: apiKey['current_key'] as String?,
        );
      }



      debugPrint('Firebase config securely stored');
    } catch (e) {
      debugPrint('Error storing Firebase config: $e');
    }
  }

  // Get Firebase API key
  Future<String?> getFirebaseApiKey() async {
    return await _secureStorage.read(key: _apiKeyKey);
  }

  // Get Firebase project ID
  Future<String?> getFirebaseProjectId() async {
    return await _secureStorage.read(key: _projectIdKey);
  }

  // Get Firebase app ID
  Future<String?> getFirebaseAppId() async {
    return await _secureStorage.read(key: _appIdKey);
  }

  // Get Firebase messaging sender ID
  Future<String?> getFirebaseMessagingSenderId() async {
    return await _secureStorage.read(key: _messagingSenderIdKey);
  }

  // Get Firebase storage bucket
  Future<String?> getFirebaseStorageBucket() async {
    return await _secureStorage.read(key: _storageBucketKey);
  }



  // Get all Firebase config as a map
  Future<Map<String, String?>> getFirebaseConfig() async {
    return {
      'apiKey': await getFirebaseApiKey(),
      'projectId': await getFirebaseProjectId(),
      'appId': await getFirebaseAppId(),
      'messagingSenderId': await getFirebaseMessagingSenderId(),
      'storageBucket': await getFirebaseStorageBucket(),
    };
  }

  // Hash sensitive data for logging (never log actual values)
  String hashSensitiveData(String data) {
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString().substring(0, 8); // Return only first 8 chars of hash
  }

  // Clear all stored config
  Future<void> clearConfig() async {
    await _secureStorage.deleteAll();
  }
}
