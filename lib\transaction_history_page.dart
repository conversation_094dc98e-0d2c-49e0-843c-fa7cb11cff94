import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:intl/intl.dart';
import 'dart:async';

import 'services/withdrawal_service.dart';
import 'utils/performance_optimizations.dart';

class TransactionHistoryPage extends StatefulWidget {
  const TransactionHistoryPage({Key? key}) : super(key: key);

  @override
  State<TransactionHistoryPage> createState() => _TransactionHistoryPageState();
}

class _TransactionHistoryPageState extends State<TransactionHistoryPage> with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  late TabController _tabController;
  final WithdrawalService _withdrawalService = WithdrawalService();

  List<Map<String, dynamic>> _transactions = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  String _errorMessage = '';

  // Pagination parameters
  static const int _pageSize = 10;
  DocumentSnapshot? _lastDocument;
  bool _hasMoreData = true;

  // Scroll controller for pagination
  late ScrollController _scrollController;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Initialize scroll controller
    _scrollController = ScrollController();
    _scrollController.addListener(_scrollListener);

    _loadTransactionHistory();
  }

  // Scroll listener for pagination
  void _scrollListener() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200 &&
        !_isLoading &&
        !_isLoadingMore &&
        _hasMoreData) {
      _loadMoreTransactions();
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  // Load transaction history (initial load) with performance optimization
  Future<void> _loadTransactionHistory() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _errorMessage = '';
      _lastDocument = null;
      _hasMoreData = true;
      _transactions = [];
    });

    // Use performance optimization to run this task in the next frame
    PerformanceOptimizations.scheduleTask(() async {
      try {
        // Get transactions without Firebase indexes (simple query)
        final user = FirebaseAuth.instance.currentUser;
        if (user == null) {
          throw Exception('User not authenticated');
        }

        // Simple query without orderBy to avoid index requirement
        final querySnapshot = await FirebaseFirestore.instance
            .collection('transactions')
            .where('userId', isEqualTo: user.uid)
            .limit(_pageSize)
            .get()
            .timeout(const Duration(seconds: 10), onTimeout: () {
          throw TimeoutException('Transaction history loading timed out');
        });

        if (!mounted) return;

        // Convert to list and sort in memory
        final transactions = querySnapshot.docs.map((doc) {
          final data = doc.data();
          data['id'] = doc.id;
          return data;
        }).toList();

        // Sort by timestamp in memory (newest first)
        transactions.sort((a, b) {
          final aDate = a['createdAt'] as Timestamp?;
          final bDate = b['createdAt'] as Timestamp?;

          if (aDate == null && bDate == null) return 0;
          if (aDate == null) return 1;
          if (bDate == null) return -1;

          return bDate.compareTo(aDate);
        });

        setState(() {
          _transactions = transactions;
          _lastDocument = querySnapshot.docs.isNotEmpty ? querySnapshot.docs.last : null;
          _hasMoreData = querySnapshot.docs.length == _pageSize;
          _isLoading = false;
        });
      } catch (e) {
        if (!mounted) return;

        setState(() {
          _isLoading = false;
          _errorMessage = 'Error loading transaction history: $e';
        });
      }
    });
  }

  // Load more transaction history (pagination) with performance optimization
  Future<void> _loadMoreTransactions() async {
    if (!_hasMoreData || _isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
    });

    // Use performance optimization to run this task in the next frame
    PerformanceOptimizations.scheduleTask(() async {
      try {
        // Get more transactions without Firebase indexes
        final user = FirebaseAuth.instance.currentUser;
        if (user == null) {
          throw Exception('User not authenticated');
        }

        // Simple query without orderBy, starting after last document
        Query query = FirebaseFirestore.instance
            .collection('transactions')
            .where('userId', isEqualTo: user.uid);

        if (_lastDocument != null) {
          query = query.startAfterDocument(_lastDocument!);
        }

        final querySnapshot = await query
            .limit(_pageSize)
            .get()
            .timeout(const Duration(seconds: 10), onTimeout: () {
          throw TimeoutException('Loading more transactions timed out');
        });

        if (!mounted) return;

        // Convert to list and sort in memory
        final newTransactions = querySnapshot.docs.map((doc) {
          final data = doc.data() as Map<String, dynamic>;
          data['id'] = doc.id;
          return data;
        }).toList();

        // Sort by timestamp in memory (newest first)
        newTransactions.sort((a, b) {
          final aDate = a['createdAt'] as Timestamp?;
          final bDate = b['createdAt'] as Timestamp?;

          if (aDate == null && bDate == null) return 0;
          if (aDate == null) return 1;
          if (bDate == null) return -1;

          return bDate.compareTo(aDate);
        });

        setState(() {
          _transactions.addAll(newTransactions);
          _lastDocument = querySnapshot.docs.isNotEmpty ? querySnapshot.docs.last : null;
          _hasMoreData = querySnapshot.docs.length == _pageSize;
          _isLoadingMore = false;
        });
      } catch (e) {
        if (!mounted) return;

        setState(() {
          _isLoadingMore = false;
          _errorMessage = 'Error loading more transactions: $e';
        });
      }
    });
  }

  // Format transaction date
  String _formatTransactionDate(dynamic timestamp) {
    if (timestamp == null) return 'Unknown date';

    DateTime date;
    if (timestamp is Timestamp) {
      date = timestamp.toDate();
    } else {
      // Try to parse the timestamp
      try {
        date = DateTime.parse(timestamp.toString());
      } catch (e) {
        return 'Unknown date';
      }
    }

    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      // Today
      return 'Today, ${DateFormat('h:mm a').format(date)}';
    } else if (difference.inDays == 1) {
      // Yesterday
      return 'Yesterday, ${DateFormat('h:mm a').format(date)}';
    } else if (difference.inDays < 7) {
      // This week
      return DateFormat('EEEE, h:mm a').format(date);
    } else {
      // Older
      return DateFormat('MMM d, yyyy').format(date);
    }
  }

  // Helper methods for transaction status
  Color _getStatusColor(String status) {
    switch (status) {
      case 'completed':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'rejected':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'pending':
        return 'Pending';
      case 'rejected':
        return 'Rejected';
      default:
        return 'Unknown';
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Transaction History',
          style: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        bottom: TabBar(
          controller: _tabController,
          labelColor: const Color(0xFF4F46E5),
          unselectedLabelColor: Colors.grey,
          indicatorColor: const Color(0xFF4F46E5),
          tabs: const [
            Tab(text: 'All'),
            Tab(text: 'Withdrawals'),
            Tab(text: 'Deposits'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage.isNotEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Error',
                        style: GoogleFonts.poppins(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.red,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 32),
                        child: Text(
                          _errorMessage,
                          textAlign: TextAlign.center,
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton(
                        onPressed: _loadTransactionHistory,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF4F46E5),
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        ),
                        child: Text(
                          'Try Again',
                          style: GoogleFonts.poppins(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              : TabBarView(
                  controller: _tabController,
                  children: [
                    // All transactions
                    _buildTransactionList(_transactions),

                    // Withdrawals
                    _buildTransactionList(
                      _transactions.where((t) =>
                        t['type'] == 'withdrawal' ||
                        t['type'] == 'withdrawal_request' ||
                        t['type'] == 'withdrawal_approved' ||
                        t['type'] == 'withdrawal_rejected'
                      ).toList(),
                    ),

                    // Deposits/Earnings
                    _buildTransactionList(
                      _transactions.where((t) =>
                        t['type'] == 'earning' ||
                        t['type'] == 'credit' ||
                        t['type'] == 'refund' ||
                        (t['type'] == 'debit' && t['description'].toString().toLowerCase().contains('refund'))
                      ).toList(),
                    ),
                  ],
                ),
    );
  }

  // Build transaction list
  Widget _buildTransactionList(List<Map<String, dynamic>> transactions) {
    if (transactions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.history,
              size: 64,
              color: Colors.grey.shade300,
            ),
            const SizedBox(height: 16),
            Text(
              'No transactions yet',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Your transaction history will appear here',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: transactions.length + (_hasMoreData && transactions.isNotEmpty ? 1 : 0),
      itemBuilder: (context, index) {
        // Show loading indicator at the bottom when loading more items
        if (index == transactions.length && _hasMoreData) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 16),
              child: CircularProgressIndicator(),
            ),
          );
        }

        // Regular transaction item
        final transaction = transactions[index];

        // Determine transaction type
        IconData icon;
        Color color;
        String prefix;
        String title;

        final String type = transaction['type'] ?? '';

        switch (type) {
          case 'withdrawal':
          case 'withdrawal_request':
            icon = Icons.arrow_upward;
            color = Colors.red;
            prefix = '-';
            title = 'Withdrawal';
            break;
          case 'withdrawal_approved':
            icon = Icons.check_circle_outline;
            color = Colors.green;
            prefix = '-';
            title = 'Withdrawal Approved';
            break;
          case 'withdrawal_rejected':
            icon = Icons.cancel_outlined;
            color = Colors.red;
            prefix = '';
            title = 'Withdrawal Rejected';
            break;
          case 'earning':
          case 'credit':
            icon = Icons.arrow_downward;
            color = Colors.green;
            prefix = '+';
            title = 'Earning';
            break;
          case 'refund':
            icon = Icons.replay;
            color = Colors.green;
            prefix = '+';
            title = 'Refund';
            break;
          case 'debit':
            // Check if this is a refund-related debit
            if (transaction['description'].toString().toLowerCase().contains('refund')) {
              icon = Icons.replay;
              color = Colors.green;
              prefix = '+';
              title = 'Refund';
            } else if (transaction['description'].toString().toLowerCase().contains('hire request')) {
              icon = Icons.work_outline;
              color = Colors.orange;
              prefix = '-';
              title = 'Hire Request Payment';
            } else {
              icon = Icons.remove_circle_outline;
              color = Colors.orange;
              prefix = '-';
              title = 'Payment';
            }
            break;
          case 'info':
            icon = Icons.info_outline;
            color = Colors.grey;
            prefix = '';
            title = 'Info';
            break;
          default:
            icon = Icons.swap_horiz;
            color = Colors.blue;
            prefix = '';
            title = 'Transaction';
        }

        // Get transaction status
        final String status = transaction['status'] ?? 'pending';

        // Use OptimizedScrollItem and OptimizedListItem for better performance
        return OptimizedScrollItem(
          keepAlive: index < 5, // Keep first 5 items alive for better scrolling
          child: OptimizedListItem(
            child: Container(
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(10),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ListTile(
                contentPadding: const EdgeInsets.all(16),
                leading: Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: color.withAlpha(30),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 24,
                  ),
                ),
                title: Text(
                  transaction['description'] ?? title,
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Show payment method for withdrawals
                    if (type.contains('withdrawal') && transaction['method'] != null)
                      Text(
                        'Method: ${transaction['method']}',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          color: Colors.black54,
                        ),
                      ),

                    // Show metadata for hire requests and refunds
                    if (transaction['metadata'] != null && transaction['metadata'] is Map) ...[
                      if (transaction['metadata']['hireRequestId'] != null)
                        Text(
                          'Hire Request ID: ${transaction['metadata']['hireRequestId']}',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            color: Colors.black54,
                          ),
                        ),
                      if (transaction['metadata']['refundReason'] != null)
                        Text(
                          'Reason: ${transaction['metadata']['refundReason']}',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            color: Colors.black54,
                          ),
                        ),
                    ],

                    // Show transaction date
                    Text(
                      'Date: ${_formatTransactionDate(transaction['requestDate'] ?? transaction['timestamp'] ?? transaction['createdAt'])}',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.black54,
                      ),
                    ),
                    Row(
                      children: [
                        Text(
                          'Status: ',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            color: Colors.black54,
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: _getStatusColor(status).withAlpha(30),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            _getStatusText(status),
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color: _getStatusColor(status),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                trailing: Text(
                  '$prefix\$${(transaction['amount'] as num).toStringAsFixed(2)}',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
