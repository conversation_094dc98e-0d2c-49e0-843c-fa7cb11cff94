import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:async';
import 'chat_screen.dart';
import 'services/messaging_service.dart';
import 'services/notification_service.dart';

class MessagingPage extends StatefulWidget {
  const MessagingPage({super.key});

  @override
  MessagingPageState createState() => MessagingPageState();
}

class MessagingPageState extends State<MessagingPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  String _searchQuery = '';
  bool _isLoading = true;
  String? _errorMessage;
  StreamSubscription? _conversationsSubscription;
  bool _currentTabIsHireRequests = false; // Track which tab we're on for UI rendering
  int _selectedTabIndex = 0; // Track the selected tab index

  // Services
  final MessagingService _messagingService = MessagingService();

  // Conversations data
  List<Map<String, dynamic>> _conversations = [];

  List<Map<String, dynamic>> get _filteredConversations {
    if (_searchQuery.isEmpty) {
      return _conversations;
    }

    return _conversations.where((conversation) {
      final name = conversation['name'].toString().toLowerCase();
      final lastMessage = conversation['lastMessage'].toString().toLowerCase();
      final query = _searchQuery.toLowerCase();

      return name.contains(query) || lastMessage.contains(query);
    }).toList();
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _searchController.addListener(_updateSearchQuery);

    // Only use direct loading for better stability
    _loadConversationsDirectly();

    // Set up a timer to refresh conversations periodically
    Timer.periodic(const Duration(seconds: 30), (timer) {
      if (mounted) {
        _loadConversationsDirectly();
      } else {
        timer.cancel();
      }
    });
  }



  // Fallback method to load conversations directly from Firestore
  Future<void> _loadConversationsDirectly() async {
    try {
      debugPrint('MessagingPage - _loadConversationsDirectly: Loading conversations directly');

      if (_messagingService.currentUserId == null) {
        debugPrint('MessagingPage - _loadConversationsDirectly: User not authenticated');
        setState(() {
          _errorMessage = 'You need to be logged in to view messages';
          _isLoading = false;
        });
        return;
      }

      // Get conversations from Firestore without using orderBy to avoid requiring an index
      final QuerySnapshot snapshot = await FirebaseFirestore.instance
          .collection('conversations')
          .where('participants', arrayContains: _messagingService.currentUserId)
          .get();

      debugPrint('MessagingPage - _loadConversationsDirectly: Got ${snapshot.docs.length} conversations');

      // Get hire requests from Firestore
      final QuerySnapshot hireRequestsSnapshot = await FirebaseFirestore.instance
          .collection('hire_requests')
          .where('clientId', isEqualTo: _messagingService.currentUserId)
          .get();

      final QuerySnapshot developerHireRequestsSnapshot = await FirebaseFirestore.instance
          .collection('hire_requests')
          .where('developerId', isEqualTo: _messagingService.currentUserId)
          .get();

      debugPrint('MessagingPage - _loadConversationsDirectly: Got ${hireRequestsSnapshot.docs.length} client hire requests');
      debugPrint('MessagingPage - _loadConversationsDirectly: Got ${developerHireRequestsSnapshot.docs.length} developer hire requests');

      // Log the raw data for debugging
      for (final doc in snapshot.docs) {
        debugPrint('Raw conversation data: ${doc.id} - ${doc.data()}');
      }

      final List<Map<String, dynamic>> conversations = [];

      for (final doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final String conversationId = doc.id;

        // Get the other participant's ID
        final List<String> participants = List<String>.from(data['participants'] ?? []);
        final String otherUserId = participants.firstWhere(
          (id) => id != _messagingService.currentUserId,
          orElse: () => '',
        );

        if (otherUserId.isEmpty) continue;

        // Get the other user's data
        final otherUserDoc = await FirebaseFirestore.instance.collection('users').doc(otherUserId).get();
        if (!otherUserDoc.exists) continue;

        final otherUserData = otherUserDoc.data() ?? {};

        // Create conversation map
        final Map<String, dynamic> conversation = {
          'id': conversationId,
          'name': otherUserData['displayName'] ?? 'Unknown User',
          'avatar': otherUserData['photoURL'] ?? '',
          'lastMessage': data['lastMessage'] ?? '',
          'time': _formatTimestamp(data['lastMessageTimestamp']),
          'rawTimestamp': data['lastMessageTimestamp'], // Store raw timestamp for sorting
          'unread': 0, // Will be updated later
          'isOnline': false, // Will be updated later
          'type': data['type'] ?? 'normal',
          'otherUserId': otherUserId,
          'isNew': data['isNew'] ?? false,
          'welcomeMessageSent': data['welcomeMessageSent'] ?? false,
        };

        // Get unread count
        try {
          if (data.containsKey('unreadCount')) {
            if (data['unreadCount'] is Map) {
              final Map<String, dynamic> unreadCountMap = Map<String, dynamic>.from(data['unreadCount']);

              // Check for both string and non-string keys for backward compatibility
              final String currentUserIdStr = _messagingService.currentUserId.toString();

              if (unreadCountMap.containsKey(currentUserIdStr)) {
                final dynamic countValue = unreadCountMap[currentUserIdStr];
                if (countValue is int) {
                  conversation['unread'] = countValue;
                } else if (countValue is num) {
                  conversation['unread'] = countValue.toInt();
                } else if (countValue is String) {
                  conversation['unread'] = int.tryParse(countValue) ?? 0;
                }
              } else if (unreadCountMap.containsKey(_messagingService.currentUserId)) {
                final dynamic countValue = unreadCountMap[_messagingService.currentUserId];
                if (countValue is int) {
                  conversation['unread'] = countValue;
                } else if (countValue is num) {
                  conversation['unread'] = countValue.toInt();
                } else if (countValue is String) {
                  conversation['unread'] = int.tryParse(countValue) ?? 0;
                }
              }

              debugPrint('MessagingPage - Conversation $conversationId unread count for user ${_messagingService.currentUserId}: ${conversation['unread']}');
            } else if (data['unreadCount'] is int) {
              // Legacy format: single integer for unread count
              conversation['unread'] = (data['unreadCount'] as int);
              debugPrint('MessagingPage - Conversation $conversationId legacy unread count: ${conversation['unread']}');
            }
          }
        } catch (e) {
          debugPrint('Error getting unread count: $e');
        }

        // Check for hire request data
        bool isHireRequest = false;

        // Check if it's explicitly marked as hire type
        if (data['type'] == 'hire') {
          isHireRequest = true;
        }

        // Check if it has a status field (which indicates it's a hire request)
        if (data['status'] != null) {
          isHireRequest = true;
        }

        // Check if it has any hire-related fields
        if (data['project'] != null || data['budget'] != null || data['duration'] != null) {
          isHireRequest = true;
        }

        // Check if any message in the conversation is a hire request
        // This is a more expensive check, but ensures we catch all hire requests
        try {
          // Get a small batch of messages and check in code to avoid using indexes
          final messagesSnapshot = await FirebaseFirestore.instance
              .collection('conversations')
              .doc(conversationId)
              .collection('messages')
              .limit(10) // Just get the most recent messages
              .get();

          // Check if any of these messages is a hire request
          for (final messageDoc in messagesSnapshot.docs) {
            final messageData = messageDoc.data();
            if (messageData['isHireRequest'] == true) {
              isHireRequest = true;
              debugPrint('MessagingPage - Found hire request message in conversation: $conversationId');
              break;
            }
          }
        } catch (e) {
          debugPrint('Error checking for hire request messages: $e');
        }

        // If it's a hire request, add the necessary fields
        if (isHireRequest) {
          conversation['type'] = 'hire';
          conversation['status'] = data['status'] ?? 'pending';
          conversation['project'] = data['project'] ?? 'Custom Project';
          conversation['budget'] = data['budget'] ?? 'Not specified';
          conversation['duration'] = data['duration'] ?? 'Not specified';
          debugPrint('MessagingPage - Marked conversation $conversationId as hire request');
        }

        conversations.add(conversation);
      }

      // Process hire requests and add them to conversations
      // First, combine both hire request snapshots
      final List<QueryDocumentSnapshot> allHireRequests = [
        ...hireRequestsSnapshot.docs,
        ...developerHireRequestsSnapshot.docs,
      ];

      // Process each hire request
      for (final doc in allHireRequests) {
        final data = doc.data() as Map<String, dynamic>;
        final String hireRequestId = doc.id;

        // Skip if this hire request is already in the conversations list
        if (conversations.any((conv) => conv['hireRequestId'] == hireRequestId)) {
          continue;
        }

        // Get the conversation ID
        final String? conversationId = data['conversationId'];

        // If there's no conversation ID yet, create a placeholder conversation
        if (conversationId == null || conversationId.isEmpty) {
          // Determine if the current user is the client or developer
          final bool isClient = data['clientId'] == _messagingService.currentUserId;
          final String otherUserId = isClient ? data['developerId'] : data['clientId'];

          // Get the other user's data
          final otherUserDoc = await FirebaseFirestore.instance.collection('users').doc(otherUserId).get();
          if (!otherUserDoc.exists) continue;

          final otherUserData = otherUserDoc.data() ?? {};

          // Create a placeholder conversation with accurate information
          final Map<String, dynamic> conversation = {
            'id': 'hire_request_$hireRequestId', // Placeholder ID
            'name': otherUserData['displayName'] ?? 'Unknown User',
            'avatar': otherUserData['photoURL'] ?? '',
            'lastMessage': 'New hire request: ${data['planName']}',
            'time': _formatTimestamp(data['createdAt']),
            'rawTimestamp': data['createdAt'], // Store raw timestamp for sorting
            'unread': 1, // Mark as unread
            'isOnline': false,
            'type': 'hire',
            'otherUserId': otherUserId,
            'isNew': true,
            'welcomeMessageSent': false,
            'status': data['status'] ?? 'pending',
            'project': data['planName'] ?? 'Custom Project',
            'budget': data['budget'] != null ? '\$${data['budget']}' : 'Not specified',
            'duration': data['timeframe'] ?? 'Not specified',
            'hireRequestId': hireRequestId,
            'uniqueId': data['uniqueId'] ?? DateTime.now().millisecondsSinceEpoch.toString(), // Include unique ID
          };

          conversations.add(conversation);
        }
      }

      // Sort conversations by timestamp
      debugPrint('MessagingPage - _loadConversationsDirectly: Sorting conversations');
      conversations.sort((a, b) {
        // Try to use raw timestamps first
        final Timestamp? timestampA = a['rawTimestamp'] as Timestamp?;
        final Timestamp? timestampB = b['rawTimestamp'] as Timestamp?;

        // If both have timestamps, compare them (newest first)
        if (timestampA != null && timestampB != null) {
          return timestampB.compareTo(timestampA);
        }

        // If only one has a timestamp, prioritize that one
        if (timestampA != null) return -1;
        if (timestampB != null) return 1;

        // Fallback to string-based comparison
        final String timeA = a['time'] as String? ?? '';
        final String timeB = b['time'] as String? ?? '';

        // Special case for "Just now" which should be first
        if (timeA == 'Just now' && timeB != 'Just now') return -1;
        if (timeB == 'Just now' && timeA != 'Just now') return 1;

        // Default to alphabetical sorting of the time strings
        return timeB.compareTo(timeA);
      });

      if (mounted) {
        setState(() {
          _conversations = conversations;
          _isLoading = false;

          // Clear error message
          _errorMessage = null;
        });
      }

      debugPrint('MessagingPage - _loadConversationsDirectly: Loaded ${conversations.length} sorted conversations');
    } catch (e) {
      debugPrint('MessagingPage - Error loading conversations directly: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Error loading conversations: $e';
        });
      }
    }
  }



  // Format timestamp for display
  String _formatTimestamp(dynamic timestamp) {
    if (timestamp == null) return 'Just now';

    final DateTime now = DateTime.now();
    final DateTime messageTime = timestamp is Timestamp
        ? timestamp.toDate()
        : DateTime.now();

    final Duration difference = now.difference(messageTime);

    if (difference.inSeconds < 60) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${messageTime.day}/${messageTime.month}/${messageTime.year}';
    }
  }

  // Reset unread count for a conversation
  Future<void> _resetUnreadCount(String conversationId) async {
    try {
      // Skip if this is a placeholder hire request ID
      if (conversationId.startsWith('hire_request_')) {
        return;
      }

      debugPrint('MessagingPage - Resetting unread count for conversation: $conversationId');

      // First, get the current unreadCount map to preserve other users' counts
      final DocumentSnapshot conversationDoc = await FirebaseFirestore.instance
          .collection('conversations')
          .doc(conversationId)
          .get();

      // Extract the current unreadCount map
      Map<String, dynamic> unreadCountMap = {};
      if (conversationDoc.exists) {
        final data = conversationDoc.data() as Map<String, dynamic>;
        if (data.containsKey('unreadCount') && data['unreadCount'] is Map) {
          unreadCountMap = Map<String, dynamic>.from(data['unreadCount']);
        }
      }

      // Update only the current user's unread count
      unreadCountMap[_messagingService.currentUserId.toString()] = 0;

      // Update the conversation document
      await FirebaseFirestore.instance
          .collection('conversations')
          .doc(conversationId)
          .update({
            'unreadCount': unreadCountMap,
            'lastReadAt': FieldValue.serverTimestamp(),
          });

      // Call the messaging service to mark messages as read
      await _messagingService.markMessagesAsRead(conversationId);

      // Force update the user's total unread counts
      await _messagingService.updateUserUnreadMessageCount();

      // Update the UI to reflect the changes
      if (mounted) {
        setState(() {
          // Find the conversation in the list and update its unread count
          for (final conversation in _conversations) {
            if (conversation['id'] == conversationId) {
              conversation['unread'] = 0;
              debugPrint('MessagingPage - Updated local conversation unread count to 0');
              break;
            }
          }
        });

        // Reload conversations to refresh the UI after a short delay
        // This ensures Firebase has time to update
        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) {
            _loadConversationsDirectly();
            debugPrint('MessagingPage - Reloaded conversations after reset');
          }
        });
      }

      debugPrint('MessagingPage - Reset unread count for conversation: $conversationId');
    } catch (e) {
      debugPrint('MessagingPage - Error resetting unread count: $e');
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();

    // Cancel the conversations subscription if it exists
    _conversationsSubscription?.cancel();

    super.dispose();
  }

  void _updateSearchQuery() {
    setState(() {
      _searchQuery = _searchController.text;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: _isSearching
          ? TextField(
              controller: _searchController,
              autofocus: true,
              decoration: InputDecoration(
                hintText: 'Search conversations...',
                border: InputBorder.none,
                hintStyle: GoogleFonts.poppins(
                  color: Colors.grey[400],
                  fontSize: 16,
                ),
                prefixIcon: const Icon(Icons.search, color: Colors.grey),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    setState(() {
                      _searchController.clear();
                      _searchQuery = '';
                    });
                  },
                ),
              ),
              onSubmitted: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            )
          : Text(
              'Messages',
              style: GoogleFonts.poppins(
                color: Colors.black87,
                fontWeight: FontWeight.bold,
              ),
            ),
        actions: [
          if (!_isSearching)
            IconButton(
              icon: const Icon(Icons.search, color: Colors.black87),
              onPressed: () {
                setState(() {
                  _isSearching = true;
                });
              },
            ),
          if (_isSearching)
            IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.black87),
              onPressed: () {
                setState(() {
                  _isSearching = false;
                  _searchController.clear();
                  _searchQuery = '';
                });
              },
            ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(48),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom: BorderSide(
                  color: Colors.grey[200]!,
                  width: 1,
                ),
              ),
            ),
            child: TabBar(
              controller: _tabController,
              labelColor: const Color(0xFF6A11CB),
              unselectedLabelColor: Colors.black54,
              labelStyle: GoogleFonts.poppins(
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
              unselectedLabelStyle: GoogleFonts.poppins(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
              indicatorColor: const Color(0xFF6A11CB),
              indicatorWeight: 3,
              indicatorSize: TabBarIndicatorSize.label,
              tabs: [
                _buildTabWithBadge('All Messages', false),
                _buildTabWithBadge('Hire Requests', true),
              ],
              onTap: (index) {
                // When tab is tapped, update the UI to reflect the current tab
                setState(() {
                  _selectedTabIndex = index;
                  _currentTabIsHireRequests = index == 1;
                });
              },
            ),
          ),
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // All messages tab
          _buildConversationsList(false),

          // Hire requests tab
          _buildConversationsList(true),
        ],
      ),
    );
  }

  Widget _buildConversationsList(bool hireRequestsOnly) {
    // Show loading indicator
    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6A11CB)),
            ),
            const SizedBox(height: 16),
            Text(
              'Loading conversations...',
              style: GoogleFonts.poppins(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    // Make a deep copy of the conversations to avoid reference issues
    List<Map<String, dynamic>> allConversations = _filteredConversations.map((conv) => Map<String, dynamic>.from(conv)).toList();

    // Ensure all conversations with status are marked as hire type
    for (var conversation in allConversations) {
      if (conversation['status'] != null && conversation['type'] != 'hire') {
        conversation['type'] = 'hire';
        debugPrint('MessagingPage - Marking conversation ${conversation['id']} as hire type because it has status: ${conversation['status']}');
      }
    }

    // Filter conversations based on tab
    final List<Map<String, dynamic>> conversations = allConversations.where((conversation) {
      if (hireRequestsOnly) {
        // In Hire Requests tab, only show placeholder hire requests or conversations with hire request ID
        return conversation['id'].toString().startsWith('hire_request_') ||
               (conversation['type'] == 'hire' && conversation['status'] == 'pending');
      } else {
        // In All Messages tab, show all regular conversations and accepted/declined hire requests
        return !conversation['id'].toString().startsWith('hire_request_') &&
               !(conversation['type'] == 'hire' && conversation['status'] == 'pending');
      }
    }).toList();

    // Log the conversations for debugging
    if (hireRequestsOnly) {
      debugPrint('MessagingPage - Hire Requests Tab: Found ${conversations.length} hire requests');
      for (final conversation in conversations) {
        debugPrint('Hire Request: ${conversation['id']}, Type: ${conversation['type']}, Status: ${conversation['status'] ?? 'unknown'}');
      }
    }

    // Store the current tab for UI rendering
    _currentTabIsHireRequests = hireRequestsOnly;

    if (conversations.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              hireRequestsOnly ? Icons.work_outline : Icons.message_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              hireRequestsOnly
                ? 'No hire requests yet'
                : 'No messages yet',
              style: GoogleFonts.poppins(
                fontSize: 16,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            // Show error message if there is one
            if (_errorMessage != null) ...[
              const SizedBox(height: 8),
              Text(
                _errorMessage!,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.red[400],
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
            if (_searchQuery.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'Try a different search term',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.grey[500],
                ),
              ),
            ],
            // Empty space at the bottom
            const SizedBox(height: 24),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: conversations.length,
      itemBuilder: (context, index) {
        final conversation = conversations[index];
        return _buildConversationTile(conversation);
      },
    );
  }

  Widget _buildConversationTile(Map<String, dynamic> conversation) {
    final bool isHireRequest = conversation['type'] == 'hire';
    final String status = conversation['status'] ?? '';

    // Determine if we should show the hire request UI
    // Only show hire request UI in the hire requests tab or if it's a pending hire request in the all messages tab
    final bool showHireRequestUI = _currentTabIsHireRequests ||
                                  (isHireRequest && status == 'pending');

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          // Navigate to chat screen
          _openChatScreen(conversation);
        },
        onLongPress: () {
          // Show conversation options
          _showConversationOptions(conversation);
        },
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Avatar with online indicator
                  Stack(
                    children: [
                      CircleAvatar(
                        radius: 24,
                        backgroundColor: Colors.grey[200],
                        child: Text(
                          conversation['name'].substring(0, 1),
                          style: GoogleFonts.poppins(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF6A11CB),
                          ),
                        ),
                      ),
                      if (conversation['isOnline'] == true)
                        Positioned(
                          right: 0,
                          bottom: 0,
                          child: Container(
                            width: 12,
                            height: 12,
                            decoration: BoxDecoration(
                              color: Colors.green,
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: Colors.white,
                                width: 2,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(width: 12),

                  // Name and message
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              conversation['name'],
                              style: GoogleFonts.poppins(
                                fontWeight: FontWeight.w600,
                                fontSize: 15,
                                color: Colors.black87,
                              ),
                            ),
                            Row(
                              children: [
                                Text(
                                  conversation['time'],
                                  style: GoogleFonts.poppins(
                                    fontSize: 12,
                                    color: Colors.grey[600],
                                  ),
                                ),
                                IconButton(
                                  icon: Icon(Icons.more_vert, color: Colors.grey[600], size: 18),
                                  padding: EdgeInsets.zero,
                                  constraints: const BoxConstraints(),
                                  visualDensity: VisualDensity.compact,
                                  onPressed: () => _showConversationOptions(conversation),
                                ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            // Only show status badge if we're showing hire request UI
                            if (showHireRequestUI && isHireRequest)
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                margin: const EdgeInsets.only(right: 6),
                                decoration: BoxDecoration(
                                  color: _getStatusColor(status).withAlpha(25),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  status.toUpperCase(),
                                  style: GoogleFonts.poppins(
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                    color: _getStatusColor(status),
                                  ),
                                ),
                              ),
                            Expanded(
                              child: Text(
                                conversation['lastMessage'],
                                style: GoogleFonts.poppins(
                                  fontSize: 13,
                                  color: _getUnreadCount(conversation) > 0
                                    ? Colors.black87
                                    : Colors.grey[600],
                                  fontWeight: _getUnreadCount(conversation) > 0
                                    ? FontWeight.w500
                                    : FontWeight.normal,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            if (_getUnreadCount(conversation) > 0)
                              Container(
                                margin: const EdgeInsets.only(left: 6),
                                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: const Color(0xFF6A11CB),
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: Text(
                                  _getUnreadCount(conversation) > 99 ? '99+' : _getUnreadCount(conversation).toString(),
                                  style: GoogleFonts.poppins(
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              // Additional info for hire requests - only show in hire requests tab or for pending requests
              if (showHireRequestUI && isHireRequest) ...[
                const SizedBox(height: 12),
                const Divider(height: 1),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildHireRequestInfo(
                      Icons.work_outline,
                      'Project',
                      conversation['project']
                    ),
                    _buildHireRequestInfo(
                      Icons.attach_money,
                      'Budget',
                      conversation['budget']
                    ),
                    FutureBuilder<String>(
                      future: _getActualDeliveryTime(conversation),
                      builder: (context, snapshot) {
                        // Get the delivery time from the future or use the conversation value
                        final String deliveryTime = snapshot.data ?? conversation['duration'] ?? '';

                        return _buildHireRequestInfo(
                          Icons.timer_outlined,
                          'Duration',
                          deliveryTime
                        );
                      },
                    ),
                  ],
                ),

                // Action buttons for pending hire requests - only show for developers, not clients
                if (status == 'pending') ...[
                  FutureBuilder<bool>(
                    future: _isCurrentUserDeveloper(conversation),
                    builder: (context, snapshot) {
                      final bool isDeveloper = snapshot.data ?? false;

                      if (isDeveloper) {
                        return Column(
                          children: [
                            const SizedBox(height: 16),
                            Row(
                              children: [
                                Expanded(
                                  child: OutlinedButton(
                                    onPressed: () {
                                      _respondToHireRequest(conversation, false);
                                    },
                                    style: OutlinedButton.styleFrom(
                                      foregroundColor: Colors.red[700],
                                      side: BorderSide(color: Colors.red[300]!),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      padding: const EdgeInsets.symmetric(vertical: 10),
                                    ),
                                    child: Text(
                                      'Decline',
                                      style: GoogleFonts.poppins(
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: ElevatedButton(
                                    onPressed: () {
                                      _respondToHireRequest(conversation, true);
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: const Color(0xFF6A11CB),
                                      foregroundColor: Colors.white,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      padding: const EdgeInsets.symmetric(vertical: 10),
                                    ),
                                    child: Text(
                                      'Accept',
                                      style: GoogleFonts.poppins(
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        );
                      } else {
                        return Column(
                          children: [
                            const SizedBox(height: 16),
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.orange[50],
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.orange[200]!),
                              ),
                              child: Row(
                                children: [
                                  Icon(Icons.info_outline, color: Colors.orange[700], size: 20),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      'Waiting for developer to respond to your request',
                                      style: GoogleFonts.poppins(
                                        fontSize: 13,
                                        color: Colors.orange[800],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        );
                      }
                    },
                  ),
                ],
              ],
            ],
          ),
        ),
      ),
    ).animate().fadeIn(duration: 300.ms);
  }

  // Get the actual delivery time from a hire request
  Future<String> _getActualDeliveryTime(Map<String, dynamic> conversation) async {
    try {
      final String hireRequestId = conversation['hireRequestId'] ?? '';
      if (hireRequestId.isEmpty) return 'Not specified';

      // Get the hire request from Firestore
      final DocumentSnapshot hireRequestDoc = await FirebaseFirestore.instance
          .collection('hire_requests')
          .doc(hireRequestId)
          .get();

      if (!hireRequestDoc.exists) return 'Not specified';

      final hireRequestData = hireRequestDoc.data() as Map<String, dynamic>;

      // First check if deliveryTime is explicitly set (preferred field name)
      if (hireRequestData.containsKey('deliveryTime') && hireRequestData['deliveryTime'] is String) {
        final String deliveryTime = hireRequestData['deliveryTime'];
        if (deliveryTime.isNotEmpty && deliveryTime != 'project' && deliveryTime != 'Project') {
          return deliveryTime;
        }
      }

      // Then check if deliveryDays is explicitly set
      if (hireRequestData.containsKey('deliveryDays') && hireRequestData['deliveryDays'] is num) {
        final int deliveryDays = (hireRequestData['deliveryDays'] as num).toInt();
        return '$deliveryDays days';
      }

      // Then check deadline field
      if (hireRequestData.containsKey('deadline') && hireRequestData['deadline'] is String) {
        final String deadline = hireRequestData['deadline'];
        if (deadline.isNotEmpty && deadline != 'project' && deadline != 'Project') {
          return deadline;
        }
      }

      // Then check timeframe
      final String timeframe = hireRequestData['timeframe'] ?? '';
      if (timeframe.isNotEmpty && timeframe != 'project' && timeframe != 'Project' &&
          timeframe != 'project in duration') {
        return timeframe;
      }

      // If we still don't have a valid delivery time, check the plan
      final String planId = hireRequestData['planId'] ?? '';
      if (planId.isNotEmpty && planId != 'custom') {
        final DocumentSnapshot planDoc = await FirebaseFirestore.instance
            .collection('hire_plans')
            .doc(planId)
            .get();

        if (planDoc.exists) {
          final planData = planDoc.data() as Map<String, dynamic>;

          // Check for deliveryTime in the plan (preferred field name)
          if (planData.containsKey('deliveryTime') && planData['deliveryTime'] is String) {
            final String deliveryTime = planData['deliveryTime'];
            if (deliveryTime.isNotEmpty) {
              return deliveryTime;
            }
          }

          // Check for duration in the plan
          if (planData.containsKey('duration') && planData['duration'] is String) {
            final String duration = planData['duration'];
            if (duration != 'Project' && duration != 'project') {
              return duration;
            }
          }

          // Check features for delivery time
          if (planData.containsKey('features') && planData['features'] is List) {
            for (final feature in planData['features']) {
              if (feature is String &&
                  (feature.toLowerCase().contains('delivery time') ||
                   feature.toLowerCase().contains('delivery in'))) {
                // Extract the number from the feature string
                final RegExp regex = RegExp(r'(\d+)\s*days?', caseSensitive: false);
                final match = regex.firstMatch(feature);
                if (match != null) {
                  return '${match.group(1)} days';
                }
                return feature;
              }
            }
          }
        }
      }

      // Default fallback
      return 'As per plan';
    } catch (e) {
      debugPrint('Error getting actual delivery time: $e');
      return 'Not specified';
    }
  }

  Widget _buildHireRequestInfo(IconData icon, String label, String value) {
    // Format the value for better display
    String displayValue = value.isEmpty ? 'Not specified' : value;

    // Format budget if it's a number
    if (label == 'Budget' && !displayValue.startsWith('\$') && displayValue != 'Not specified') {
      try {
        final double? budget = double.tryParse(displayValue);
        if (budget != null) {
          displayValue = '\$${budget.toStringAsFixed(2)}';
        }
      } catch (_) {
        // If parsing fails, use the original value
      }
    }

    // Format duration for better readability
    if (label == 'Duration') {
      if (displayValue == 'project in duration' || displayValue == 'project') {
        // Replace the placeholder text with a more meaningful message
        displayValue = 'As per plan';
      } else if (!displayValue.contains('days') && displayValue != 'Not specified') {
        // Try to interpret as a timeframe
        if (RegExp(r'^\d+$').hasMatch(displayValue)) {
          // If it's just a number, assume days
          displayValue = '$displayValue days';
        } else {
          // Try to extract a number
          final RegExp regex = RegExp(r'(\d+)', caseSensitive: false);
          final match = regex.firstMatch(displayValue);
          if (match != null) {
            displayValue = '${match.group(1)} days';
          }
        }
      }
    }

    return Expanded(
      child: Column(
        children: [
          Icon(icon, size: 18, color: Colors.grey[600]),
          const SizedBox(height: 4),
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
          Tooltip(
            message: displayValue,
            child: Text(
              displayValue,
              style: GoogleFonts.poppins(
                fontSize: 13,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to get the unread count from a conversation
  int _getUnreadCount(Map<String, dynamic> conversation) {
    // First check the unreadCount map (preferred method)
    if (conversation.containsKey('unreadCount') && conversation['unreadCount'] is Map) {
      final unreadCountMap = Map<String, dynamic>.from(conversation['unreadCount']);
      final String currentUserIdStr = _messagingService.currentUserId.toString();

      if (unreadCountMap.containsKey(currentUserIdStr)) {
        final dynamic countValue = unreadCountMap[currentUserIdStr];
        if (countValue is int) {
          return countValue;
        } else if (countValue is num) {
          return countValue.toInt();
        } else if (countValue is String) {
          return int.tryParse(countValue) ?? 0;
        }
      }
    }

    // Fallback to the legacy unread field
    if (conversation.containsKey('unread')) {
      if (conversation['unread'] is int) {
        return conversation['unread'];
      } else if (conversation['unread'] is num) {
        return (conversation['unread'] as num).toInt();
      } else if (conversation['unread'] is String) {
        return int.tryParse(conversation['unread']) ?? 0;
      }
    }

    return 0;
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange[700]!;
      case 'accepted':
        return Colors.green[700]!;
      case 'rejected':
      case 'declined':
        return Colors.red[700]!;
      default:
        return Colors.grey[700]!;
    }
  }

  // Check if the current user is the developer for this hire request
  Future<bool> _isCurrentUserDeveloper(Map<String, dynamic> conversation) async {
    try {
      // If this is a placeholder hire request
      if (conversation['id'].toString().startsWith('hire_request_')) {
        // Get the hire request ID from the conversation
        final String hireRequestId = conversation['hireRequestId'];
        if (hireRequestId.isEmpty) return false;

        // Get the hire request from Firestore
        final DocumentSnapshot hireRequestDoc = await FirebaseFirestore.instance
            .collection('hire_requests')
            .doc(hireRequestId)
            .get();

        if (!hireRequestDoc.exists) return false;

        final hireRequestData = hireRequestDoc.data() as Map<String, dynamic>;

        // Check if the current user is the developer
        return hireRequestData['developerId'] == _messagingService.currentUserId;
      }
      // If this is a regular conversation with hire request
      else if (conversation['type'] == 'hire' && conversation.containsKey('hireRequestId')) {
        final String hireRequestId = conversation['hireRequestId'];
        if (hireRequestId.isEmpty) return false;

        // Get the hire request from Firestore
        final DocumentSnapshot hireRequestDoc = await FirebaseFirestore.instance
            .collection('hire_requests')
            .doc(hireRequestId)
            .get();

        if (!hireRequestDoc.exists) return false;

        final hireRequestData = hireRequestDoc.data() as Map<String, dynamic>;

        // Check if the current user is the developer
        return hireRequestData['developerId'] == _messagingService.currentUserId;
      }

      return false;
    } catch (e) {
      debugPrint('Error checking if current user is developer: $e');
      return false;
    }
  }

  void _openChatScreen(Map<String, dynamic> conversation) async {
    // Check if this is a hire request without a conversation
    if (conversation['id'].startsWith('hire_request_') && conversation.containsKey('hireRequestId')) {
      // Show loading indicator
      setState(() {
        _isLoading = true;
      });

      try {
        // Get the hire request ID
        final String hireRequestId = conversation['hireRequestId'];

        // Get the hire request data to check if it already has a conversation
        final hireRequestDoc = await FirebaseFirestore.instance
            .collection('hire_requests')
            .doc(hireRequestId)
            .get();

        if (!hireRequestDoc.exists) {
          throw Exception('Hire request not found');
        }

        final hireRequestData = hireRequestDoc.data() as Map<String, dynamic>;
        final String existingConversationId = hireRequestData['conversationId'] ?? '';
        final bool forceNewConversation = hireRequestData['forceNewConversation'] ?? false;

        // Check if we should force a new conversation
        if (forceNewConversation) {
          // Always create a new conversation for this hire request
          debugPrint('Forcing new conversation for hire request: $hireRequestId');
          await _createNewConversationForHireRequest(conversation, hireRequestId);

          // Update the hire request to not force a new conversation next time
          await FirebaseFirestore.instance
              .collection('hire_requests')
              .doc(hireRequestId)
              .update({
                'forceNewConversation': false,
              });
        }
        // If the hire request already has a conversation ID, use that
        else if (existingConversationId.isNotEmpty) {
          // Check if the conversation exists
          final conversationDoc = await FirebaseFirestore.instance
              .collection('conversations')
              .doc(existingConversationId)
              .get();

          if (conversationDoc.exists) {
            // Use the existing conversation
            conversation['id'] = existingConversationId;
            debugPrint('Using existing conversation: $existingConversationId for hire request: $hireRequestId');
          } else {
            // Conversation doesn't exist, create a new one
            await _createNewConversationForHireRequest(conversation, hireRequestId);
          }
        } else {
          // Create a new conversation for this hire request
          await _createNewConversationForHireRequest(conversation, hireRequestId);
        }

        // Hide loading indicator
        setState(() {
          _isLoading = false;
        });

        // Navigate to chat screen
        _navigateToChatScreen(conversation);
      } catch (e) {
        // Hide loading indicator
        setState(() {
          _isLoading = false;
        });

        // Show error message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error creating conversation: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }

        return; // Don't proceed to chat screen
      }
    } else {
      // For regular conversations, just navigate to chat screen
      _navigateToChatScreen(conversation);
    }
  }

  // Helper method to create a new conversation for a hire request
  Future<void> _createNewConversationForHireRequest(Map<String, dynamic> conversation, String hireRequestId) async {
    final String currentUserId = _messagingService.currentUserId!;
    final String otherUserId = conversation['otherUserId'];

    // Generate a unique conversation ID
    final String uniqueConversationId = 'hire_${DateTime.now().millisecondsSinceEpoch}_${currentUserId.substring(0, 4)}';

    // Create the conversation document
    final conversationRef = await FirebaseFirestore.instance.collection('conversations').add({
      'participants': [currentUserId, otherUserId],
      'createdAt': FieldValue.serverTimestamp(),
      'lastMessageTimestamp': FieldValue.serverTimestamp(),
      'lastMessage': 'New hire request',
      'type': 'hire',
      'status': 'pending',
      'hireRequestId': hireRequestId,
      'project': conversation['project'],
      'budget': conversation['budget'],
      'duration': conversation['duration'],
      'uniqueId': uniqueConversationId, // Use a new unique ID for each conversation
      'isNewHireRequest': true, // Flag to indicate this is a new hire request conversation
    });

    // Update the hire request with the conversation ID
    await FirebaseFirestore.instance.collection('hire_requests').doc(hireRequestId).update({
      'conversationId': conversationRef.id,
    });

    // Update the conversation object with the real ID
    conversation['id'] = conversationRef.id;

    debugPrint('Created new conversation: ${conversationRef.id} for hire request: $hireRequestId');
  }

  // Build a tab with an unread count badge
  Widget _buildTabWithBadge(String title, bool isHireRequestsTab) {
    return StreamBuilder<List<Map<String, dynamic>>>(
      stream: _messagingService.getConversations(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return Tab(text: title);
        }

        // Filter conversations based on tab
        final List<Map<String, dynamic>> conversations = snapshot.data!.where((conversation) {
          if (isHireRequestsTab) {
            // In Hire Requests tab, only show placeholder hire requests or conversations with hire request ID
            return conversation['id'].toString().startsWith('hire_request_') ||
                   (conversation['type'] == 'hire' && conversation['status'] == 'pending');
          } else {
            // In All Messages tab, show all regular conversations and accepted/declined hire requests
            return !conversation['id'].toString().startsWith('hire_request_') &&
                   !(conversation['type'] == 'hire' && conversation['status'] == 'pending');
          }
        }).toList();

        // Count total unread messages
        int unreadCount = 0;
        for (final conversation in conversations) {
          // First check the unread field which is set in the UI
          if (conversation.containsKey('unread') && conversation['unread'] is num) {
            final int count = (conversation['unread'] as num).toInt();
            if (count > 0) {
              unreadCount += count; // Count actual messages, not just conversations
              debugPrint('MessagingPage - Tab badge: Conversation ${conversation['id']} has $count unread messages');
            }
          }
          // Fallback to checking unreadCount map
          else if (conversation.containsKey('unreadCount')) {
            final unreadCountMap = conversation['unreadCount'] as Map<String, dynamic>?;
            if (unreadCountMap != null && _messagingService.currentUserId != null) {
              final String currentUserIdStr = _messagingService.currentUserId.toString();
              if (unreadCountMap.containsKey(currentUserIdStr)) {
                final dynamic countValue = unreadCountMap[currentUserIdStr];
                if (countValue is int && countValue > 0) {
                  unreadCount += countValue;
                  debugPrint('MessagingPage - Tab badge: Conversation ${conversation['id']} has $countValue unread messages from map');
                } else if (countValue is num && countValue > 0) {
                  unreadCount += countValue.toInt();
                  debugPrint('MessagingPage - Tab badge: Conversation ${conversation['id']} has ${countValue.toInt()} unread messages from map (num)');
                }
              }
            }
          }
        }

        // If no unread messages, just show the tab title
        if (unreadCount == 0) {
          return Tab(text: title);
        }

        // Otherwise, show the tab with a badge
        return Tab(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(title),
              const SizedBox(width: 6),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(10),
                ),
                constraints: const BoxConstraints(
                  minWidth: 20,
                  minHeight: 20,
                ),
                child: Text(
                  unreadCount > 99 ? '99+' : unreadCount.toString(),
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Navigate to chat screen and handle the result
  void _navigateToChatScreen(Map<String, dynamic> conversation) {
    if (mounted) {
      // Reset unread count in the local conversation object
      conversation['unread'] = 0;

      // Reset unread count in Firebase before navigating
      _resetUnreadCount(conversation['id']);

      // Force a UI update to show zero unread count immediately
      setState(() {});

      // Force update the tab bar badge count
      _messagingService.updateUserUnreadMessageCount();

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChatScreen(conversation: conversation),
        ),
      ).then((result) {
      // Only refresh conversations if we have a specific result that requires it
      // This prevents unnecessary refreshes when simply navigating back
      bool shouldRefresh = false;

      // Handle result from chat screen (e.g., accept/reject hire request)
      if (result != null && result is Map<String, dynamic>) {
        if (result.containsKey('action') && result.containsKey('id')) {
          shouldRefresh = true;
          final String action = result['action'];
          final String id = result['id'];

          if (action == 'accept' || action == 'reject') {
            // Update the local conversation status
            setState(() {
              final index = _conversations.indexWhere((c) => c['id'] == id);
              if (index != -1) {
                _conversations[index]['status'] = action == 'accept' ? 'accepted' : 'rejected';
                _conversations[index]['lastMessage'] = action == 'accept'
                    ? 'Hire request was accepted'
                    : 'Hire request was declined';
                _conversations[index]['time'] = 'Just now';

                debugPrint('MessagingPage - Updated conversation status from chat result: ${_conversations[index]['status']}');
              }
            });

            // Show confirmation if the widget is still mounted
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    action == 'accept'
                      ? 'You accepted the hire request from ${conversation['name']}'
                      : 'You declined the hire request from ${conversation['name']}',
                    style: GoogleFonts.poppins(),
                  ),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            }
          } else if (action == 'deleted' || action == 'cleared') {
            // These actions require a refresh
            shouldRefresh = true;
          }
        } else if (result.containsKey('refresh') && result['refresh'] == true) {
          // Explicit refresh request
          shouldRefresh = true;
        }
      }

      // Only refresh if needed
      if (shouldRefresh) {
        _loadConversationsDirectly();
      }
    });
    }
  }

  // Show conversation options dialog
  void _showConversationOptions(Map<String, dynamic> conversation) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Padding(
        padding: const EdgeInsets.symmetric(vertical: 16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.delete_outline, color: Colors.red),
              title: Text(
                'Delete Conversation',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w500,
                ),
              ),
              onTap: () {
                Navigator.pop(context); // Close the bottom sheet
                _confirmDeleteConversation(conversation);
              },
            ),
            ListTile(
              leading: const Icon(Icons.block_outlined, color: Colors.orange),
              title: Text(
                'Block User',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w500,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                // Show not implemented message
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Block user feature coming soon'),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.report_outlined, color: Colors.grey),
              title: Text(
                'Report Conversation',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w500,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                // Show not implemented message
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Report conversation feature coming soon'),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  // Confirm delete conversation
  void _confirmDeleteConversation(Map<String, dynamic> conversation) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Delete Conversation',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          'Are you sure you want to delete this conversation? This action cannot be undone.',
          style: GoogleFonts.poppins(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteConversation(conversation);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text(
              'Delete',
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Delete conversation
  Future<void> _deleteConversation(Map<String, dynamic> conversation) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await _messagingService.deleteConversation(conversation['id']);

      if (result['success']) {
        // Remove the conversation from the local list
        setState(() {
          _conversations.removeWhere((c) => c['id'] == conversation['id']);
          _isLoading = false;
        });

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Conversation deleted successfully'),
              behavior: SnackBarBehavior.floating,
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        // Show error message
        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error deleting conversation: ${result['error']}'),
              behavior: SnackBarBehavior.floating,
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // Show error message
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting conversation: $e'),
            behavior: SnackBarBehavior.floating,
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _respondToHireRequest(Map<String, dynamic> conversation, bool accept) async {
    // Show loading indicator
    setState(() {
      _isLoading = true;
    });

    try {
      // Check if this is a placeholder hire request
      final bool isPlaceholder = conversation['id'].startsWith('hire_request_');
      final String hireRequestId = conversation['hireRequestId'] ?? '';

      if (hireRequestId.isEmpty) {
        throw Exception('Hire request ID not found');
      }

      // Update the hire request status in the hire_requests collection
      await FirebaseFirestore.instance
          .collection('hire_requests')
          .doc(hireRequestId)
          .update({
            'status': accept ? 'accepted' : 'declined',
            'updatedAt': FieldValue.serverTimestamp(),
          });

      // If this is a placeholder, we need to create a conversation first
      String conversationId;
      if (isPlaceholder) {
        // Create a new conversation for this hire request
        final String currentUserId = _messagingService.currentUserId!;
        final String otherUserId = conversation['otherUserId'];

        // Create the conversation document
        final conversationRef = await FirebaseFirestore.instance.collection('conversations').add({
          'participants': [currentUserId, otherUserId],
          'createdAt': FieldValue.serverTimestamp(),
          'lastMessageTimestamp': FieldValue.serverTimestamp(),
          'lastMessage': accept ? 'Hire request was accepted' : 'Hire request was declined',
          'type': 'hire',
          'status': accept ? 'accepted' : 'declined',
          'hireRequestId': hireRequestId,
          'project': conversation['project'],
          'budget': conversation['budget'],
          'duration': conversation['duration'],
        });

        conversationId = conversationRef.id;

        // Update the hire request with the conversation ID
        await FirebaseFirestore.instance.collection('hire_requests').doc(hireRequestId).update({
          'conversationId': conversationId,
        });
      } else {
        // Use the existing conversation
        conversationId = conversation['id'];

        // Update conversation in Firebase
        await FirebaseFirestore.instance
            .collection('conversations')
            .doc(conversationId)
            .update({
              'status': accept ? 'accepted' : 'declined',
              'lastStatusUpdate': FieldValue.serverTimestamp(),
              'lastMessage': accept ? 'Hire request was accepted' : 'Hire request was declined',
              'lastMessageTimestamp': FieldValue.serverTimestamp(),
            });
      }

      // Add a system message to the conversation
      await FirebaseFirestore.instance
          .collection('conversations')
          .doc(conversationId)
          .collection('messages')
          .add({
            'senderId': 'system',
            'text': accept
                ? 'Hire request was accepted'
                : 'Hire request was declined',
            'timestamp': FieldValue.serverTimestamp(),
            'isRead': false,
            'type': 'system',
            'systemAction': accept ? 'hire_request_accepted' : 'hire_request_declined',
          });

      // Get the other user's ID for notification
      final String otherUserId = conversation['otherUserId'];

      // Send notification to the other user
      final NotificationService notificationService = NotificationService();
      await notificationService.sendNotification(
        userId: otherUserId,
        title: accept ? 'Hire Request Accepted' : 'Hire Request Declined',
        body: accept
            ? 'Your hire request has been accepted'
            : 'Your hire request has been declined',
        type: accept ? 'hire_request_accepted' : 'hire_request_declined',
        data: {
          'conversationId': conversationId,
          'hireRequestId': hireRequestId,
        },
      );

      // Update local state
      setState(() {
        // Find the conversation in the list
        final index = _conversations.indexWhere((c) =>
            (isPlaceholder && c['hireRequestId'] == hireRequestId) ||
            (!isPlaceholder && c['id'] == conversationId));

        if (index != -1) {
          // Update the status
          _conversations[index]['status'] = accept ? 'accepted' : 'declined';

          // Update the last message to reflect the status change
          _conversations[index]['lastMessage'] = accept
              ? 'Hire request was accepted'
              : 'Hire request was declined';

          // Update the timestamp to now
          _conversations[index]['time'] = 'Just now';

          // If this was a placeholder, update the ID
          if (isPlaceholder) {
            _conversations[index]['id'] = conversationId;
          }

          debugPrint('MessagingPage - Updated conversation status: ${_conversations[index]['status']}');
        } else {
          debugPrint('MessagingPage - Could not find conversation with ID: ${conversation['id']}');
        }
        _isLoading = false;
      });

      // Force a reload of conversations to ensure everything is up to date
      _loadConversationsDirectly();

      // Show confirmation
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              accept
                ? 'You accepted the hire request from ${conversation['name']}'
                : 'You declined the hire request from ${conversation['name']}',
              style: GoogleFonts.poppins(),
            ),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (error) {
      debugPrint('Error responding to hire request: $error');

      // Update loading state
      setState(() {
        _isLoading = false;
      });

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error: ${error.toString()}',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }
}