import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'indus_iap_service.dart';
import 'stripe_payment_service.dart';
import 'financial_service.dart';

/// Service to manage migration from Stripe to Indus AppStore In-App Purchases
/// This service will gradually phase out Stripe and replace it with IAP
class PaymentMigrationService {
  static final PaymentMigrationService _instance = PaymentMigrationService._internal();
  factory PaymentMigrationService() => _instance;
  PaymentMigrationService._internal();

  final IndusIAPService _iapService = IndusIAPService();
  final StripePaymentService _stripeService = StripePaymentService();
  final FinancialService _financialService = FinancialService();
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  bool _iapInitialized = false;
  bool _migrationEnabled = true; // Feature flag for migration

  String? get currentUserId => _auth.currentUser?.uid;

  /// Initialize the payment migration service
  Future<bool> initialize() async {
    try {
      debugPrint('PaymentMigrationService: Initializing...');

      // Initialize IAP service
      _iapInitialized = await _iapService.initialize();
      
      if (_iapInitialized) {
        debugPrint('PaymentMigrationService: IAP service initialized successfully');
      } else {
        debugPrint('PaymentMigrationService: IAP service initialization failed, falling back to Stripe');
        // Initialize Stripe as fallback
        await _stripeService.initialize();
      }

      return true;
    } catch (e) {
      debugPrint('PaymentMigrationService: Initialization error: $e');
      return false;
    }
  }

  /// Process wallet top-up using the best available method
  Future<Map<String, dynamic>> processWalletTopUp(double amount) async {
    try {
      // Use IAP if available and migration is enabled
      if (_iapInitialized && _migrationEnabled) {
        return await _processWalletTopUpViaIAP(amount);
      } else {
        // Fallback to Stripe
        return await _processWalletTopUpViaStripe(amount);
      }
    } catch (e) {
      debugPrint('PaymentMigrationService: Wallet top-up error: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Process wallet top-up via In-App Purchase
  Future<Map<String, dynamic>> _processWalletTopUpViaIAP(double amount) async {
    try {
      // Find the appropriate product for the amount
      final products = _iapService.products;
      final productId = _getWalletProductId(amount);
      
      final product = products.firstWhere(
        (p) => p.id == productId,
        orElse: () => throw Exception('Product not found for amount: \$${amount.toStringAsFixed(2)}'),
      );

      // Initiate purchase
      final success = await _iapService.purchaseProduct(product);
      
      if (success) {
        return {
          'success': true,
          'method': 'iap',
          'message': 'Purchase initiated successfully',
          'amount': amount,
        };
      } else {
        throw Exception('Failed to initiate purchase');
      }
    } catch (e) {
      debugPrint('PaymentMigrationService: IAP wallet top-up error: $e');
      // Fallback to Stripe if IAP fails
      return await _processWalletTopUpViaStripe(amount);
    }
  }

  /// Process wallet top-up via Stripe (legacy)
  Future<Map<String, dynamic>> _processWalletTopUpViaStripe(double amount) async {
    try {
      debugPrint('PaymentMigrationService: Using Stripe for wallet top-up');
      
      // Process payment via Stripe
      final paymentResult = await _stripeService.processPayment(amount: amount);
      
      if (paymentResult['success'] == true) {
        // Add balance to wallet
        final walletResult = await _stripeService.addBalanceToWallet(
          amount,
          paymentResult['paymentIntentId'],
        );
        
        return {
          'success': walletResult['success'],
          'method': 'stripe',
          'message': walletResult['message'],
          'amount': amount,
          'newBalance': walletResult['newBalance'],
        };
      } else {
        return paymentResult;
      }
    } catch (e) {
      debugPrint('PaymentMigrationService: Stripe wallet top-up error: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Process asset purchase
  Future<Map<String, dynamic>> processAssetPurchase({
    required String assetId,
    required double price,
    required String paymentMethod, // 'wallet', 'iap', 'stripe'
  }) async {
    try {
      switch (paymentMethod) {
        case 'wallet':
          return await _processAssetPurchaseViaWallet(assetId, price);
        case 'iap':
          return await _processAssetPurchaseViaIAP(assetId, price);
        case 'stripe':
          return await _processAssetPurchaseViaStripe(assetId, price);
        default:
          throw Exception('Invalid payment method: $paymentMethod');
      }
    } catch (e) {
      debugPrint('PaymentMigrationService: Asset purchase error: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Process asset purchase via wallet
  Future<Map<String, dynamic>> _processAssetPurchaseViaWallet(String assetId, double price) async {
    final userId = currentUserId;
    if (userId == null) {
      return {
        'success': false,
        'error': 'User not authenticated',
      };
    }

    try {
      // Check wallet balance
      final balance = await _financialService.getUserBalance();
      if (balance < price) {
        return {
          'success': false,
          'error': 'Insufficient wallet balance',
          'currentBalance': balance,
          'requiredAmount': price,
        };
      }

      // Process wallet payment
      await _firestore.runTransaction((transaction) async {
        final userRef = _firestore.collection('users').doc(userId);
        final userDoc = await transaction.get(userRef);

        if (!userDoc.exists) {
          throw Exception('User not found');
        }

        final userData = userDoc.data()!;
        final currentBalance = (userData['balance'] as num?)?.toDouble() ?? 0.0;
        final newBalance = currentBalance - price;

        // Update user balance
        transaction.update(userRef, {
          'balance': newBalance,
          'updatedAt': FieldValue.serverTimestamp(),
        });

        // Add asset to user's purchases
        final purchaseRef = _firestore.collection('user_purchases').doc();
        transaction.set(purchaseRef, {
          'userId': userId,
          'assetId': assetId,
          'price': price,
          'paymentMethod': 'wallet',
          'purchasedAt': FieldValue.serverTimestamp(),
        });

        // Create transaction record
        final transactionRef = _firestore.collection('transactions').doc();
        transaction.set(transactionRef, {
          'userId': userId,
          'type': 'asset_purchase',
          'amount': price,
          'reason': 'Asset purchase via wallet',
          'metadata': {
            'assetId': assetId,
            'paymentMethod': 'wallet',
          },
          'previousBalance': currentBalance,
          'newBalance': newBalance,
          'createdAt': FieldValue.serverTimestamp(),
        });
      });

      return {
        'success': true,
        'method': 'wallet',
        'message': 'Asset purchased successfully',
        'assetId': assetId,
        'price': price,
      };
    } catch (e) {
      debugPrint('PaymentMigrationService: Wallet asset purchase error: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Process asset purchase via In-App Purchase
  Future<Map<String, dynamic>> _processAssetPurchaseViaIAP(String assetId, double price) async {
    try {
      // For asset purchases via IAP, we need to create dynamic products
      // This would typically be handled by your backend
      debugPrint('PaymentMigrationService: Asset purchase via IAP not yet implemented');
      
      // For now, fallback to wallet or Stripe
      return {
        'success': false,
        'error': 'Asset purchase via IAP not yet implemented. Please use wallet or add funds first.',
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Process asset purchase via Stripe (legacy)
  Future<Map<String, dynamic>> _processAssetPurchaseViaStripe(String assetId, double price) async {
    try {
      debugPrint('PaymentMigrationService: Using Stripe for asset purchase');
      
      // Process payment via Stripe
      final paymentResult = await _stripeService.processPayment(amount: price);
      
      if (paymentResult['success'] == true) {
        // Record asset purchase
        final userId = currentUserId;
        if (userId != null) {
          await _firestore.collection('user_purchases').add({
            'userId': userId,
            'assetId': assetId,
            'price': price,
            'paymentMethod': 'stripe',
            'paymentIntentId': paymentResult['paymentIntentId'],
            'purchasedAt': FieldValue.serverTimestamp(),
          });
        }
        
        return {
          'success': true,
          'method': 'stripe',
          'message': 'Asset purchased successfully',
          'assetId': assetId,
          'price': price,
        };
      } else {
        return paymentResult;
      }
    } catch (e) {
      debugPrint('PaymentMigrationService: Stripe asset purchase error: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Get the appropriate wallet product ID for the amount (INR)
  String _getWalletProductId(double amount) {
    // For predefined amounts
    if (amount <= 50.0) return IndusIAPService.walletTopUpProducts['wallet_50']!;
    if (amount <= 100.0) return IndusIAPService.walletTopUpProducts['wallet_100']!;
    if (amount <= 200.0) return IndusIAPService.walletTopUpProducts['wallet_200']!;
    if (amount <= 500.0) return IndusIAPService.walletTopUpProducts['wallet_500']!;
    if (amount <= 1000.0) return IndusIAPService.walletTopUpProducts['wallet_1000']!;
    if (amount <= 2000.0) return IndusIAPService.walletTopUpProducts['wallet_2000']!;
    if (amount <= 5000.0) return IndusIAPService.walletTopUpProducts['wallet_5000']!;

    // For custom amounts, generate dynamic product ID
    return 'com.asatu.wallet.custom.${amount.toInt()}inr';
  }

  /// Get available wallet top-up options
  List<Map<String, dynamic>> getWalletTopUpOptions() {
    if (_iapInitialized && _migrationEnabled) {
      return _iapService.products
          .where((product) => IndusIAPService.walletTopUpProducts.containsValue(product.id))
          .map((product) => {
                'id': product.id,
                'title': product.title,
                'description': product.description,
                'price': product.price,
                'amount': _getAmountFromProductId(product.id),
                'method': 'iap',
              })
          .toList();
    } else {
      // Return predefined Stripe options (INR)
      return [
        {'id': 'stripe_50', 'title': '₹50 Wallet Top-up', 'price': '₹50.00', 'amount': 50.0, 'method': 'stripe'},
        {'id': 'stripe_100', 'title': '₹100 Wallet Top-up', 'price': '₹100.00', 'amount': 100.0, 'method': 'stripe'},
        {'id': 'stripe_200', 'title': '₹200 Wallet Top-up', 'price': '₹200.00', 'amount': 200.0, 'method': 'stripe'},
        {'id': 'stripe_500', 'title': '₹500 Wallet Top-up', 'price': '₹500.00', 'amount': 500.0, 'method': 'stripe'},
        {'id': 'stripe_1000', 'title': '₹1000 Wallet Top-up', 'price': '₹1000.00', 'amount': 1000.0, 'method': 'stripe'},
        {'id': 'stripe_2000', 'title': '₹2000 Wallet Top-up', 'price': '₹2000.00', 'amount': 2000.0, 'method': 'stripe'},
        {'id': 'stripe_5000', 'title': '₹5000 Wallet Top-up', 'price': '₹5000.00', 'amount': 5000.0, 'method': 'stripe'},
      ];
    }
  }

  /// Get amount from product ID (INR)
  double _getAmountFromProductId(String productId) {
    switch (productId) {
      case 'com.asatu.wallet.50inr':
        return 50.0;
      case 'com.asatu.wallet.100inr':
        return 100.0;
      case 'com.asatu.wallet.200inr':
        return 200.0;
      case 'com.asatu.wallet.500inr':
        return 500.0;
      case 'com.asatu.wallet.1000inr':
        return 1000.0;
      case 'com.asatu.wallet.2000inr':
        return 2000.0;
      case 'com.asatu.wallet.5000inr':
        return 5000.0;
      default:
        // Handle custom amounts
        if (productId.startsWith('com.asatu.wallet.custom.') && productId.endsWith('inr')) {
          final amountStr = productId
              .replaceFirst('com.asatu.wallet.custom.', '')
              .replaceFirst('inr', '');
          return double.tryParse(amountStr) ?? 0.0;
        }
        return 0.0;
    }
  }

  /// Check if IAP is available
  bool get isIAPAvailable => _iapInitialized;

  /// Check if migration is enabled
  bool get isMigrationEnabled => _migrationEnabled;

  /// Enable/disable migration
  void setMigrationEnabled(bool enabled) {
    _migrationEnabled = enabled;
  }

  /// Dispose resources
  void dispose() {
    _iapService.dispose();
  }
}
