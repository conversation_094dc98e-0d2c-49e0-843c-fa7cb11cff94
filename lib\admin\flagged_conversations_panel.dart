import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../services/flagged_conversation_service.dart';

class FlaggedConversationsPanel extends StatefulWidget {
  const FlaggedConversationsPanel({super.key});

  @override
  FlaggedConversationsPanelState createState() => FlaggedConversationsPanelState();
}

class FlaggedConversationsPanelState extends State<FlaggedConversationsPanel> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final FlaggedConversationService _flaggedService = FlaggedConversationService();

  bool _isLoading = true;
  String? _errorMessage;

  // Flagged conversations data
  List<Map<String, dynamic>> _flaggedConversations = [];

  // Selected conversation for detail view
  Map<String, dynamic>? _selectedConversation;
  List<Map<String, dynamic>> _conversationContext = [];
  bool _isLoadingContext = false;

  // Statistics
  Map<String, dynamic> _stats = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadFlaggedConversations();
    _loadStats();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  // Load flagged conversations
  void _loadFlaggedConversations() {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    _flaggedService.getFlaggedConversations().listen((conversations) {
      if (mounted) {
        setState(() {
          _flaggedConversations = conversations;
          _isLoading = false;
        });
      }
    }, onError: (error) {
      debugPrint('Error loading flagged conversations: $error');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to load flagged conversations. Please try again.';
        });
      }
    });
  }

  // Load statistics
  Future<void> _loadStats() async {
    try {
      final stats = await _flaggedService.getFlaggedConversationStats();
      if (mounted) {
        setState(() {
          _stats = stats;
        });
      }
    } catch (e) {
      debugPrint('Error loading stats: $e');
    }
  }

  // Load conversation context
  Future<void> _loadConversationContext(Map<String, dynamic> conversation) async {
    if (conversation['conversationId'] == null || conversation['timestamp'] == null) return;

    setState(() {
      _isLoadingContext = true;
      _selectedConversation = conversation;
    });

    try {
      final context = await _flaggedService.getConversationContext(
        conversation['conversationId'],
        conversation['timestamp'],
      );

      if (mounted) {
        setState(() {
          _conversationContext = context;
          _isLoadingContext = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading conversation context: $e');
      if (mounted) {
        setState(() {
          _isLoadingContext = false;
          _conversationContext = [];
        });
      }
    }
  }

  // Dismiss flagged conversation (false positive)
  Future<void> _dismissFlaggedConversation(String flaggedId, String moderationLogId) async {
    try {
      final success = await _flaggedService.dismissFlaggedConversation(
        flaggedId,
        moderationLogId,
      );

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Conversation dismissed as false positive',
                style: GoogleFonts.poppins(),
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }

        // Refresh data
        _loadFlaggedConversations();
        _loadStats();
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to dismiss conversation',
                style: GoogleFonts.poppins(),
              ),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Error dismissing flagged conversation: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error: $e',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  // Send warning to users
  Future<void> _sendWarningToUsers(
    String flaggedId,
    String moderationLogId,
    String senderId,
    String receiverId,
  ) async {
    // Show warning dialog
    if (!mounted) return;

    final TextEditingController warningController = TextEditingController(
      text: 'Your conversation has been flagged for potentially violating our platform policies. '
           'Please review our terms of service regarding direct contact information and off-platform transactions. '
           'Continued violations may result in account restrictions.',
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Send Warning',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Enter warning message to send to users:',
              style: GoogleFonts.poppins(),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: warningController,
              maxLines: 5,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                hintText: 'Enter warning message...',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(
                color: Colors.grey[700],
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              final warningMessage = warningController.text.trim();
              if (warningMessage.isEmpty) return;

              Navigator.pop(context);

              try {
                final success = await _flaggedService.sendWarningToUsers(
                  flaggedId,
                  moderationLogId,
                  senderId,
                  receiverId,
                  warningMessage,
                );

                if (mounted) {
                  if (success) {
                    final scaffoldMessenger = ScaffoldMessenger.of(context);
                    scaffoldMessenger.showSnackBar(
                      SnackBar(
                        content: Text(
                          'Warning sent to users',
                          style: GoogleFonts.poppins(),
                        ),
                        backgroundColor: Colors.green,
                        behavior: SnackBarBehavior.floating,
                      ),
                    );

                    // Refresh data
                    _loadFlaggedConversations();
                    _loadStats();
                  } else {
                    final scaffoldMessenger = ScaffoldMessenger.of(context);
                    scaffoldMessenger.showSnackBar(
                      SnackBar(
                        content: Text(
                          'Failed to send warning',
                          style: GoogleFonts.poppins(),
                        ),
                        backgroundColor: Colors.red,
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  }
                }
              } catch (e) {
                debugPrint('Error sending warning: $e');
                if (mounted) {
                  final scaffoldMessenger = ScaffoldMessenger.of(context);
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text(
                        'Error: $e',
                        style: GoogleFonts.poppins(),
                      ),
                      backgroundColor: Colors.red,
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: Text(
              'Send Warning',
              style: GoogleFonts.poppins(),
            ),
          ),
        ],
      ),
    );
  }

  // Suspend users
  Future<void> _suspendUsers(
    String flaggedId,
    String moderationLogId,
    String senderId,
    String receiverId,
  ) async {
    // Show suspension dialog
    if (!mounted) return;

    final TextEditingController reasonController = TextEditingController(
      text: 'Attempting to bypass platform fees by sharing contact information and discussing off-platform transactions.',
    );

    int suspensionDays = 7; // Default 7 days

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(
            'Suspend Users',
            style: GoogleFonts.poppins(
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Suspension duration:',
                style: GoogleFonts.poppins(),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: Slider(
                      value: suspensionDays.toDouble(),
                      min: 1,
                      max: 30,
                      divisions: 29,
                      label: '$suspensionDays days',
                      onChanged: (value) {
                        setState(() {
                          suspensionDays = value.round();
                        });
                      },
                    ),
                  ),
                  Text(
                    '$suspensionDays days',
                    style: GoogleFonts.poppins(),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                'Suspension reason:',
                style: GoogleFonts.poppins(),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: reasonController,
                maxLines: 3,
                decoration: InputDecoration(
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  hintText: 'Enter suspension reason...',
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'Cancel',
                style: GoogleFonts.poppins(
                  color: Colors.grey[700],
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                final suspensionReason = reasonController.text.trim();
                if (suspensionReason.isEmpty) return;

                Navigator.pop(context);

                try {
                  final success = await _flaggedService.suspendUsers(
                    flaggedId,
                    moderationLogId,
                    senderId,
                    receiverId,
                    suspensionDays,
                    suspensionReason,
                  );

                  if (mounted) {
                    if (success) {
                      final scaffoldMessenger = ScaffoldMessenger.of(context);
                      scaffoldMessenger.showSnackBar(
                        SnackBar(
                          content: Text(
                            'Users suspended for $suspensionDays days',
                            style: GoogleFonts.poppins(),
                          ),
                          backgroundColor: Colors.green,
                          behavior: SnackBarBehavior.floating,
                        ),
                      );

                      // Refresh data
                      _loadFlaggedConversations();
                      _loadStats();
                    } else {
                      final scaffoldMessenger = ScaffoldMessenger.of(context);
                      scaffoldMessenger.showSnackBar(
                        SnackBar(
                          content: Text(
                            'Failed to suspend users',
                            style: GoogleFonts.poppins(),
                          ),
                          backgroundColor: Colors.red,
                          behavior: SnackBarBehavior.floating,
                        ),
                      );
                    }
                  }
                } catch (e) {
                  debugPrint('Error suspending users: $e');
                  if (mounted) {
                    final scaffoldMessenger = ScaffoldMessenger.of(context);
                    scaffoldMessenger.showSnackBar(
                      SnackBar(
                        content: Text(
                          'Error: $e',
                          style: GoogleFonts.poppins(),
                        ),
                        backgroundColor: Colors.red,
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: Text(
                'Suspend Users',
                style: GoogleFonts.poppins(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: GoogleFonts.poppins(
                fontSize: 16,
                color: Colors.red,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadFlaggedConversations,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'Retry',
                style: GoogleFonts.poppins(),
              ),
            ),
          ],
        ),
      );
    }

    return Row(
      children: [
        // Left panel - List of flagged conversations
        Expanded(
          flex: 3,
          child: Column(
            children: [
              // Stats panel
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 13), // 0.05 * 255 ≈ 13
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildStatCard('Total Flagged', _stats['totalFlagged'] ?? 0, Colors.blue),
                    _buildStatCard('Pending', _stats['pending'] ?? 0, Colors.orange),
                    _buildStatCard('Warned', _stats['warned'] ?? 0, Colors.amber),
                    _buildStatCard('Suspended', _stats['suspended'] ?? 0, Colors.red),
                  ],
                ),
              ),

              // Flagged conversations list
              Expanded(
                child: _flaggedConversations.isEmpty
                    ? Center(
                        child: Text(
                          'No flagged conversations found',
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            color: Colors.grey[600],
                          ),
                        ),
                      )
                    : ListView.builder(
                        itemCount: _flaggedConversations.length,
                        padding: const EdgeInsets.all(8),
                        itemBuilder: (context, index) {
                          final conversation = _flaggedConversations[index];
                          final isSelected = _selectedConversation == conversation;

                          return Card(
                            elevation: isSelected ? 4 : 1,
                            margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                              side: BorderSide(
                                color: isSelected ? Colors.blue : Colors.transparent,
                                width: 2,
                              ),
                            ),
                            child: ListTile(
                              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                              selected: isSelected,
                              onTap: () => _loadConversationContext(conversation),
                              title: Text(
                                'Flagged: ${conversation['reason'] ?? 'Unknown reason'}',
                                style: GoogleFonts.poppins(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const SizedBox(height: 4),
                                  Text(
                                    'Between: ${conversation['senderName']} and ${conversation['receiverName']}',
                                    style: GoogleFonts.poppins(fontSize: 13),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    'Status: ${conversation['status'] ?? 'pending'}',
                                    style: GoogleFonts.poppins(
                                      fontSize: 13,
                                      color: _getStatusColor(conversation['status']),
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    'Flagged on: ${_formatTimestamp(conversation['timestamp'])}',
                                    style: GoogleFonts.poppins(fontSize: 12, color: Colors.grey[600]),
                                  ),
                                ],
                              ),
                              trailing: Icon(
                                Icons.chevron_right,
                                color: isSelected ? Colors.blue : Colors.grey,
                              ),
                            ),
                          );
                        },
                      ),
              ),
            ],
          ),
        ),

        // Right panel - Conversation details
        Expanded(
          flex: 4,
          child: _selectedConversation == null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.chat_bubble_outline,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Select a flagged conversation to view details',
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                )
              : _isLoadingContext
                  ? const Center(child: CircularProgressIndicator())
                  : _buildConversationDetails(),
        ),
      ],
    );
  }

  // Helper method to build stat card
  Widget _buildStatCard(String title, int value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 26), // 0.1 * 255 ≈ 26
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value.toString(),
            style: GoogleFonts.poppins(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to get status color
  Color _getStatusColor(String? status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'dismissed':
        return Colors.green;
      case 'warned':
        return Colors.amber;
      case 'suspended':
        return Colors.red;
      case 'banned':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  // Helper method to format timestamp
  String _formatTimestamp(dynamic timestamp) {
    if (timestamp == null) return 'Unknown';

    try {
      final DateTime dateTime = timestamp is Timestamp
          ? timestamp.toDate()
          : DateTime.fromMillisecondsSinceEpoch(timestamp);

      return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return 'Invalid date';
    }
  }

  // Build conversation details panel
  Widget _buildConversationDetails() {
    final conversation = _selectedConversation!;

    return Column(
      children: [
        // Header with actions
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 13), // 0.05 * 255 ≈ 13
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      'Flagged Conversation Details',
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  if (conversation['status'] == 'pending')
                    Row(
                      children: [
                        ElevatedButton.icon(
                          onPressed: () => _dismissFlaggedConversation(
                            conversation['id'],
                            conversation['moderationLogId'],
                          ),
                          icon: const Icon(Icons.check_circle_outline),
                          label: Text(
                            'Dismiss',
                            style: GoogleFonts.poppins(),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton.icon(
                          onPressed: () => _sendWarningToUsers(
                            conversation['id'],
                            conversation['moderationLogId'],
                            conversation['senderId'],
                            conversation['receiverId'],
                          ),
                          icon: const Icon(Icons.warning_amber_outlined),
                          label: Text(
                            'Warn',
                            style: GoogleFonts.poppins(),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                          ),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton.icon(
                          onPressed: () => _suspendUsers(
                            conversation['id'],
                            conversation['moderationLogId'],
                            conversation['senderId'],
                            conversation['receiverId'],
                          ),
                          icon: const Icon(Icons.block),
                          label: Text(
                            'Suspend',
                            style: GoogleFonts.poppins(),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildInfoCard(
                      'Sender',
                      conversation['senderName'] ?? 'Unknown',
                      conversation['senderId'] ?? '',
                      Icons.person,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildInfoCard(
                      'Receiver',
                      conversation['receiverName'] ?? 'Unknown',
                      conversation['receiverId'] ?? '',
                      Icons.person_outline,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              _buildInfoCard(
                'Flagged Reason',
                conversation['reason'] ?? 'Unknown reason',
                conversation['confidence'] != null
                    ? 'Confidence: ${(conversation['confidence'] * 100).toStringAsFixed(1)}%'
                    : '',
                Icons.flag,
              ),
            ],
          ),
        ),

        // Conversation messages
        Expanded(
          child: _conversationContext.isEmpty
              ? Center(
                  child: Text(
                    'No conversation context available',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      color: Colors.grey[600],
                    ),
                  ),
                )
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _conversationContext.length,
                  itemBuilder: (context, index) {
                    final message = _conversationContext[index];
                    final isSender = message['senderId'] == conversation['senderId'];
                    final isHighlighted = message['flagged'] == true;

                    return Container(
                      margin: const EdgeInsets.symmetric(vertical: 8),
                      child: Row(
                        mainAxisAlignment: isSender
                            ? MainAxisAlignment.start
                            : MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (isSender)
                            CircleAvatar(
                              radius: 16,
                              backgroundColor: Colors.blue,
                              child: Text(
                                conversation['senderName']?[0] ?? 'S',
                                style: GoogleFonts.poppins(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          const SizedBox(width: 8),
                          Flexible(
                            child: Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: isHighlighted
                                    ? Colors.red.shade50
                                    : (isSender ? Colors.blue.shade50 : Colors.grey.shade100),
                                borderRadius: BorderRadius.circular(12),
                                border: isHighlighted
                                    ? Border.all(color: Colors.red, width: 1)
                                    : null,
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    message['text'] ?? '',
                                    style: GoogleFonts.poppins(
                                      fontSize: 14,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    _formatTimestamp(message['timestamp']),
                                    style: GoogleFonts.poppins(
                                      fontSize: 10,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                  if (isHighlighted) ...[
                                    const SizedBox(height: 8),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 4,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.red.shade100,
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          const Icon(
                                            Icons.warning,
                                            size: 14,
                                            color: Colors.red,
                                          ),
                                          const SizedBox(width: 4),
                                          Flexible(
                                            child: Text(
                                              message['flagReason'] ?? 'Flagged content',
                                              style: GoogleFonts.poppins(
                                                fontSize: 12,
                                                color: Colors.red,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          if (!isSender)
                            CircleAvatar(
                              radius: 16,
                              backgroundColor: Colors.purple,
                              child: Text(
                                conversation['receiverName']?[0] ?? 'R',
                                style: GoogleFonts.poppins(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                        ],
                      ),
                    );
                  },
                ),
        ),
      ],
    );
  }

  // Helper method to build info card
  Widget _buildInfoCard(String title, String value, String subtitle, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: Colors.blue,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  value,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (subtitle.isNotEmpty)
                  Text(
                    subtitle,
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}