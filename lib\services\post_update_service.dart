import 'dart:async';
import 'package:flutter/foundation.dart';

/// Service to manage post updates across different pages
class PostUpdateService {
  static final PostUpdateService _instance = PostUpdateService._internal();
  factory PostUpdateService() => _instance;
  PostUpdateService._internal();

  // Stream controllers for different types of updates
  final StreamController<Map<String, dynamic>> _postUpdateController =
      StreamController<Map<String, dynamic>>.broadcast();

  final StreamController<Map<String, dynamic>> _commentUpdateController =
      StreamController<Map<String, dynamic>>.broadcast();

  // Streams for listening to updates
  Stream<Map<String, dynamic>> get postUpdates => _postUpdateController.stream;
  Stream<Map<String, dynamic>> get commentUpdates => _commentUpdateController.stream;

  /// Update post data across all pages
  void updatePost(String postId, Map<String, dynamic> updates) {
    final updateData = {
      'postId': postId,
      'updates': updates,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };

    _postUpdateController.add(updateData);
    debugPrint('PostUpdateService: Broadcasting post update for $postId: $updates');
  }

  /// Update comment count for a post
  void updateCommentCount(String postId, int newCount) {
    updatePost(postId, {
      'comments': newCount,
      'commentsCount': newCount,
    });
  }

  /// Update like count for a post
  void updateLikeCount(String postId, int newCount, bool isLiked) {
    updatePost(postId, {
      'likes': newCount,
      'likesCount': newCount,
      'isLiked': isLiked,
    });
  }

  /// Add a new comment to the updates stream
  void addComment(String postId, Map<String, dynamic> comment) {
    final commentData = {
      'postId': postId,
      'comment': comment,
      'action': 'add',
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };

    _commentUpdateController.add(commentData);
    debugPrint('PostUpdateService: Broadcasting new comment for $postId');
  }

  /// Update a comment in the updates stream
  void updateComment(String postId, String commentId, Map<String, dynamic> updates) {
    final commentData = {
      'postId': postId,
      'commentId': commentId,
      'updates': updates,
      'action': 'update',
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };

    _commentUpdateController.add(commentData);
    debugPrint('PostUpdateService: Broadcasting comment update for $commentId');
  }

  /// Update comment like status across all pages
  void updateCommentLike(String postId, String commentId, bool isLiked, int likeCount) {
    updateComment(postId, commentId, {
      'isLiked': isLiked,
      'likes': likeCount,
      'likesCount': likeCount,
    });
  }

  /// Dispose resources
  void dispose() {
    _postUpdateController.close();
    _commentUpdateController.close();
  }
}
