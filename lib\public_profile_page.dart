import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:async';
import 'dart:math';
import 'services/user_content_service.dart';
import 'services/messaging_service.dart';
import 'services/notification_service.dart';
import 'chat_screen.dart';

class PublicProfilePage extends StatefulWidget {
  final String? userId;

  const PublicProfilePage({Key? key, this.userId}) : super(key: key);

  @override
  State<PublicProfilePage> createState() => _PublicProfilePageState();
}

class _PublicProfilePageState extends State<PublicProfilePage> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isFollowing = false;
  bool _isLoading = true;
  bool _isOwnProfile = false;
  final UserContentService _userContentService = UserContentService();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // User data
  Map<String, dynamic> _userData = {
    'name': 'User',
    'username': 'username',
    'bio': 'No bio yet',
    'location': 'No location set',
    'avatar': 'https://ui-avatars.com/api/?name=User&background=6A11CB&color=fff',
    'followers': 0,
    'following': 0,
    'assets': 0,
    'posts': 0,
    'downloads': 0,
    'skills': [],
  };

  // Content data
  List<Map<String, dynamic>> _assets = [];
  List<Map<String, dynamic>> _posts = [];
  List<Map<String, dynamic>> _hirePlans = [];
  final ScrollController _scrollController = ScrollController();
  bool _isScrolled = false;
  final bool _isOnline = true; // Online status for messaging

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Listen to scroll events to animate the app bar
    _scrollController.addListener(() {
      setState(() {
        _isScrolled = _scrollController.offset > 80;
      });
    });

    // Check if this is the current user's profile
    _checkIfOwnProfile();

    // Load user data
    _loadUserData();

    // Load assets, posts, and hire plans
    _loadAssets();
    _loadPosts();
    _loadHirePlans();

    // Check if current user is following this user
    _checkFollowStatus();

    // Delay the animations for a smoother entry experience
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        setState(() {});
      }
    });
  }

  // Check if the user is viewing their own profile
  void _checkIfOwnProfile() {
    final currentUserId = _userContentService.currentUserId;
    if (currentUserId != null && widget.userId != null) {
      setState(() {
        _isOwnProfile = currentUserId == widget.userId;
      });
      debugPrint('PublicProfilePage - Is own profile: $_isOwnProfile');
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // This will refresh the assets when navigating to this page
    _refreshData();
  }

  // Refresh all data
  Future<void> _refreshData() async {
    // Reload user data, assets, posts, and hire plans
    await _loadUserData();
    await _loadAssets();
    await _loadPosts();
    await _loadHirePlans();
  }

  // Load user data from Firebase
  Future<void> _loadUserData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Debug the userId parameter
      debugPrint('PublicProfilePage - Loading user data for userId: ${widget.userId}');

      if (widget.userId == null || widget.userId!.isEmpty) {
        debugPrint('PublicProfilePage - Error: userId is null or empty');
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
        return;
      }

      // Fetch user document from Firestore
      debugPrint('PublicProfilePage - Fetching user document for ID: ${widget.userId}');
      final userDoc = await _firestore.collection('users').doc(widget.userId).get();

      if (!userDoc.exists) {
        debugPrint('PublicProfilePage - Error: User document does not exist for ID: ${widget.userId}');
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
        return;
      }

      if (!mounted) return;

      final userData = userDoc.data() as Map<String, dynamic>;
      debugPrint('PublicProfilePage - User data loaded successfully: ${userData['displayName']}');

      // Get skills
      List<Map<String, dynamic>> skills = [];
      try {
        debugPrint('PublicProfilePage - Fetching user profile document for skills');
        final userProfileDoc = await _firestore.collection('user_profiles').doc(widget.userId).get();
        if (userProfileDoc.exists) {
          final profileData = userProfileDoc.data() as Map<String, dynamic>;
          if (profileData['skills'] != null) {
            skills = List<Map<String, dynamic>>.from(profileData['skills']);
            debugPrint('PublicProfilePage - Loaded ${skills.length} skills');
          } else {
            debugPrint('PublicProfilePage - No skills found in profile data');
          }
        } else {
          debugPrint('PublicProfilePage - User profile document does not exist');
        }
      } catch (e) {
        debugPrint('PublicProfilePage - Error loading skills: $e');
      }

      if (mounted) {
        setState(() {
          _userData = {
            'id': widget.userId,
            'name': userData['displayName'] ?? 'User',
            'username': userData['username'] ?? 'username',
            'bio': userData['bio'] ?? 'No bio yet',
            'location': userData['location'] ?? 'No location set',
            'avatar': userData['photoURL'] ?? 'https://ui-avatars.com/api/?name=${userData['displayName']?.replaceAll(' ', '+') ?? 'User'}&background=6A11CB&color=fff',
            'followers': userData['followersCount'] ?? 0,
            'following': userData['followingCount'] ?? 0,
            'assets': userData['assetsCount'] ?? 0,
            'posts': 0, // Will be updated when loading posts
            'downloads': userData['downloadsCount'] ?? 0,
            'skills': skills,
            'isVerified': userData['isVerified'] ?? false,
            'isElite': userData['isElite'] ?? false,
          };
          _isLoading = false;
          debugPrint('PublicProfilePage - User data state updated successfully');
        });
      }
    } catch (e) {
      debugPrint('PublicProfilePage - Error loading user data: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Load assets from Firebase
  Future<void> _loadAssets() async {
    try {
      if (widget.userId == null || widget.userId!.isEmpty) {
        debugPrint('PublicProfilePage - Cannot load assets: userId is null or empty');
        return;
      }

      debugPrint('PublicProfilePage - Loading assets for user: ${widget.userId}');
      final assets = await _userContentService.getUserAssets(userId: widget.userId);
      debugPrint('PublicProfilePage - Loaded ${assets.length} assets');

      if (mounted) {
        setState(() {
          _assets = assets;
          // Update asset count
          _userData['assets'] = assets.length;
          debugPrint('PublicProfilePage - Updated assets state');
        });
      }
    } catch (e) {
      debugPrint('PublicProfilePage - Error loading assets: $e');
    }
  }

  // Load posts from Firebase
  Future<void> _loadPosts() async {
    try {
      if (widget.userId == null || widget.userId!.isEmpty) {
        debugPrint('PublicProfilePage - Cannot load posts: userId is null or empty');
        return;
      }

      debugPrint('PublicProfilePage - Loading posts for user: ${widget.userId}');
      final posts = await _userContentService.getUserPosts(userId: widget.userId);
      debugPrint('PublicProfilePage - Loaded ${posts.length} posts');

      if (mounted) {
        setState(() {
          _posts = posts;
          // Update post count
          _userData['posts'] = posts.length;
          debugPrint('PublicProfilePage - Updated posts state');
        });
      }
    } catch (e) {
      debugPrint('PublicProfilePage - Error loading posts: $e');
    }
  }

  // Get active hire plans
  List<Map<String, dynamic>> _getActivePlans() {
    // Filter plans that are explicitly marked as active
    final activePlans = _hirePlans.where((plan) {
      // Check if the plan has an 'active' field and it's true
      if (plan.containsKey('active')) {
        return plan['active'] == true;
      }
      // If no 'active' field, consider it inactive
      return false;
    }).toList();

    debugPrint('PublicProfilePage - Found ${activePlans.length} active plans out of ${_hirePlans.length} total plans');
    return activePlans;
  }

  // Extract delivery time from hire plan
  String _getDeliveryTimeFromPlan(Map<String, dynamic> hirePlan) {
    debugPrint('PublicProfilePage - Getting delivery time from plan: ${hirePlan['name']}');

    // Check if deliveryTime is directly available (preferred field)
    if (hirePlan.containsKey('deliveryTime') && hirePlan['deliveryTime'] is String) {
      final String deliveryTime = hirePlan['deliveryTime'];
      if (deliveryTime.isNotEmpty && deliveryTime != 'project' && deliveryTime != 'Project') {
        debugPrint('PublicProfilePage - Found deliveryTime field: $deliveryTime');
        return deliveryTime;
      }
    }

    // Check for duration field
    if (hirePlan.containsKey('duration') && hirePlan['duration'] is String) {
      final String duration = hirePlan['duration'];
      if (duration.isNotEmpty && duration != 'project' && duration != 'Project') {
        debugPrint('PublicProfilePage - Found duration field: $duration');

        // Extract number of days from duration if it contains a number
        final RegExp daysRegex = RegExp(r'(\d+)\s*days?', caseSensitive: false);
        final match = daysRegex.firstMatch(duration);
        if (match != null) {
          final String days = '${match.group(1)} days';
          debugPrint('PublicProfilePage - Extracted days from duration: $days');
          return days;
        }

        return duration;
      }
    }

    // Check for delivery time in features
    if (hirePlan.containsKey('features') && hirePlan['features'] is List) {
      debugPrint('PublicProfilePage - Checking features for delivery time');
      for (final feature in hirePlan['features']) {
        if (feature is String &&
            (feature.toLowerCase().contains('delivery time') ||
             feature.toLowerCase().contains('delivery in') ||
             feature.toLowerCase().contains('delivered in'))) {
          debugPrint('PublicProfilePage - Found delivery time in feature: $feature');

          // Extract the number from the feature string
          final RegExp regex = RegExp(r'(\d+)\s*days?', caseSensitive: false);
          final match = regex.firstMatch(feature);
          if (match != null) {
            final String days = '${match.group(1)} days';
            debugPrint('PublicProfilePage - Extracted days from feature: $days');
            return days;
          }

          return feature;
        }
      }
    }

    // Default fallback
    debugPrint('PublicProfilePage - No delivery time found, using default: 7 days');
    return '7 days';
  }

  // Load hire plans from Firebase
  Future<void> _loadHirePlans() async {
    try {
      if (widget.userId == null || widget.userId!.isEmpty) {
        debugPrint('PublicProfilePage - Cannot load hire plans: userId is null or empty');
        return;
      }

      debugPrint('PublicProfilePage - Loading hire plans for user: ${widget.userId}');

      // First try direct Firestore query to check if hire plans exist
      debugPrint('PublicProfilePage - Performing direct Firestore query for hire plans');
      // Use simple query without orderBy to avoid Firebase index requirements
      final QuerySnapshot directSnapshot = await FirebaseFirestore.instance
          .collection('hire_plans')
          .where('userId', isEqualTo: widget.userId)
          .get();

      debugPrint('PublicProfilePage - Direct Firestore query found ${directSnapshot.docs.length} hire plans');

      // Log each document for debugging
      for (var doc in directSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        debugPrint('PublicProfilePage - Direct query - Plan: ${doc.id}, Name: ${data['name']}, UserId: ${data['userId']}');
      }

      // Now try the service method
      final hirePlans = await _userContentService.getUserHirePlans(userId: widget.userId);
      debugPrint('PublicProfilePage - Loaded ${hirePlans.length} hire plans from service');

      if (mounted) {
        // If service method returned empty but direct query found plans, use direct query results
        if (hirePlans.isEmpty && directSnapshot.docs.isNotEmpty) {
          debugPrint('PublicProfilePage - Using direct query results instead of service results');

          final directHirePlans = directSnapshot.docs.map((doc) {
            final data = doc.data() as Map<String, dynamic>;

            // Extract features from the plan data
            List<String> features = [];
            if (data['features'] != null && data['features'] is List) {
              features = (data['features'] as List)
                  .map((feature) {
                    if (feature is Map<String, dynamic>) {
                      final name = feature['name'] ?? '';
                      final value = feature['value'] ?? '';
                      final type = feature['type'] ?? 'boolean';

                      // Format the value based on type
                      String formattedValue = value.toString();

                      if (type == 'boolean') {
                        // For boolean values, just use the name with Yes/No
                        if (value.toString().toLowerCase() == 'true') {
                          formattedValue = 'true';
                        } else if (value.toString().toLowerCase() == 'false') {
                          formattedValue = 'false';
                        }
                      } else if (type == 'number' && value.toString().isNotEmpty) {
                        // For numbers, format with commas for thousands
                        try {
                          final num numValue = num.parse(value.toString());
                          formattedValue = numValue.toString();
                        } catch (e) {
                          formattedValue = value.toString();
                        }
                      }

                      return '$name: $formattedValue';
                    }
                    return feature.toString();
                  })
                  .toList()
                  .cast<String>();
            }

            // Map the document to the expected format
            return {
              'id': doc.id,
              'title': data['name'] ?? 'Untitled Plan',
              'price': (data['price'] as num?)?.toDouble() ?? 0.0,
              'unit': data['duration'] ?? '',
              'duration': data['duration'] ?? 'Not specified',
              'type': data['duration']?.toString().toLowerCase() ?? 'hourly',
              'features': features,
              'isPopular': data['isPopular'] ?? false,
              'active': data['active'] ?? false, // Make sure to include the active field
            };
          }).toList();

          setState(() {
            _hirePlans = directHirePlans;
            debugPrint('PublicProfilePage - Updated hire plans state with ${_hirePlans.length} plans from direct query');
          });
        } else {
          // Map the hire plans to the expected format
          final formattedHirePlans = hirePlans.map((plan) {
            // Extract features from the plan data
            List<String> features = [];
            if (plan['features'] != null && plan['features'] is List) {
              features = (plan['features'] as List)
                  .map((feature) {
                    if (feature is Map<String, dynamic>) {
                      final name = feature['name'] ?? '';
                      final value = feature['value'] ?? '';
                      final type = feature['type'] ?? 'boolean';

                      // Format the value based on type
                      String formattedValue = value.toString();

                      if (type == 'boolean') {
                        // For boolean values, just use the name with Yes/No
                        if (value.toString().toLowerCase() == 'true') {
                          formattedValue = 'true';
                        } else if (value.toString().toLowerCase() == 'false') {
                          formattedValue = 'false';
                        }
                      } else if (type == 'number' && value.toString().isNotEmpty) {
                        // For numbers, format with commas for thousands
                        try {
                          final num numValue = num.parse(value.toString());
                          formattedValue = numValue.toString();
                        } catch (e) {
                          formattedValue = value.toString();
                        }
                      }

                      return '$name: $formattedValue';
                    }
                    return feature.toString();
                  })
                  .toList()
                  .cast<String>();
            }

            return {
              'id': plan['id'],
              'title': plan['name'] ?? 'Untitled Plan',
              'price': plan['price'] ?? 0.0,
              'unit': plan['duration'] ?? '',
              'duration': plan['duration'] ?? 'Not specified',
              'type': plan['duration']?.toString().toLowerCase() ?? 'hourly',
              'features': features,
              'isPopular': plan['isPopular'] ?? false,
              'active': plan['active'] ?? false, // Make sure to include the active field
            };
          }).toList();

          setState(() {
            _hirePlans = formattedHirePlans;
            debugPrint('PublicProfilePage - Updated hire plans state with ${_hirePlans.length} plans from service');
          });
        }
      }
    } catch (e) {
      debugPrint('PublicProfilePage - Error loading hire plans: $e');
    }
  }

  // Check if current user is following this user
  Future<void> _checkFollowStatus() async {
    try {
      if (widget.userId == null) return;

      final isFollowing = await _userContentService.isFollowing(widget.userId!);

      if (mounted) {
        setState(() {
          _isFollowing = isFollowing;
        });
      }
    } catch (e) {
      debugPrint('Error checking follow status: $e');
    }
  }

  // Format count for display (e.g., 1200 -> 1.2K)
  String _formatCount(dynamic count) {
    if (count == null) return '0';

    final int numCount = count is int ? count : int.tryParse(count.toString()) ?? 0;

    if (numCount >= 1000000) {
      return '${(numCount / 1000000).toStringAsFixed(1)}M';
    } else if (numCount >= 1000) {
      return '${(numCount / 1000).toStringAsFixed(1)}K';
    } else {
      return numCount.toString();
    }
  }





  // Build skill bars from user data
  List<Widget> _buildSkillBars() {
    final List<Widget> skillWidgets = [];
    final List<Map<String, dynamic>> skills = List<Map<String, dynamic>>.from(_userData['skills'] ?? []);

    if (skills.isEmpty) {
      skillWidgets.add(
        Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              'No skills added yet',
              style: GoogleFonts.poppins(
                fontSize: 16,
                color: Colors.grey[500],
              ),
            ),
          ),
        ),
      );
      return skillWidgets;
    }

    // Sort skills by level (highest first)
    skills.sort((a, b) => (b['level'] as double).compareTo(a['level'] as double));

    // Add skill bars with increasing delay
    int delayMs = 700;
    for (int i = 0; i < skills.length; i++) {
      final skill = skills[i];

      skillWidgets.add(
        _buildAnimatedSkillBar(
          skill['name'] ?? 'Unnamed Skill',
          skill['level'] ?? 0.5,
          delayMs + (i * 100),
        ),
      );

      // Add spacing except after the last item
      if (i < skills.length - 1) {
        skillWidgets.add(const SizedBox(height: 16));
      }
    }

    return skillWidgets;
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: NestedScrollView(
        controller: _scrollController,
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 0,
              floating: true,
              pinned: true,
              elevation: _isScrolled ? 4 : 0,
              backgroundColor: Colors.white,
              title: AnimatedOpacity(
                opacity: _isScrolled ? 1.0 : 0.0,
                duration: const Duration(milliseconds: 250),
                child: Text(
                  _userData['name'] ?? 'User',
                  style: GoogleFonts.poppins(
                    color: Colors.black87,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              leading: IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.black87),
                onPressed: () => Navigator.of(context).pop(),
              ),
              actions: [
                IconButton(
                  icon: const Icon(Icons.share_outlined, color: Colors.black87),
                  onPressed: () {},
                ),
                IconButton(
                  icon: const Icon(Icons.flag_outlined, color: Colors.black87),
                  tooltip: 'Report User',
                  onPressed: () {
                    _showReportDialog();
                  },
                ),
              ],
            ),
          ];
        },
        body: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildProfileHeader(),
              const SizedBox(height: 24),
              _buildBioSection(),
              const SizedBox(height: 24),
              _buildSkillsSection(),
              const SizedBox(height: 24),
              _buildStatisticsSection(),
              const SizedBox(height: 32),
              _buildActionButtons(),
              const SizedBox(height: 24),
              _buildTabSection(),
            ],
          ),
        ),
      ),
    );
  }

  // Show report dialog
  void _showReportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Report User',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Why are you reporting this user?',
              style: GoogleFonts.poppins(),
            ),
            const SizedBox(height: 16),
            _buildReportOption('Inappropriate content'),
            _buildReportOption('Fake profile'),
            _buildReportOption('Spam'),
            _buildReportOption('Harassment'),
            _buildReportOption('Other'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF6A11CB),
            ),
            child: Text(
              'Submit',
              style: GoogleFonts.poppins(
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build report option
  Widget _buildReportOption(String text) {
    return InkWell(
      onTap: () {},
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            Icon(
              Icons.radio_button_unchecked,
              size: 20,
              color: Colors.grey[600],
            ),
            const SizedBox(width: 12),
            Text(
              text,
              style: GoogleFonts.poppins(),
            ),
          ],
        ),
      ),
    );
  }



  Widget _buildProfileHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Profile Avatar with Badge
              Stack(
                children: [
                  Hero(
                    tag: 'profile-image',
                    child: Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 4),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(25),
                            blurRadius: 10,
                            spreadRadius: 1,
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(50),
                        child: _userData['avatar'] != null && _userData['avatar'].toString().isNotEmpty
                            ? Image.network(
                                _userData['avatar'],
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  debugPrint('Error loading avatar: $error');
                                  return Container(
                                    color: Colors.grey[300],
                                    child: Icon(
                                      Icons.person,
                                      size: 50,
                                      color: Colors.grey[600],
                                    ),
                                  );
                                },
                              )
                            : Container(
                                color: Colors.grey[300],
                                child: Icon(
                                  Icons.person,
                                  size: 50,
                                  color: Colors.grey[600],
                                ),
                              ),
                      ),
                    ),
                  ).animate()
                    .fade(duration: 500.ms)
                    .scale(delay: 100.ms, duration: 500.ms),
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      width: 28,
                      height: 28,
                      decoration: BoxDecoration(
                        color: _isOnline ? const Color(0xFF4CAF50) : Colors.grey,
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 2),
                      ),
                      child: _isOnline
                        ? const Icon(Icons.check, color: Colors.white, size: 18)
                        : const Icon(Icons.access_time, color: Colors.white, size: 16),
                    ),
                  ),
                ],
              ),
              const SizedBox(width: 20),
              // Name, badges, and follow stats
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      crossAxisAlignment: WrapCrossAlignment.center,
                      children: [
                        Text(
                          _userData['name'] ?? 'User',
                          style: GoogleFonts.poppins(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ).animate()
                          .fade(duration: 500.ms)
                          .slideX(begin: 0.2, end: 0, duration: 500.ms, curve: Curves.easeOutQuad),
                        if (_userData['isElite'] == true)
                          _buildAnimatedBadge('Elite', const Color(0xFF9C27B0), 200),
                        if (_userData['isVerified'] == true)
                          _buildAnimatedBadge('Verified', const Color(0xFF2196F3), 300),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '@${_userData['username'] ?? 'username'}',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.black54,
                      ),
                    ).animate()
                      .fade(duration: 500.ms, delay: 100.ms)
                      .slideX(begin: 0.2, end: 0, duration: 500.ms, delay: 100.ms),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        _buildAnimatedFollowStat('Followers', _formatCount(_userData['followers'] ?? 0), 400),
                        const SizedBox(width: 16),
                        _buildAnimatedFollowStat('Following', _formatCount(_userData['following'] ?? 0), 500),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedBadge(String text, Color color, int delayMs) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withAlpha(30),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: GoogleFonts.poppins(
          fontSize: 12,
          fontWeight: FontWeight.w600,
          color: color,
        ),
      ),
    ).animate()
      .fade(delay: Duration(milliseconds: delayMs), duration: 500.ms)
      .scale(delay: Duration(milliseconds: delayMs), duration: 500.ms, begin: const Offset(0.8, 0.8));
  }

  Widget _buildAnimatedFollowStat(String label, String count, int delayMs) {
    return Column(
      children: [
        Text(
          count,
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: Colors.black54,
          ),
        ),
      ],
    ).animate()
      .fade(delay: Duration(milliseconds: delayMs), duration: 500.ms)
      .slideY(delay: Duration(milliseconds: delayMs), duration: 500.ms, begin: 0.2, end: 0);
  }

  Widget _buildBioSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'About',
            style: GoogleFonts.poppins(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ).animate()
            .fade(duration: 500.ms, delay: 300.ms)
            .slideY(begin: 0.2, end: 0, duration: 500.ms, delay: 300.ms),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Text(
              _userData['bio'] ?? 'No bio available',
              style: GoogleFonts.poppins(
                fontSize: 15,
                height: 1.5,
                color: Colors.black87,
              ),
            ),
          ).animate()
            .fade(duration: 500.ms, delay: 400.ms)
            .slideY(begin: 0.2, end: 0, duration: 500.ms, delay: 400.ms),

          // Only show location if available
          if (_userData['location'] != null && _userData['location'].toString().isNotEmpty)
            Container(
              margin: const EdgeInsets.only(top: 16),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 8),
                  Text(
                    _userData['location'],
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: Colors.grey[700],
                    ),
                  ),
                ],
              ),
            ).animate()
              .fade(duration: 500.ms, delay: 500.ms)
              .slideY(begin: 0.2, end: 0, duration: 500.ms, delay: 500.ms),
        ],
      ),
    );
  }



  Widget _buildSkillsSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Skills',
            style: GoogleFonts.poppins(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ).animate()
            .fade(duration: 500.ms, delay: 600.ms)
            .slideY(begin: 0.2, end: 0, duration: 500.ms, delay: 600.ms),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(13),
                  blurRadius: 10,
                  spreadRadius: 0,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: _buildSkillBars(),
            ),
          ).animate()
            .fade(duration: 500.ms, delay: 700.ms)
            .scale(duration: 500.ms, delay: 700.ms, begin: const Offset(0.95, 0.95)),
        ],
      ),
    );
  }

  Widget _buildAnimatedSkillBar(String skill, double level, int delayMs) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              skill,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '${(level * 100).toInt()}%',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: const Color(0xFF6A11CB),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Stack(
          children: [
            // Background
            Container(
              height: 8,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            // Foreground (animated)
            TweenAnimationBuilder<double>(
              duration: const Duration(milliseconds: 1500),
              curve: Curves.easeOutQuart,
              tween: Tween<double>(begin: 0.0, end: level),
              builder: (context, value, child) {
                return Container(
                  height: 8,
                  width: MediaQuery.of(context).size.width * value * 0.8,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFF6A11CB), Color(0xFF2575FC)],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                    borderRadius: BorderRadius.circular(4),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF6A11CB).withAlpha(50),
                        blurRadius: 4,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ],
    ).animate()
      .fade(delay: Duration(milliseconds: delayMs), duration: 500.ms)
      .slideX(delay: Duration(milliseconds: delayMs), duration: 500.ms, begin: -0.2, end: 0);
  }

  Widget _buildStatisticsSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Statistics',
            style: GoogleFonts.poppins(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ).animate()
            .fade(duration: 500.ms, delay: 800.ms)
            .slideY(begin: 0.2, end: 0, duration: 500.ms, delay: 800.ms),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(child: _buildAnimatedStatCard(
                'Total Assets',
                _formatCount(_userData['assets'] ?? 0),
                Icons.code,
                900
              )),
              const SizedBox(width: 16),
              Expanded(child: _buildAnimatedStatCard(
                'Downloads',
                _formatCount(_userData['downloads'] ?? 0),
                Icons.download,
                1000
              )),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(child: _buildAnimatedStatCard(
                'Posts',
                _formatCount(_userData['posts'] ?? 0),
                Icons.article_outlined,
                1100
              )),
              const SizedBox(width: 16),
              Expanded(child: _buildAnimatedStatCard(
                'Followers',
                _formatCount(_userData['followers'] ?? 0),
                Icons.people_outline,
                1200
              )),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedStatCard(String title, String value, IconData icon, int delayMs) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: const Color(0xFF3D5AFE).withAlpha(15),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(icon, color: const Color(0xFF3D5AFE), size: 22),
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: GoogleFonts.poppins(
              fontSize: 22,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.black54,
            ),
          ),
        ],
      ),
    ).animate()
      .fade(delay: Duration(milliseconds: delayMs), duration: 500.ms)
      .scale(delay: Duration(milliseconds: delayMs), duration: 500.ms, begin: const Offset(0.9, 0.9));
  }





  // Check if this is the current user's profile
  bool get _isCurrentUserProfile {
    return _isOwnProfile;
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          // Main action buttons row
          Row(
            children: [
              // Follow button (hidden if viewing own profile)
              if (!_isCurrentUserProfile)
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () async {
                      if (widget.userId == null) return;

                      // Show loading indicator in button
                      setState(() {
                        _isLoading = true;
                      });

                      // Update UI immediately for better UX
                      final bool wasFollowing = _isFollowing;
                      setState(() {
                        _isFollowing = !_isFollowing;
                      });

                      try {
                        // Perform the follow/unfollow action
                        final bool success = _isFollowing
                            ? await _userContentService.followUser(widget.userId!)
                            : await _userContentService.unfollowUser(widget.userId!);

                        if (success && mounted) {
                          // Show success message
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                _isFollowing ? 'Now following ${_userData['name']}' : 'Unfollowed ${_userData['name']}',
                              ),
                              behavior: SnackBarBehavior.floating,
                              backgroundColor: _isFollowing ? Colors.green : Colors.blue,
                              duration: const Duration(seconds: 2),
                            ),
                          );

                          // Update follower count in UI
                          setState(() {
                            if (_isFollowing) {
                              _userData['followers'] = (_userData['followers'] as int) + 1;
                            } else {
                              _userData['followers'] = (_userData['followers'] as int) - 1;
                            }
                          });
                        } else if (!success && mounted) {
                          // Revert UI if operation failed
                          setState(() {
                            _isFollowing = wasFollowing;
                          });

                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'Failed to ${_isFollowing ? 'follow' : 'unfollow'} user',
                              ),
                              behavior: SnackBarBehavior.floating,
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      } catch (e) {
                        debugPrint('Error toggling follow status: $e');
                        if (mounted) {
                          // Revert UI if operation failed
                          setState(() {
                            _isFollowing = wasFollowing;
                          });

                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Error: $e'),
                              behavior: SnackBarBehavior.floating,
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      } finally {
                        // Hide loading indicator
                        if (mounted) {
                          setState(() {
                            _isLoading = false;
                          });
                        }
                      }
                    },
                    icon: _isLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        )
                      : Icon(
                          _isFollowing ? Icons.check : Icons.add,
                          color: Colors.white,
                        ),
                    label: Text(
                      _isLoading
                        ? 'Processing...'
                        : (_isFollowing ? 'Following' : 'Follow'),
                      style: GoogleFonts.poppins(
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _isFollowing ? const Color(0xFF4CAF50) : const Color(0xFF6A11CB),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 0,
                    ),
                  ),
                )
              else
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      // Show edit profile dialog
                      _showEditProfileDialog();
                    },
                    icon: const Icon(
                      Icons.edit,
                      color: Colors.white,
                    ),
                    label: Text(
                      'Edit Profile',
                      style: GoogleFonts.poppins(
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[700],
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 0,
                    ),
                  ),
                ),

              // Message button - more prominent (hidden if viewing own profile)
              if (!_isCurrentUserProfile) ...[
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      // Navigate to messaging page instead of showing dialog
                      _navigateToMessagingPage();
                    },
                    icon: const Icon(
                      Icons.message_outlined,
                      color: Colors.white,
                    ),
                    label: Text(
                      'Message',
                      style: GoogleFonts.poppins(
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF2196F3),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 0,
                    ),
                  ),
                ),
              ],
            ],
          ),

          // Hire button - full width and prominent (hidden if viewing own profile)
          if (!_isCurrentUserProfile) ...[
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                // Always show the hire dialog - it will handle checking for active plans
                _showHireDialog();
              },
              icon: const Icon(
                Icons.work_outline,
                color: Colors.white,
              ),
              label: Text(
                'Hire This Developer',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                  color: Colors.white,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFF5722),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 2,
              ),
            ),
          ],
        ],
      ),
    ).animate()
      .fade(duration: 500.ms, delay: 1400.ms)
      .slideY(begin: 0.2, end: 0, duration: 500.ms, delay: 1400.ms);
  }



  // Show edit profile dialog
  void _showEditProfileDialog() {
    // Create controllers for each field
    final TextEditingController nameController = TextEditingController(text: _userData['name']);
    final TextEditingController usernameController = TextEditingController(text: _userData['username']);
    final TextEditingController bioController = TextEditingController(text: _userData['bio']);
    final TextEditingController locationController = TextEditingController(text: _userData['location']);

    // State variables for username validation
    bool isCheckingUsername = false;
    bool isUsernameAvailable = true;
    String usernameError = '';

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            // Function to check username availability
            Future<void> checkUsername(String username) async {
              if (username.isEmpty || username == _userData['username']) {
                setState(() {
                  isCheckingUsername = false;
                  isUsernameAvailable = true;
                  usernameError = '';
                });
                return;
              }

              setState(() {
                isCheckingUsername = true;
              });

              try {
                // Check if username is available
                final QuerySnapshot result = await FirebaseFirestore.instance
                    .collection('users')
                    .where('username', isEqualTo: username)
                    .limit(1)
                    .get();

                if (result.docs.isEmpty) {
                  setState(() {
                    isUsernameAvailable = true;
                    usernameError = '';
                  });
                } else {
                  setState(() {
                    isUsernameAvailable = false;
                    usernameError = 'Username is already taken';
                  });
                }
              } catch (e) {
                setState(() {
                  isUsernameAvailable = false;
                  usernameError = 'Error checking username: $e';
                });
              } finally {
                setState(() {
                  isCheckingUsername = false;
                });
              }
            }

            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              child: Container(
                padding: const EdgeInsets.all(24),
                constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            color: const Color(0xFF6A11CB).withAlpha(20),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: const Icon(
                            Icons.edit,
                            color: Color(0xFF6A11CB),
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Edit Profile',
                                style: GoogleFonts.poppins(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                'Update your profile information',
                                style: GoogleFonts.poppins(
                                  fontSize: 14,
                                  color: Colors.black54,
                                ),
                              ),
                            ],
                          ),
                        ),
                        IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: const Icon(Icons.close),
                          color: Colors.black54,
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Name field
                            Text(
                              'Name',
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 8),
                            TextField(
                              controller: nameController,
                              decoration: InputDecoration(
                                hintText: 'Enter your name',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: BorderSide(color: Colors.grey[300]!),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: const BorderSide(color: Color(0xFF6A11CB), width: 2),
                                ),
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Username field with availability check
                            Row(
                              children: [
                                Text(
                                  'Username',
                                  style: GoogleFonts.poppins(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                if (isCheckingUsername)
                                  const SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6A11CB)),
                                    ),
                                  )
                                else if (usernameController.text.isNotEmpty && usernameController.text != _userData['username'])
                                  Container(
                                    width: 20,
                                    height: 20,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: isUsernameAvailable ? Colors.green : Colors.red,
                                    ),
                                    child: Icon(
                                      isUsernameAvailable ? Icons.check : Icons.close,
                                      color: Colors.white,
                                      size: 12,
                                    ),
                                  ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            TextField(
                              controller: usernameController,
                              decoration: InputDecoration(
                                hintText: 'Enter your username',
                                prefixText: '@',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: BorderSide(color: Colors.grey[300]!),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: const BorderSide(color: Color(0xFF6A11CB), width: 2),
                                ),
                                errorText: usernameError.isNotEmpty ? usernameError : null,
                              ),
                              onChanged: (value) {
                                // Debounce username check
                                Future.delayed(const Duration(milliseconds: 500), () {
                                  if (value == usernameController.text) {
                                    checkUsername(value);
                                  }
                                });
                              },
                            ),
                            const SizedBox(height: 16),

                            // Bio field
                            Text(
                              'Bio',
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 8),
                            TextField(
                              controller: bioController,
                              maxLines: 3,
                              decoration: InputDecoration(
                                hintText: 'Tell us about yourself',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: BorderSide(color: Colors.grey[300]!),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: const BorderSide(color: Color(0xFF6A11CB), width: 2),
                                ),
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Location field
                            Text(
                              'Location',
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 8),
                            TextField(
                              controller: locationController,
                              decoration: InputDecoration(
                                hintText: 'Enter your location',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: BorderSide(color: Colors.grey[300]!),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  borderSide: const BorderSide(color: Color(0xFF6A11CB), width: 2),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: Text(
                            'Cancel',
                            style: GoogleFonts.poppins(
                              fontWeight: FontWeight.w500,
                              color: Colors.black54,
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        ElevatedButton(
                          onPressed: () async {
                            // Validate inputs
                            if (nameController.text.trim().isEmpty) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Please enter your name'),
                                  behavior: SnackBarBehavior.floating,
                                ),
                              );
                              return;
                            }

                            if (usernameController.text.trim().isEmpty) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Please enter a username'),
                                  behavior: SnackBarBehavior.floating,
                                ),
                              );
                              return;
                            }

                            if (!isUsernameAvailable && usernameController.text != _userData['username']) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Username is already taken'),
                                  behavior: SnackBarBehavior.floating,
                                ),
                              );
                              return;
                            }

                            // Show loading indicator
                            showDialog(
                              context: context,
                              barrierDismissible: false,
                              builder: (BuildContext context) => const Center(
                                child: CircularProgressIndicator(),
                              ),
                            );

                            // Capture form values before async operations
                            final String name = nameController.text.trim();
                            final String username = usernameController.text.trim();
                            final String bio = bioController.text.trim();
                            final String location = locationController.text.trim();

                            // Use a separate method to handle the async operations
                            _updateUserProfile(
                              context: context,
                              name: name,
                              username: username,
                              bio: bio,
                              location: location,
                            );
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF6A11CB),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 12,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: Text(
                            'Save Changes',
                            style: GoogleFonts.poppins(
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  // Update user profile
  Future<void> _updateUserProfile({
    required BuildContext context,
    required String name,
    required String username,
    required String bio,
    required String location,
  }) async {
    // Store context navigator and scaffold messenger before async operations
    final navigator = Navigator.of(context);
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    try {
      // Update user profile in Firebase
      await FirebaseFirestore.instance.collection('users').doc(widget.userId).update({
        'displayName': name,
        'username': username,
        'displayNameLower': name.toLowerCase(),
        'usernameLower': username.toLowerCase(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Update user profile document
      await FirebaseFirestore.instance.collection('user_profiles').doc(widget.userId).set({
        'bio': bio,
        'location': location,
        'updatedAt': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));

      // Only proceed if the widget is still mounted
      if (!mounted) return;

      // Close loading dialog and edit dialog
      navigator.pop(); // Close loading dialog
      navigator.pop(); // Close edit dialog

      // Show success message
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('Profile updated successfully'),
          behavior: SnackBarBehavior.floating,
          backgroundColor: Colors.green,
        ),
      );

      // Refresh user data
      await _loadUserData();
    } catch (e) {
      debugPrint('Error updating profile: $e');

      // Only proceed if the widget is still mounted
      if (!mounted) return;

      // Close loading dialog
      navigator.pop();

      // Show error message
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('Error updating profile: $e'),
          behavior: SnackBarBehavior.floating,
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Create a hire request and navigate to messaging page
  Future<void> _createHireRequestAndNavigate({
    required String projectName,
    required String planTitle,
    required String projectDescription,
    required double budget,
    required String deadline,
    required String unit,
    required Map<String, dynamic> hirePlan,
  }) async {
    try {
      // Show loading indicator
      setState(() {
        _isLoading = true;
      });

      // Get current user ID
      final currentUserId = FirebaseAuth.instance.currentUser?.uid;
      if (currentUserId == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('You need to be logged in to send hire requests'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      // Generate a unique ID for this hire request with more randomness
      final String uniqueId = '${DateTime.now().millisecondsSinceEpoch}_${currentUserId.substring(0, 4)}_${Random().nextInt(10000)}';

      // Extract delivery time from hire plan features if available
      String deliveryTime = deadline;
      int deliveryDays = 10; // Default to 10 days (as per your hire plan)

      debugPrint('PublicProfilePage - Extracting delivery time from hire plan: $hirePlan');

      // First check if duration is directly specified in the plan
      if (hirePlan.containsKey('duration') && hirePlan['duration'] is String) {
        final String duration = hirePlan['duration'];
        debugPrint('PublicProfilePage - Found duration in hire plan: $duration');

        // Extract number of days from duration
        final RegExp daysRegex = RegExp(r'(\d+)\s*days?', caseSensitive: false);
        final match = daysRegex.firstMatch(duration);
        if (match != null) {
          deliveryTime = '${match.group(1)} days';
          deliveryDays = int.tryParse(match.group(1)!) ?? 10;
          debugPrint('PublicProfilePage - Extracted delivery time from duration: $deliveryTime');
        }
      }

      // If not found in duration, check features
      if (deliveryTime == deadline && hirePlan.containsKey('features') && hirePlan['features'] is List) {
        debugPrint('PublicProfilePage - Checking features for delivery time');
        // Look for delivery_time feature
        for (final feature in hirePlan['features']) {
          if (feature is String && feature.toLowerCase().contains('delivery time')) {
            // Extract the number from the feature string
            final RegExp regex = RegExp(r'(\d+)\s*days?', caseSensitive: false);
            final match = regex.firstMatch(feature);
            if (match != null) {
              deliveryTime = '${match.group(1)} days';
              deliveryDays = int.tryParse(match.group(1)!) ?? 10;
              debugPrint('PublicProfilePage - Extracted delivery time from feature: $deliveryTime');
            }
          }
        }
      }

      // If still not found, use the deadline parameter but ensure it has "days" suffix
      if (deliveryTime == deadline && deadline.isNotEmpty) {
        debugPrint('PublicProfilePage - Using deadline parameter: $deadline');
        // Check if deadline is just a number
        if (RegExp(r'^\d+$').hasMatch(deadline)) {
          deliveryDays = int.tryParse(deadline) ?? 10;
          deliveryTime = '$deliveryDays days';
          debugPrint('PublicProfilePage - Formatted deadline as: $deliveryTime');
        } else {
          // Try to extract a number from the deadline
          final RegExp regex = RegExp(r'(\d+)', caseSensitive: false);
          final match = regex.firstMatch(deadline);
          if (match != null) {
            deliveryDays = int.tryParse(match.group(1)!) ?? 10;
            deliveryTime = '$deliveryDays days';
            debugPrint('PublicProfilePage - Extracted days from deadline: $deliveryTime');
          }
        }
      }

      debugPrint('PublicProfilePage - Using delivery time: $deliveryTime');

      // Always create a new hire request document for each new request
      final hireRequestRef = await FirebaseFirestore.instance.collection('hire_requests').add({
        'clientId': currentUserId,
        'developerId': widget.userId,
        'planId': hirePlan['id'] ?? 'custom',
        'planName': planTitle,
        'projectDetails': projectDescription,
        'budget': budget,
        'timeframe': deliveryTime.isNotEmpty ? deliveryTime : unit,
        'deliveryDays': deliveryDays, // Add explicit delivery days
        'status': 'pending',
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'milestones': [],
        'files': [],
        'isCompleted': false,
        'isRated': false,
        'conversationId': '', // Will be updated after conversation is created
        'uniqueId': uniqueId, // Add a unique ID to ensure uniqueness
        'forceNewConversation': true, // Flag to force creating a new conversation
        'createTime': DateTime.now().toString(), // Add creation time for additional uniqueness
      });

      final String hireRequestId = hireRequestRef.id;
      debugPrint('Created new hire request: $hireRequestId');

      // Navigate to messaging page with hire request
      if (mounted) {
        _navigateToMessagingPage(
          initialMessage: projectDescription,
          isHireRequest: true,
          additionalData: {
            'isHireRequest': true,
            'projectName': projectName,
            'planName': planTitle,
            'deadline': deliveryTime.isNotEmpty ? deliveryTime : null,
            'status': 'pending',
            'type': 'hire',
            'budget': budget.toString(),
            'duration': deliveryTime.isNotEmpty ? deliveryTime : unit,
            'deliveryDays': deliveryDays,
            'hireRequestId': hireRequestId, // Use the hire request ID
          },
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating hire request: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Navigate to messaging page with this user
  void _navigateToMessagingPage({
    String? initialMessage,
    bool isHireRequest = false,
    Map<String, dynamic>? additionalData,
  }) async {
    if (widget.userId == null) return;

    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Create a conversation or get existing one
      final messagingService = MessagingService();
      final notificationService = NotificationService();

      // Get current user ID
      final currentUserId = messagingService.currentUserId;
      if (currentUserId == null) {
        if (mounted) {
          Navigator.pop(context); // Close loading dialog
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('You need to be logged in to send messages'),
              behavior: SnackBarBehavior.floating,
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      // For hire requests, we always want to create a new conversation
      // For regular messages, check if a conversation already exists
      String existingConversationId = '';

      // For hire requests, we NEVER use existing conversations
      // For regular messages, check if a conversation already exists
      if (!isHireRequest) {
        // Query Firestore directly to find existing conversations
        // We'll avoid using composite indexes by doing a simpler query and filtering in code
        final QuerySnapshot existingConversations = await FirebaseFirestore.instance
            .collection('conversations')
            .where('participants', arrayContains: currentUserId)
            .get();

        // Log for debugging
        debugPrint('PublicProfilePage - Found ${existingConversations.docs.length} conversations for current user');

        // Look for a NORMAL conversation with the target user (not a hire request)
        for (final doc in existingConversations.docs) {
          final List<dynamic> participants = doc.get('participants') as List<dynamic>;
          if (participants.contains(widget.userId)) {
            // Get the conversation data
            final data = doc.data() as Map<String, dynamic>;

            // Check if this is a normal conversation (not a hire request)
            // We're filtering in code to avoid needing a composite index
            final String type = data['type'] as String? ?? 'normal';
            if (type != 'hire') {
              existingConversationId = doc.id;
              debugPrint('PublicProfilePage - Found existing normal conversation: $existingConversationId');
              break;
            } else {
              debugPrint('PublicProfilePage - Skipping hire request conversation: ${doc.id}');
            }
          }
        }
      } else {
        debugPrint('PublicProfilePage - This is a hire request, creating a new conversation');
        // Force new conversation for hire requests
        existingConversationId = '';
      }

      // Prepare the message to send
      final String message;
      if (initialMessage != null) {
        // Only send a message if explicitly provided
        message = initialMessage;
      } else {
        // Don't auto-send a welcome message, we'll show it as a suggestion instead
        message = '';
      }

      // Send the message or create a conversation
      Map<String, dynamic> result;

      // For hire requests, always create a new conversation directly
      if (isHireRequest) {
        // Get the hire request ID
        final String hireRequestId = additionalData?['hireRequestId'] ?? '';

        if (hireRequestId.isEmpty) {
          throw Exception('Hire request ID is missing');
        }

        // Create a new conversation directly in Firestore
        final conversationRef = await FirebaseFirestore.instance.collection('conversations').add({
          'participants': [currentUserId, widget.userId],
          'createdAt': FieldValue.serverTimestamp(),
          'lastMessageTimestamp': FieldValue.serverTimestamp(),
          'lastMessage': message,
          'type': 'hire',
          'status': 'pending',
          'unreadCount': {
            widget.userId: 1,
            currentUserId: 0,
          },
          'isNew': false,
          'welcomeMessageSent': true,
          'project': additionalData?['projectName'] ?? 'Custom Project',
          'budget': additionalData?['budget'] ?? 'Not specified',
          'duration': additionalData?['duration'] ?? 'Not specified',
          'hireRequestId': hireRequestId,
          'uniqueId': 'hire_${DateTime.now().millisecondsSinceEpoch}_${currentUserId.substring(0, 4)}',
          'developerId': widget.userId, // Store developer ID for easy access
          'clientId': currentUserId, // Store client ID for easy access
        });

        // Add the initial message to the conversation
        await FirebaseFirestore.instance
            .collection('conversations')
            .doc(conversationRef.id)
            .collection('messages')
            .add({
              'senderId': currentUserId,
              'text': message,
              'timestamp': FieldValue.serverTimestamp(),
              'isRead': false,
              'isDelivered': false,
              'type': 'text',
              'isHireRequest': true,
            });

        // We're not using subcollections anymore - just update the main hire request document
        try {
          // Update the hire request with the conversation ID and message info
          await FirebaseFirestore.instance
              .collection('hire_requests')
              .doc(hireRequestId)
              .update({
                'conversationId': conversationRef.id,
                'lastMessage': message,
                'lastMessageTimestamp': FieldValue.serverTimestamp(),
                'lastMessageSender': currentUserId,
              });
        } catch (e) {
          // Log the error but continue - this is not critical
          debugPrint('Error updating hire request: $e');
          // The conversation will still work even if this fails
        }

        // We already updated the hire request above, no need to do it again

        // Create a result with the new conversation ID
        result = {
          'success': true,
          'conversationId': conversationRef.id,
        };

        debugPrint('PublicProfilePage - Created new hire request conversation: ${conversationRef.id}');
      } else if (message.isNotEmpty) {
        // For regular messages, use the messaging service
        result = await messagingService.sendMessage(
          conversationId: existingConversationId, // Empty or existing conversation ID
          text: message,
          receiverId: widget.userId!,
          additionalData: additionalData,
        );
      } else {
        // Just create a result with the existing conversation ID
        result = {
          'success': true,
          'conversationId': existingConversationId,
        };
      }

      // Close loading dialog
      if (mounted) Navigator.pop(context);

      if (result['success'] == true) {
        // Navigate to chat screen with the conversation
        final String conversationId = result['conversationId'];

        // Create a conversation object for the chat screen
        final Map<String, dynamic> conversation = {
          'id': conversationId,
          'name': _userData['name'] ?? 'User',
          'avatar': _userData['avatar'],
          'otherUserId': widget.userId,
          'isOnline': _isOnline,
          'type': isHireRequest ? 'hire' : 'normal',
          'isNew': false, // Ensure we don't treat this as a new conversation
          'welcomeMessageSent': true, // Mark welcome message as sent
        };

        // Send additional notification for hire requests
        if (isHireRequest && additionalData != null && additionalData.containsKey('hireRequestId')) {
          // Get current user info
          final currentUserDoc = await FirebaseFirestore.instance.collection('users').doc(currentUserId).get();
          final currentUserData = currentUserDoc.data() ?? {};
          final currentUserName = currentUserData['displayName'] ?? 'Someone';

          // Update the hire request with the conversation ID
          final String hireRequestId = additionalData['hireRequestId'];
          await FirebaseFirestore.instance.collection('hire_requests').doc(hireRequestId).update({
            'conversationId': conversationId,
          });

          // Send a hire request notification
          await notificationService.sendNotification(
            userId: widget.userId!,
            title: 'New Hire Request',
            body: '$currentUserName wants to hire you',
            type: 'hire_request',
            data: {
              'conversationId': conversationId,
              'hireRequestId': hireRequestId,
              'senderId': currentUserId,
              'senderName': currentUserName,
            },
          );
        }

        // Navigate to chat screen
        if (mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => ChatScreen(conversation: conversation),
            ),
          );
        }
      } else {
        // Show error message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to start conversation: ${result['error'] ?? 'Unknown error'}'),
              behavior: SnackBarBehavior.floating,
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // Close loading dialog if still showing
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error starting conversation: $e'),
            behavior: SnackBarBehavior.floating,
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Show hire dialog with available plans
  void _showHireDialog() {
    // Get active plans
    final List<Map<String, dynamic>> activePlans = _getActivePlans();

    // If no active plans, show a message and return
    if (activePlans.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'This developer is not accepting hire requests at the moment.',
            style: GoogleFonts.poppins(),
          ),
          behavior: SnackBarBehavior.floating,
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // If there are active plans, show the dialog
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          padding: const EdgeInsets.all(24),
          constraints: const BoxConstraints(maxWidth: 500),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFF5722).withAlpha(20),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Icon(
                      Icons.work_outline,
                      color: Color(0xFFFF5722),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Hire ${_userData['name'] ?? 'User'}',
                          style: GoogleFonts.poppins(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'Select a plan that works for you',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            color: Colors.black54,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                    color: Colors.black54,
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Show active hire plans
              Column(
                children: activePlans.map((plan) {
                      // Define gradient colors based on plan type
                      List<Color> gradient;
                      bool isPopular = plan['isPopular'] ?? false;

                      switch (plan['type']) {
                        case 'hourly':
                          gradient = const [Color(0xFF6A11CB), Color(0xFF2575FC)];
                          break;
                        case 'fixed':
                          gradient = const [Color(0xFFFF416C), Color(0xFFFF4B2B)];
                          break;
                        case 'retainer':
                          gradient = const [Color(0xFF11998E), Color(0xFF38EF7D)];
                          break;
                        default:
                          gradient = const [Color(0xFF6A11CB), Color(0xFF2575FC)];
                      }

                      return Column(
                        children: [
                          _buildHirePlanCard(
                            title: plan['title'] ?? 'Untitled Plan',
                            price: '\$${plan['price']?.toString() ?? '0'}',
                            unit: plan['unit'] ?? '',
                            features: List<String>.from(plan['features'] ?? []),
                            gradient: gradient,
                            isPopular: isPopular,
                            duration: plan['duration'] ?? 'Not specified',
                          ),
                          const SizedBox(height: 16),
                        ],
                      );
                    }).toList(),
                ),

              const SizedBox(height: 24),
              Center(
                child: TextButton.icon(
                  onPressed: () {
                    Navigator.pop(context);
                    // Use the messaging page navigation with hire request flag
                    _navigateToMessagingPage(
                      initialMessage: 'I would like to discuss a custom hire plan with you.',
                      isHireRequest: true,
                    );
                  },
                  icon: const Icon(Icons.message_outlined, size: 16),
                  label: Text(
                    'Contact for custom requirements',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  style: TextButton.styleFrom(
                    foregroundColor: const Color(0xFF6A11CB),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    ));
  }

  // Build hire plan card
  Widget _buildHirePlanCard({
    required String title,
    required String price,
    required String unit,
    required List<String> features,
    required List<Color> gradient,
    required bool isPopular,
    String? duration,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: isPopular ? gradient[0].withAlpha(50) : Colors.grey[200]!,
          width: isPopular ? 2 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Plan header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: gradient,
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(15),
                topRight: Radius.circular(15),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: GoogleFonts.poppins(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Wrap(
                        crossAxisAlignment: WrapCrossAlignment.end,
                        spacing: 4,
                        children: [
                          Text(
                            price,
                            style: GoogleFonts.poppins(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          Text(
                            unit,
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              color: Colors.white.withAlpha(200),
                            ),
                          ),
                          if (duration != null && duration.isNotEmpty)
                            Text(
                              ' • $duration',
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                color: Colors.white.withAlpha(200),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
                if (isPopular)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withAlpha(30),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      'Popular',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // Plan features
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ...features.map((feature) {
                  // Parse the feature string to extract name and value
                  String featureName = feature;
                  String featureValue = '';

                  if (feature.contains(':')) {
                    final parts = feature.split(':');
                    featureName = parts[0].trim();
                    featureValue = parts[1].trim();

                    // Format boolean values
                    if (featureValue.toLowerCase() == 'true') {
                      featureValue = 'Yes';
                    } else if (featureValue.toLowerCase() == 'false' ||
                              featureValue.toLowerCase() == 'no' ||
                              featureValue.toLowerCase() == '0' ||
                              featureValue.toLowerCase() == 'none') {
                      featureValue = 'No';
                    }
                  }

                  return Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: 20,
                          height: 20,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: featureValue.toLowerCase() == 'no'
                              ? Colors.red.withAlpha(15)
                              : gradient[0].withAlpha(15),
                          ),
                          child: Center(
                            child: Icon(
                              featureValue.toLowerCase() == 'no' ? Icons.close : Icons.check,
                              size: 14,
                              color: featureValue.toLowerCase() == 'no' ? Colors.red : gradient[0],
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: RichText(
                            text: TextSpan(
                              children: [
                                TextSpan(
                                  text: featureName,
                                  style: GoogleFonts.poppins(
                                    fontSize: 14,
                                    color: Colors.black87,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                if (featureValue.isNotEmpty && featureValue != 'Yes' && featureValue != 'No')
                                  TextSpan(
                                    text: ': $featureValue',
                                    style: GoogleFonts.poppins(
                                      fontSize: 14,
                                      color: Colors.black87,
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),

                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      // Show project details dialog
                      _showProjectDetailsDialog(title);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: gradient[0],
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      'Select Plan',
                      style: GoogleFonts.poppins(
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabSection() {
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              bottom: BorderSide(
                color: Colors.grey[200]!,
                width: 1,
              ),
            ),
          ),
          child: TabBar(
            controller: _tabController,
            labelColor: const Color(0xFF6A11CB),
            unselectedLabelColor: Colors.black54,
            labelStyle: GoogleFonts.poppins(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
            unselectedLabelStyle: GoogleFonts.poppins(
              fontWeight: FontWeight.w500,
              fontSize: 16,
            ),
            indicatorColor: const Color(0xFF6A11CB),
            indicatorWeight: 3,
            indicatorSize: TabBarIndicatorSize.label,
            dividerColor: Colors.transparent,
            tabs: const [
              Tab(text: 'Assets'),
              Tab(text: 'Activity'),
              Tab(text: 'Reviews'),
            ],
          ),
        ).animate()
          .fade(duration: 500.ms, delay: 1500.ms)
          .slideY(begin: 0.2, end: 0, duration: 500.ms, delay: 1500.ms),
        SizedBox(
          height: 600, // Increased height for better content display
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildAssetsTab(),
              _buildActivityTab(),
              _buildReviewsTab(),
            ],
          ),
        ).animate()
          .fade(duration: 500.ms, delay: 1600.ms),
      ],
    );
  }

  Widget _buildAssetsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Filter options
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(10),
                  blurRadius: 4,
                  spreadRadius: 0,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                const Icon(Icons.filter_list, color: Color(0xFF6A11CB)),
                const SizedBox(width: 8),
                Text(
                  'Filter',
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF6A11CB),
                  ),
                ),
                const SizedBox(width: 16),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: const Color(0xFF6A11CB).withAlpha(15),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'All',
                    style: GoogleFonts.poppins(
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF6A11CB),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'UI Kits',
                    style: GoogleFonts.poppins(
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'Code',
                    style: GoogleFonts.poppins(
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),

          // Assets grid using Wrap for responsive layout
          _assets.isEmpty
              ? Center(
                  child: Padding(
                    padding: const EdgeInsets.all(32.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.code,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'No assets yet',
                          style: GoogleFonts.poppins(
                            fontSize: 18,
                            fontWeight: FontWeight.w500,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'This user hasn\'t uploaded any assets',
                          textAlign: TextAlign.center,
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            color: Colors.grey[500],
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              : Wrap(
                  spacing: 16,
                  runSpacing: 16,
                  children: _assets.map((asset) {
                    return _buildAssetCard(
                      title: asset['name'] ?? 'Untitled',
                      type: asset['type'] ?? 'Unknown',
                      price: '\$${asset['price']?.toStringAsFixed(2) ?? '0.00'}',
                      downloads: asset['downloads'] ?? 0,
                      rating: asset['rating']?.toDouble() ?? 0.0,
                      imageUrl: asset['image'],
                    );
                  }).toList(),
                ),
        ],
      ),
    );
  }

  Widget _buildAssetCard({
    required String title,
    required String type,
    required String price,
    required int downloads,
    required double rating,
    String? imageUrl,
  }) {
    // Calculate width based on screen size (2 columns on most screens)
    final width = (MediaQuery.of(context).size.width - 48) / 2;

    return Container(
      width: width,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Asset Image
          Container(
            height: 120,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: imageUrl != null && imageUrl.isNotEmpty
                ? ClipRRect(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(12),
                      topRight: Radius.circular(12),
                    ),
                    child: Image.network(
                      imageUrl,
                      fit: BoxFit.cover,
                      width: double.infinity,
                      height: 120,
                      errorBuilder: (context, error, stackTrace) {
                        return Center(
                          child: Icon(
                            Icons.image_not_supported,
                            size: 40,
                            color: Colors.grey[400],
                          ),
                        );
                      },
                    ),
                  )
                : Center(
                    child: Icon(
                      type == 'UI Kit' ? Icons.dashboard : (type == 'Code' ? Icons.code : Icons.extension),
                      size: 40,
                      color: Colors.grey[400],
                    ),
                  ),
          ),

          // Asset Details
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Asset Type Badge
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getTypeColor(type).withAlpha(15),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    type,
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: _getTypeColor(type),
                    ),
                  ),
                ),
                const SizedBox(height: 8),

                // Title
                Text(
                  title,
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),

                // Rating
                Row(
                  children: [
                    ...List.generate(5, (index) {
                      return Icon(
                        index < rating.floor() ? Icons.star : (index < rating ? Icons.star_half : Icons.star_border),
                        color: const Color(0xFFFFB400),
                        size: 16,
                      );
                    }),
                    const SizedBox(width: 4),
                    Text(
                      rating.toString(),
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                // Price and Downloads
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      price,
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: price == 'Free' ? const Color(0xFF4CAF50) : Colors.black87,
                      ),
                    ),
                    Row(
                      children: [
                        const Icon(Icons.download, size: 14, color: Colors.black54),
                        const SizedBox(width: 4),
                        Text(
                          downloads.toString(),
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: Colors.black54,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getTypeColor(String type) {
    switch (type) {
      case 'UI Kit':
        return const Color(0xFF6A11CB);
      case 'Code':
        return const Color(0xFF2196F3);
      case 'Icon Pack':
        return const Color(0xFFFF9800);
      default:
        return const Color(0xFF6A11CB);
    }
  }

  // Show project details dialog
  void _showProjectDetailsDialog(String planTitle) {
    final TextEditingController projectDescriptionController = TextEditingController();

    // Get the hire plan details
    final hirePlan = _hirePlans.firstWhere(
      (plan) => plan['title'] == planTitle,
      orElse: () => {'price': 0.0, 'unit': 'hour', 'duration': 'Not specified'},
    );

    // Get the budget and duration from the plan
    final double budget = hirePlan['price'] ?? 0.0;
    final String unit = hirePlan['unit'] ?? 'hour';
    final String duration = hirePlan['duration'] ?? 'Not specified';

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          padding: const EdgeInsets.all(24),
          constraints: const BoxConstraints(maxWidth: 500),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: const Color(0xFF6A11CB).withAlpha(20),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Icon(
                        Icons.assignment_outlined,
                        color: Color(0xFF6A11CB),
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Project Details',
                            style: GoogleFonts.poppins(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'Tell us about your project',
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              color: Colors.black54,
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(Icons.close),
                      color: Colors.black54,
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // Plan details
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Selected Plan: $planTitle',
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Budget: \$${budget.toStringAsFixed(2)}',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        'Duration: $duration',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),

                // Project description field
                Text(
                  'Project Description',
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: projectDescriptionController,
                  maxLines: 5,
                  decoration: InputDecoration(
                    hintText: 'Describe your project requirements',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    contentPadding: const EdgeInsets.all(16),
                  ),
                ),
                const SizedBox(height: 24),

                // Submit button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      final String projectDescription = projectDescriptionController.text.trim();

                      if (projectDescription.isEmpty) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Please describe your project'),
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                        return;
                      }

                      Navigator.pop(context);

                      // Create a hire request in the hire_requests collection first
                      _createHireRequestAndNavigate(
                        projectName: planTitle, // Use plan title as project name
                        planTitle: planTitle,
                        projectDescription: projectDescription,
                        budget: budget,
                        deadline: _getDeliveryTimeFromPlan(hirePlan), // Extract proper delivery time
                        unit: hirePlan['duration'] == 'Project' ? 'Project' : unit,
                        hirePlan: hirePlan,
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF6A11CB),
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      'Submit Hire Request',
                      style: GoogleFonts.poppins(
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActivityTab() {
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _userContentService.getUserActivity(userId: widget.userId),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 48, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'Error loading activity',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          );
        }

        final activities = snapshot.data ?? [];

        if (activities.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.history, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'No activity yet',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'This user hasn\'t posted any activity',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.grey[500],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          physics: const BouncingScrollPhysics(),
          itemCount: activities.length,
          itemBuilder: (context, index) {
            final activity = activities[index];

            IconData icon;
            switch (activity['type']) {
              case 'upload':
                icon = Icons.upload_file;
                break;
              case 'update':
                icon = Icons.update;
                break;
              case 'comment':
                icon = Icons.comment;
                break;
              case 'like':
                icon = Icons.thumb_up;
                break;
              case 'follow':
                icon = Icons.person_add;
                break;
              default:
                icon = Icons.history;
            }

            return _buildActivityItem(
              activity: activity['text'] ?? 'Unknown activity',
              icon: icon,
              time: activity['time'] ?? 'Unknown time',
            );
          },
        );
      },
    );
  }

  Widget _buildActivityItem({
    required String activity,
    required IconData icon,
    required String time,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: const Color(0xFF6A11CB).withAlpha(15),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: const Color(0xFF6A11CB),
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  activity,
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  time,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.black54,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.chevron_right),
            onPressed: () {},
            color: Colors.black54,
          ),
        ],
      ),
    );
  }

  Widget _buildReviewsTab() {
    debugPrint('PublicProfilePage - Building reviews tab for user: ${widget.userId}');

    // First check if we have access to the reviews collection
    return FutureBuilder<bool>(
      future: _userContentService.checkReviewsAccess(),
      builder: (context, accessSnapshot) {
        if (accessSnapshot.connectionState == ConnectionState.waiting) {
          debugPrint('PublicProfilePage - Checking reviews access...');
          return const Center(child: CircularProgressIndicator());
        }

        if (accessSnapshot.hasError) {
          debugPrint('PublicProfilePage - Error checking reviews access: ${accessSnapshot.error}');
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error_outline, size: 48, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'Error checking reviews access: ${accessSnapshot.error}',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          );
        }

        final bool hasAccess = accessSnapshot.data ?? false;
        debugPrint('PublicProfilePage - Reviews access check result: $hasAccess');

        if (!hasAccess) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.lock_outline, size: 48, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'Cannot access reviews due to permission issues',
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          );
        }

        // Now fetch the reviews
        return FutureBuilder<List<Map<String, dynamic>>>(
          future: _userContentService.getUserReviews(userId: widget.userId),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              debugPrint('PublicProfilePage - Reviews loading...');
              return const Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError) {
              debugPrint('PublicProfilePage - Error loading reviews: ${snapshot.error}');
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error_outline, size: 48, color: Colors.grey[400]),
                    const SizedBox(height: 16),
                    Text(
                      'Error loading reviews: ${snapshot.error}',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              );
            }

            final reviews = snapshot.data ?? [];
            debugPrint('PublicProfilePage - Loaded ${reviews.length} reviews');

            if (reviews.isEmpty) {
              debugPrint('PublicProfilePage - No reviews found');

              // Check for completed projects that haven't been rated
              _checkForCompletedProjects();

              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.star_border, size: 64, color: Colors.grey[400]),
                    const SizedBox(height: 16),
                    Text(
                      'No reviews yet',
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'This user hasn\'t received any reviews',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.grey[500],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              );
            }

            // Calculate average rating
            double totalRating = 0;
            for (final review in reviews) {
              totalRating += (review['rating'] as num?)?.toDouble() ?? 5.0;
            }
            final double averageRating = reviews.isEmpty ? 0 : totalRating / reviews.length;

            return Column(
              children: [
                // Show average rating at the top
                Container(
                  margin: const EdgeInsets.all(16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(13),
                        blurRadius: 10,
                        spreadRadius: 0,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Text(
                        'Average Rating',
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[800],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            averageRating.toStringAsFixed(1),
                            style: GoogleFonts.poppins(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.orange[700],
                            ),
                          ),
                          const SizedBox(width: 8),
                          Icon(
                            Icons.star,
                            color: Colors.orange[700],
                            size: 24,
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Based on ${reviews.length} review${reviews.length == 1 ? '' : 's'}',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),

                // List of reviews
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    physics: const BouncingScrollPhysics(),
                    itemCount: reviews.length,
                    itemBuilder: (context, index) {
                      final review = reviews[index];
                      debugPrint('PublicProfilePage - Building review item ${index + 1}/${reviews.length}');

                      return _buildReviewItem(
                        name: review['reviewerName'] ?? 'Anonymous',
                        avatar: review['reviewerAvatar'] ?? '',
                        rating: (review['rating'] as num?)?.toInt() ?? 5,
                        comment: review['comment'] ?? 'No comment provided',
                        time: review['time'] ?? 'Unknown time',
                        projectName: review['projectName'] ?? 'Project',
                      );
                    },
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  // Check for completed projects that haven't been rated
  Future<void> _checkForCompletedProjects() async {
    try {
      final String? userId = widget.userId;
      if (userId == null) return;

      debugPrint('PublicProfilePage - Checking for completed projects for user: $userId');

      // Check for hire requests where this user is the developer and the project is completed but not rated
      final QuerySnapshot hireRequestsSnapshot = await FirebaseFirestore.instance
          .collection('hire_requests')
          .where('developerId', isEqualTo: userId)
          .where('isCompleted', isEqualTo: true)
          .get();

      int completedCount = 0;
      int ratedCount = 0;

      for (final doc in hireRequestsSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        completedCount++;

        if (data['isRated'] == true) {
          ratedCount++;
        }
      }

      debugPrint('PublicProfilePage - Found $completedCount completed projects, $ratedCount of which have been rated');

      // Also check the reviews collection directly
      final QuerySnapshot reviewsSnapshot = await FirebaseFirestore.instance
          .collection('reviews')
          .where('receiverId', isEqualTo: userId)
          .get();

      debugPrint('PublicProfilePage - Found ${reviewsSnapshot.docs.length} reviews in the reviews collection');

    } catch (e) {
      debugPrint('PublicProfilePage - Error checking for completed projects: $e');
    }
  }

  Widget _buildReviewItem({
    required String name,
    String avatar = '',
    required int rating,
    required String comment,
    required String time,
    String projectName = 'Project',
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // Avatar
              avatar.isNotEmpty
                  ? CircleAvatar(
                      radius: 20,
                      backgroundImage: NetworkImage(avatar),
                    )
                  : Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        shape: BoxShape.circle,
                      ),
                      child: Center(
                        child: Icon(
                          Icons.person,
                          size: 24,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      name,
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'Project: $projectName',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: List.generate(5, (index) {
                        return Icon(
                          index < rating ? Icons.star : Icons.star_border,
                          color: index < rating ? const Color(0xFFFFB400) : Colors.grey[300],
                          size: 16,
                        );
                      }),
                    ),
                  ],
                ),
              ),
              Text(
                time,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.black54,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            comment,
            style: GoogleFonts.poppins(
              fontSize: 14,
              height: 1.5,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }
}
