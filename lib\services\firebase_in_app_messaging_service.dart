import 'package:flutter/foundation.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// A service class to handle Firebase In-App Messaging
class FirebaseInAppMessagingService {
  // Singleton pattern
  static final FirebaseInAppMessagingService _instance = FirebaseInAppMessagingService._internal();
  factory FirebaseInAppMessagingService() => _instance;
  FirebaseInAppMessagingService._internal();

  // Firebase instances
  final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Initialize the service
  Future<void> initialize() async {
    try {
      // Request permission for all platforms
      final settings = await _messaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
        criticalAlert: true,
        announcement: true,
      );
      debugPrint('User granted permission: ${settings.authorizationStatus}');

      // Set foreground notification presentation options
      await _messaging.setForegroundNotificationPresentationOptions(
        alert: true,
        badge: true,
        sound: true,
      );

      // For iOS, set the notification categories
      if (!kIsWeb && defaultTargetPlatform == TargetPlatform.iOS) {
        await _messaging.setAutoInitEnabled(true);
      }

      // Get FCM token
      final token = await _messaging.getToken();
      debugPrint('FCM Token: $token');

      // Save token to Firestore
      if (token != null) {
        await _saveFCMToken(token);
      }

      // Listen for token refresh
      _messaging.onTokenRefresh.listen((newToken) {
        debugPrint('FCM Token refreshed: $newToken');
        _saveFCMToken(newToken);
      });

      // Set up message handlers
      _setupMessageHandlers();

      // Subscribe to topics
      await _messaging.subscribeToTopic('all_users');
      final user = _auth.currentUser;
      if (user != null) {
        await _messaging.subscribeToTopic('user_${user.uid}');
        debugPrint('Subscribed to user-specific topic: user_${user.uid}');
      }

      debugPrint('Firebase In-App Messaging Service initialized successfully');
    } catch (e) {
      debugPrint('Error initializing Firebase In-App Messaging Service: $e');
    }
  }

  // Save FCM token to Firestore
  Future<void> _saveFCMToken(String token) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      // Get existing tokens
      final tokenDoc = await _firestore.collection('fcm_tokens').doc(user.uid).get();
      List<String> tokens = [];

      if (tokenDoc.exists) {
        final data = tokenDoc.data();
        if (data != null && data['tokens'] is List) {
          tokens = List<String>.from(data['tokens']);
        }
      }

      // Add new token if it doesn't exist
      if (!tokens.contains(token)) {
        tokens.add(token);
        await _firestore.collection('fcm_tokens').doc(user.uid).set({
          'uid': user.uid,
          'tokens': tokens,
          'updatedAt': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));
        debugPrint('FCM token saved to Firestore');
      }
    } catch (e) {
      debugPrint('Error saving FCM token: $e');
    }
  }

  // Set up message handlers
  void _setupMessageHandlers() {
    // Handle messages when the app is in the foreground
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      debugPrint('Got a message whilst in the foreground!');
      debugPrint('Message data: ${message.data}');

      if (message.notification != null) {
        debugPrint('Message also contained a notification:');
        debugPrint('Title: ${message.notification!.title}');
        debugPrint('Body: ${message.notification!.body}');

        // Store the notification in Firestore for persistence
        _storeNotificationInFirestore(message);

        // We don't call _showInAppMessage here because it requires a BuildContext
        // Instead, we'll rely on the HomePage to show the notification
        // The HomePage listens to this same stream and has access to a BuildContext
      }
    });

    // Handle message when the app is opened from a terminated state
    FirebaseMessaging.instance.getInitialMessage().then((RemoteMessage? message) {
      if (message != null) {
        debugPrint('App opened from terminated state with message:');
        debugPrint('Message data: ${message.data}');

        // Store the notification in Firestore for persistence
        _storeNotificationInFirestore(message);

        // Handle the message after a delay to ensure the app is fully initialized
        Future.delayed(const Duration(seconds: 1), () {
          _handleMessageTap(message);
        });
      }
    });

    // Handle message when the app is in the background
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      debugPrint('App opened from background state with message:');
      debugPrint('Message data: ${message.data}');

      // Store the notification in Firestore for persistence
      _storeNotificationInFirestore(message);

      _handleMessageTap(message);
    });
  }

  // Store notification in Firestore for persistence
  Future<void> _storeNotificationInFirestore(RemoteMessage message) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      // Create notification document
      await _firestore.collection('notifications').add({
        'userId': user.uid,
        'title': message.notification?.title ?? 'New Notification',
        'body': message.notification?.body ?? '',
        'data': message.data,
        'type': message.data['type'] ?? 'default',
        'read': false,
        'createdAt': FieldValue.serverTimestamp(),
      });

      // Increment unread notifications count
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      if (userDoc.exists) {
        final userData = userDoc.data();
        if (userData != null) {
          final int unreadCount = (userData['unreadNotifications'] as int?) ?? 0;
          await _firestore.collection('users').doc(user.uid).update({
            'unreadNotifications': unreadCount + 1,
          });
        }
      }

      debugPrint('Notification stored in Firestore');
    } catch (e) {
      debugPrint('Error storing notification in Firestore: $e');
    }
  }

  // Internal method to show in-app message from a RemoteMessage
  void _showInAppMessage(RemoteMessage message, {BuildContext? context}) {
    // If context is not provided, we can't show a dialog
    // This is a limitation of the current implementation
    if (context == null) return;

    // Use the public method with the message data
    showInAppMessage(
      context: context,
      title: message.notification?.title ?? 'New Message',
      body: message.notification?.body ?? '',
      data: message.data,
      onView: () {
        _handleMessageTap(message);
      },
    );
  }

  // Handle message tap
  void _handleMessageTap(RemoteMessage message) {
    // Extract message type from data
    final String type = message.data['type'] ?? 'default';

    // Log the message tap for analytics
    debugPrint('Message tapped with type: $type');

    // We can't navigate directly from here because we don't have a BuildContext
    // Instead, we'll store the action in a global variable that can be checked by the app

    // Store the last tapped message for the app to handle
    _lastTappedMessage = message;
    _lastTappedMessageTime = DateTime.now();

    // The actual navigation will be handled by the app when it checks _lastTappedMessage
  }

  // Store the last tapped message for the app to handle
  RemoteMessage? _lastTappedMessage;
  DateTime? _lastTappedMessageTime;

  // Get the last tapped message if it's recent (within the last 5 seconds)
  RemoteMessage? getLastTappedMessage() {
    if (_lastTappedMessage == null || _lastTappedMessageTime == null) {
      return null;
    }

    // Only return the message if it was tapped recently
    final now = DateTime.now();
    final difference = now.difference(_lastTappedMessageTime!);
    if (difference.inSeconds <= 5) {
      final message = _lastTappedMessage;
      // Clear the message so it's not handled multiple times
      _lastTappedMessage = null;
      _lastTappedMessageTime = null;
      return message;
    }

    return null;
  }

  // Show in-app message with custom context
  void showInAppMessage({
    required BuildContext context,
    required String title,
    required String body,
    Map<String, dynamic>? data,
    VoidCallback? onView,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          title,
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          body,
          style: GoogleFonts.poppins(),
        ),
        actions: [
          if (onView != null)
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onView();
              },
              child: Text(
                'View',
                style: GoogleFonts.poppins(
                  color: Colors.blue,
                ),
              ),
            ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Dismiss',
              style: GoogleFonts.poppins(),
            ),
          ),
        ],
      ),
    );
  }
}
