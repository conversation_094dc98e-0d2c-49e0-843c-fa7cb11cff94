<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">
    
    <!-- Inner circle with gradient -->
    <path
        android:pathData="M54,54m-32,0a32,32 0,1 1,64 0a32,32 0,1 1,-64 0"
        android:fillColor="#FFFFFF"
        android:alpha="0.3" />
    
    <!-- A letter shape -->
    <path
        android:pathData="M54,30L74,78H64L54,58L44,78H34L54,30Z"
        android:fillColor="#1A237E" />
    
    <!-- Crossbar of A -->
    <path
        android:pathData="M44,68L64,68"
        android:strokeWidth="3"
        android:strokeColor="#FFFFFF" />
    
    <!-- Connection dots -->
    <path
        android:pathData="M44,48m-4,0a4,4 0,1 1,8 0a4,4 0,1 1,-8 0"
        android:fillColor="#1565C0" />
    
    <path
        android:pathData="M64,58m-4,0a4,4 0,1 1,8 0a4,4 0,1 1,-8 0"
        android:fillColor="#C62828" />
    
    <path
        android:pathData="M48,68m-4,0a4,4 0,1 1,8 0a4,4 0,1 1,-8 0"
        android:fillColor="#2E7D32" />
</vector>
