import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/notification_service.dart';

/// Result class for paginated transaction history
class PaginatedTransactionResult {
  final List<Map<String, dynamic>> transactions;
  final DocumentSnapshot? lastDocument;
  final bool hasMoreData;

  PaginatedTransactionResult({
    required this.transactions,
    this.lastDocument,
    required this.hasMoreData,
  });
}

/// A service class to handle payment withdrawal operations
class WithdrawalService {
  // Singleton pattern
  static final WithdrawalService _instance = WithdrawalService._internal();
  factory WithdrawalService() => _instance;
  WithdrawalService._internal();

  // Firebase instances
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final NotificationService _notificationService = NotificationService();

  // Withdrawal constants
  static const double _minWithdrawalAmount = 20.0; // Minimum withdrawal amount
  static const double _maxWithdrawalAmount = 5000.0; // Maximum withdrawal amount
  static const List<String> _supportedWithdrawalMethods = [
    'Bank Transfer',
    'PayPal',
    'Stripe',
    'Paytm',
    'PhonePe',
    'Google Pay',
    'Apple Pay',
  ];

  // Get supported withdrawal methods
  List<String> getSupportedWithdrawalMethods() {
    return _supportedWithdrawalMethods;
  }

  // Get minimum withdrawal amount
  double getMinWithdrawalAmount() {
    return _minWithdrawalAmount;
  }

  // Get maximum withdrawal amount
  double getMaxWithdrawalAmount() {
    return _maxWithdrawalAmount;
  }

  // Get user's available balance
  Future<double> getUserAvailableBalance() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      final userDoc = await _firestore.collection('users').doc(currentUser.uid).get();
      if (!userDoc.exists) {
        throw Exception('User document not found');
      }

      final userData = userDoc.data()!;
      final walletBalance = (userData['walletBalance'] as num?)?.toDouble() ?? 0.0;
      final pendingWithdrawals = (userData['pendingWithdrawals'] as num?)?.toDouble() ?? 0.0;

      return walletBalance;
    } catch (e) {
      debugPrint('Error getting user available balance: $e');
      rethrow;
    }
  }

  // Get user's pending withdrawals
  Future<double> getUserPendingWithdrawals() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      final userDoc = await _firestore.collection('users').doc(currentUser.uid).get();
      if (!userDoc.exists) {
        throw Exception('User document not found');
      }

      final userData = userDoc.data()!;
      return (userData['pendingWithdrawals'] as num?)?.toDouble() ?? 0.0;
    } catch (e) {
      debugPrint('Error getting user pending withdrawals: $e');
      rethrow;
    }
  }

  // Request withdrawal
  Future<void> requestWithdrawal({
    required double amount,
    required String method,
    required Map<String, dynamic> paymentDetails,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Validate amount
      if (amount < _minWithdrawalAmount) {
        throw Exception('Withdrawal amount must be at least \$$_minWithdrawalAmount');
      }

      if (amount > _maxWithdrawalAmount) {
        throw Exception('Withdrawal amount cannot exceed \$$_maxWithdrawalAmount');
      }

      // Validate method
      if (!_supportedWithdrawalMethods.contains(method)) {
        throw Exception('Unsupported withdrawal method: $method');
      }

      // Check if user has sufficient balance
      final availableBalance = await getUserAvailableBalance();
      if (amount > availableBalance) {
        throw Exception('Insufficient balance. Available: \$${availableBalance.toStringAsFixed(2)}');
      }

      // Use a transaction to ensure data consistency
      await _firestore.runTransaction((transaction) async {
        // Get user document
        final userRef = _firestore.collection('users').doc(currentUser.uid);
        final userDoc = await transaction.get(userRef);

        if (!userDoc.exists) {
          throw Exception('User document not found');
        }

        final userData = userDoc.data()!;
        final currentBalance = (userData['walletBalance'] as num?)?.toDouble() ?? 0.0;
        final pendingWithdrawals = (userData['pendingWithdrawals'] as num?)?.toDouble() ?? 0.0;

        // Calculate new balance and pending withdrawals
        final newBalance = currentBalance - amount;
        final newPendingWithdrawals = pendingWithdrawals + amount;

        // Update user document
        transaction.update(userRef, {
          'walletBalance': newBalance,
          'pendingWithdrawals': newPendingWithdrawals,
          'updatedAt': FieldValue.serverTimestamp(),
        });

        // Create withdrawal request document
        final withdrawalRef = _firestore.collection('withdrawals').doc();
        transaction.set(withdrawalRef, {
          'userId': currentUser.uid,
          'userEmail': currentUser.email,
          'userName': currentUser.displayName,
          'amount': amount,
          'method': method,
          'paymentDetails': paymentDetails,
          'status': 'pending',
          'type': 'withdrawal', // Add type for filtering in transaction history
          'requestDate': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
      });
    } catch (e) {
      debugPrint('Error requesting withdrawal: $e');
      rethrow;
    }
  }

  // Get user's withdrawal history with pagination (avoids composite index requirement)
  Future<PaginatedTransactionResult> getPaginatedTransactionHistory(
    int limit, {
    DocumentSnapshot? startAfterDocument,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Start with a query filtered by userId
      Query query = _firestore
          .collection('withdrawals')
          .where('userId', isEqualTo: currentUser.uid);

      // Apply pagination
      if (startAfterDocument != null) {
        query = query.startAfterDocument(startAfterDocument);
      }

      // Apply limit
      query = query.limit(limit);

      // Execute query
      final withdrawalsSnapshot = await query.get();

      // Check if there are more documents
      bool hasMoreData = withdrawalsSnapshot.docs.length >= limit;

      // Get the last document for pagination
      DocumentSnapshot? lastDocument =
          withdrawalsSnapshot.docs.isNotEmpty ?
          withdrawalsSnapshot.docs.last : null;

      // Map documents to data
      final transactions = withdrawalsSnapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        data['id'] = doc.id;
        return data;
      }).toList();

      // Sort the results in memory (client-side) to avoid composite index
      transactions.sort((a, b) {
        final aDate = a['requestDate'] as Timestamp?;
        final bDate = b['requestDate'] as Timestamp?;

        if (aDate == null && bDate == null) return 0;
        if (aDate == null) return 1;
        if (bDate == null) return -1;

        // Sort in descending order (newest first)
        return bDate.compareTo(aDate);
      });

      return PaginatedTransactionResult(
        transactions: transactions,
        lastDocument: lastDocument,
        hasMoreData: hasMoreData,
      );
    } catch (e) {
      debugPrint('Error getting paginated transaction history: $e');
      rethrow;
    }
  }

  // Legacy method for backward compatibility
  Future<List<Map<String, dynamic>>> getUserWithdrawalHistory() async {
    try {
      final result = await getPaginatedTransactionHistory(100);
      return result.transactions;
    } catch (e) {
      debugPrint('Error getting user withdrawal history: $e');
      rethrow;
    }
  }

  // Get all withdrawal requests (admin only)
  Future<List<Map<String, dynamic>>> getAllWithdrawalRequests() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Check if user is admin
      final userDoc = await _firestore.collection('users').doc(currentUser.uid).get();
      if (!userDoc.exists || userDoc.data()?['isAdmin'] != true) {
        throw Exception('Only admins can view all withdrawal requests');
      }

      final withdrawalsSnapshot = await _firestore
          .collection('withdrawals')
          .orderBy('requestDate', descending: true)
          .get();

      return withdrawalsSnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      debugPrint('Error getting all withdrawal requests: $e');
      rethrow;
    }
  }

  // Approve withdrawal request (admin only)
  Future<void> approveWithdrawalRequest(String withdrawalId, String transactionId) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Check if user is admin
      final userDoc = await _firestore.collection('users').doc(currentUser.uid).get();
      if (!userDoc.exists || userDoc.data()?['isAdmin'] != true) {
        throw Exception('Only admins can approve withdrawal requests');
      }

      // Get the withdrawal document
      final withdrawalDoc = await _firestore.collection('withdrawals').doc(withdrawalId).get();
      if (!withdrawalDoc.exists) {
        throw Exception('Withdrawal request not found');
      }

      final withdrawalData = withdrawalDoc.data()!;
      final userId = withdrawalData['userId'];
      final amount = (withdrawalData['amount'] as num).toDouble();

      // Use a transaction to ensure data consistency
      await _firestore.runTransaction((transaction) async {
        // Get user document
        final userRef = _firestore.collection('users').doc(userId);
        final userDoc = await transaction.get(userRef);

        if (!userDoc.exists) {
          throw Exception('User document not found');
        }

        final userData = userDoc.data()!;
        final pendingWithdrawals = (userData['pendingWithdrawals'] as num?)?.toDouble() ?? 0.0;

        // Calculate new pending withdrawals
        final newPendingWithdrawals = pendingWithdrawals - amount;

        // Update user document
        transaction.update(userRef, {
          'pendingWithdrawals': newPendingWithdrawals,
          'updatedAt': FieldValue.serverTimestamp(),
        });

        // Update withdrawal document
        final withdrawalRef = _firestore.collection('withdrawals').doc(withdrawalId);
        transaction.update(withdrawalRef, {
          'status': 'completed',
          'transactionId': transactionId,
          'processedBy': currentUser.uid,
          'processedDate': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
      });

      // Send notification to user
      await _notificationService.sendNotification(
        userId: userId,
        title: 'Withdrawal Approved',
        body: 'Your withdrawal request for \$${amount.toStringAsFixed(2)} has been approved.',
        type: 'withdrawal',
        data: {
          'status': 'completed',
          'amount': amount,
          'transactionId': transactionId,
        },
      );
    } catch (e) {
      debugPrint('Error approving withdrawal request: $e');
      rethrow;
    }
  }

  // Reject withdrawal request (admin only)
  Future<void> rejectWithdrawalRequest(String withdrawalId, String reason) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Check if user is admin
      final userDoc = await _firestore.collection('users').doc(currentUser.uid).get();
      if (!userDoc.exists || userDoc.data()?['isAdmin'] != true) {
        throw Exception('Only admins can reject withdrawal requests');
      }

      // Get the withdrawal document
      final withdrawalDoc = await _firestore.collection('withdrawals').doc(withdrawalId).get();
      if (!withdrawalDoc.exists) {
        throw Exception('Withdrawal request not found');
      }

      final withdrawalData = withdrawalDoc.data()!;
      final userId = withdrawalData['userId'];
      final amount = (withdrawalData['amount'] as num).toDouble();

      // Use a transaction to ensure data consistency
      await _firestore.runTransaction((transaction) async {
        // Get user document
        final userRef = _firestore.collection('users').doc(userId);
        final userDoc = await transaction.get(userRef);

        if (!userDoc.exists) {
          throw Exception('User document not found');
        }

        final userData = userDoc.data()!;
        final currentBalance = (userData['walletBalance'] as num?)?.toDouble() ?? 0.0;
        final pendingWithdrawals = (userData['pendingWithdrawals'] as num?)?.toDouble() ?? 0.0;

        // Calculate new balance and pending withdrawals
        final newBalance = currentBalance + amount;
        final newPendingWithdrawals = pendingWithdrawals - amount;

        // Update user document
        transaction.update(userRef, {
          'walletBalance': newBalance,
          'pendingWithdrawals': newPendingWithdrawals,
          'updatedAt': FieldValue.serverTimestamp(),
        });

        // Update withdrawal document
        final withdrawalRef = _firestore.collection('withdrawals').doc(withdrawalId);
        transaction.update(withdrawalRef, {
          'status': 'rejected',
          'rejectionReason': reason,
          'processedBy': currentUser.uid,
          'processedDate': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
      });

      // Send notification to user
      await _notificationService.sendNotification(
        userId: userId,
        title: 'Withdrawal Rejected',
        body: 'Your withdrawal request for \$${amount.toStringAsFixed(2)} has been rejected. Reason: $reason',
        type: 'withdrawal',
        data: {
          'status': 'rejected',
          'amount': amount,
          'reason': reason,
        },
      );
    } catch (e) {
      debugPrint('Error rejecting withdrawal request: $e');
      rethrow;
    }
  }
}
