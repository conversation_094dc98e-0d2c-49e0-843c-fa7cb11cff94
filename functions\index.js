const functions = require('firebase-functions');
const admin = require('firebase-admin');
admin.initializeApp();

const db = admin.firestore();

// Function to add earnings to a user's balance
exports.addEarnings = functions.https.onCall(async (data, context) => {
  // Ensure user is authenticated
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be logged in');
  }

  // Check if user is admin
  const adminRef = db.collection('users').doc(context.auth.uid);
  const adminDoc = await adminRef.get();
  
  if (!adminDoc.exists || !adminDoc.data().isAdmin) {
    throw new functions.https.HttpsError('permission-denied', 'Only admins can add earnings');
  }

  const { userId, amount, reason, metadata } = data;

  // Validate the amount
  if (!amount || typeof amount !== 'number' || amount <= 0) {
    throw new functions.https.HttpsError('invalid-argument', 'Amount must be a positive number');
  }

  // Validate the reason
  if (!reason || typeof reason !== 'string') {
    throw new functions.https.HttpsError('invalid-argument', 'Reason is required');
  }

  try {
    // Use a transaction to ensure data consistency
    return await db.runTransaction(async (transaction) => {
      // Get the current user data
      const userRef = db.collection('users').doc(userId);
      const userDoc = await transaction.get(userRef);
      
      if (!userDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'User not found');
      }
      
      const userData = userDoc.data();
      
      // Calculate new balance
      const currentBalance = userData.balance || 0;
      const totalEarnings = userData.totalEarnings || 0;
      const newBalance = currentBalance + amount;
      const newTotalEarnings = totalEarnings + amount;
      
      // Update user document with new balance
      transaction.update(userRef, {
        balance: newBalance,
        totalEarnings: newTotalEarnings,
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      
      // Create a transaction record
      const transactionRef = db.collection('transactions').doc();
      transaction.set(transactionRef, {
        userId,
        type: 'credit',
        amount,
        reason,
        metadata: metadata || {},
        previousBalance: currentBalance,
        newBalance,
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      });
      
      return {
        success: true,
        newBalance,
        transactionId: transactionRef.id
      };
    });
  } catch (error) {
    console.error('Error adding earnings:', error);
    throw new functions.https.HttpsError('internal', 'Failed to add earnings');
  }
});

// Function to request a withdrawal
exports.requestWithdrawal = functions.https.onCall(async (data, context) => {
  // Ensure user is authenticated
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be logged in');
  }

  const { amount, paymentMethod, paymentDetails } = data;
  const userId = context.auth.uid;

  // Validate the amount
  if (!amount || typeof amount !== 'number' || amount <= 0) {
    throw new functions.https.HttpsError('invalid-argument', 'Amount must be a positive number');
  }

  // Validate payment method
  if (!paymentMethod || typeof paymentMethod !== 'string') {
    throw new functions.https.HttpsError('invalid-argument', 'Payment method is required');
  }

  try {
    // Use a transaction to ensure data consistency
    return await db.runTransaction(async (transaction) => {
      // Get the current user data
      const userRef = db.collection('users').doc(userId);
      const userDoc = await transaction.get(userRef);
      
      if (!userDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'User not found');
      }
      
      const userData = userDoc.data();
      
      // Check if user has sufficient balance
      const currentBalance = userData.balance || 0;
      if (currentBalance < amount) {
        throw new functions.https.HttpsError('failed-precondition', 'Insufficient balance');
      }
      
      // Calculate new balance and pending withdrawals
      const pendingWithdrawals = userData.pendingWithdrawals || 0;
      const newBalance = currentBalance - amount;
      const newPendingWithdrawals = pendingWithdrawals + amount;
      
      // Update user document
      transaction.update(userRef, {
        balance: newBalance,
        pendingWithdrawals: newPendingWithdrawals,
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      
      // Create a withdrawal request
      const withdrawalRef = db.collection('withdrawals').doc();
      transaction.set(withdrawalRef, {
        userId,
        amount,
        paymentMethod,
        paymentDetails: paymentDetails || {},
        status: 'pending',
        requestedAt: admin.firestore.FieldValue.serverTimestamp(),
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      
      // Create a transaction record
      const transactionRef = db.collection('transactions').doc();
      transaction.set(transactionRef, {
        userId,
        type: 'withdrawal_request',
        amount,
        reason: 'Withdrawal request',
        previousBalance: currentBalance,
        newBalance,
        withdrawalId: withdrawalRef.id,
        status: 'pending',
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      });
      
      return {
        success: true,
        withdrawalId: withdrawalRef.id,
        newBalance
      };
    });
  } catch (error) {
    console.error('Error requesting withdrawal:', error);
    throw new functions.https.HttpsError('internal', 'Failed to request withdrawal');
  }
});

// Admin function to process a withdrawal (approve or reject)
exports.processWithdrawal = functions.https.onCall(async (data, context) => {
  // Ensure user is authenticated and is an admin
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be logged in');
  }
  
  // Check if user is admin
  const adminRef = db.collection('users').doc(context.auth.uid);
  const adminDoc = await adminRef.get();
  
  if (!adminDoc.exists || !adminDoc.data().isAdmin) {
    throw new functions.https.HttpsError('permission-denied', 'Only admins can process withdrawals');
  }

  const { withdrawalId, action, notes } = data;
  
  if (!withdrawalId || typeof withdrawalId !== 'string') {
    throw new functions.https.HttpsError('invalid-argument', 'Withdrawal ID is required');
  }
  
  if (action !== 'approve' && action !== 'reject') {
    throw new functions.https.HttpsError('invalid-argument', 'Action must be either approve or reject');
  }

  try {
    // Use a transaction to ensure data consistency
    return await db.runTransaction(async (transaction) => {
      // Get the withdrawal request
      const withdrawalRef = db.collection('withdrawals').doc(withdrawalId);
      const withdrawalDoc = await transaction.get(withdrawalRef);
      
      if (!withdrawalDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Withdrawal request not found');
      }
      
      const withdrawalData = withdrawalDoc.data();
      
      // Check if withdrawal is already processed
      if (withdrawalData.status !== 'pending') {
        throw new functions.https.HttpsError('failed-precondition', 'Withdrawal has already been processed');
      }
      
      const userId = withdrawalData.userId;
      const amount = withdrawalData.amount;
      
      // Get the user data
      const userRef = db.collection('users').doc(userId);
      const userDoc = await transaction.get(userRef);
      
      if (!userDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'User not found');
      }
      
      const userData = userDoc.data();
      const pendingWithdrawals = userData.pendingWithdrawals || 0;
      
      // Update withdrawal status
      transaction.update(withdrawalRef, {
        status: action,
        processedBy: context.auth.uid,
        processedAt: admin.firestore.FieldValue.serverTimestamp(),
        notes: notes || '',
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      
      // Update user's pending withdrawals
      const newPendingWithdrawals = Math.max(0, pendingWithdrawals - amount);
      transaction.update(userRef, {
        pendingWithdrawals: newPendingWithdrawals,
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });
      
      // Create a transaction record
      const transactionRef = db.collection('transactions').doc();
      transaction.set(transactionRef, {
        userId,
        type: action === 'approve' ? 'withdrawal_approved' : 'withdrawal_rejected',
        amount,
        reason: action === 'approve' ? 'Withdrawal approved' : 'Withdrawal rejected',
        withdrawalId,
        notes: notes || '',
        processedBy: context.auth.uid,
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      });
      
      return {
        success: true,
        status: action
      };
    });
  } catch (error) {
    console.error('Error processing withdrawal:', error);
    throw new functions.https.HttpsError('internal', 'Failed to process withdrawal');
  }
});
