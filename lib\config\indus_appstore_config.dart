/// Configuration for Indus AppStore Integration
/// 
/// This file contains all the configuration settings for Indus AppStore
/// in-app purchases, including product IDs, pricing, and developer settings.
class IndusAppStoreConfig {
  // Developer Information
  static const String developerEmail = '<EMAIL>';
  static const String appName = 'Asatu - developer community and marketplace';
  static const String packageName = 'com.asatu.com';
  static const String currency = 'INR';
  static const String currencySymbol = '₹';

  // App Store Information
  static const String appStoreName = 'Indus AppStore';
  static const String appStoreUrl = 'https://indusappstore.com'; // Update with actual URL
  
  // Wallet Top-up Configuration
  static const Map<String, WalletTopUpProduct> walletProducts = {
    'wallet_50': WalletTopUpProduct(
      id: 'com.asatu.wallet.50inr',
      amount: 50.0,
      title: '₹50 Wallet Top-up',
      description: 'Add ₹50 to your Asatu wallet',
    ),
    'wallet_100': WalletTopUpProduct(
      id: 'com.asatu.wallet.100inr',
      amount: 100.0,
      title: '₹100 Wallet Top-up',
      description: 'Add ₹100 to your Asatu wallet',
    ),
    'wallet_200': WalletTopUpProduct(
      id: 'com.asatu.wallet.200inr',
      amount: 200.0,
      title: '₹200 Wallet Top-up',
      description: 'Add ₹200 to your Asatu wallet',
    ),
    'wallet_500': WalletTopUpProduct(
      id: 'com.asatu.wallet.500inr',
      amount: 500.0,
      title: '₹500 Wallet Top-up',
      description: 'Add ₹500 to your Asatu wallet',
    ),
    'wallet_1000': WalletTopUpProduct(
      id: 'com.asatu.wallet.1000inr',
      amount: 1000.0,
      title: '₹1000 Wallet Top-up',
      description: 'Add ₹1000 to your Asatu wallet',
    ),
    'wallet_2000': WalletTopUpProduct(
      id: 'com.asatu.wallet.2000inr',
      amount: 2000.0,
      title: '₹2000 Wallet Top-up',
      description: 'Add ₹2000 to your Asatu wallet',
    ),
    'wallet_5000': WalletTopUpProduct(
      id: 'com.asatu.wallet.5000inr',
      amount: 5000.0,
      title: '₹5000 Wallet Top-up',
      description: 'Add ₹5000 to your Asatu wallet',
    ),
  };

  // Premium Subscription Configuration
  static const Map<String, PremiumProduct> premiumProducts = {
    'premium_monthly': PremiumProduct(
      id: 'com.asatu.premium.monthly',
      duration: Duration(days: 30),
      title: 'Asatu Premium Monthly',
      description: 'Get premium features for 1 month',
      price: 99.0, // ₹99 per month
    ),
    'premium_yearly': PremiumProduct(
      id: 'com.asatu.premium.yearly',
      duration: Duration(days: 365),
      title: 'Asatu Premium Yearly',
      description: 'Get premium features for 1 year',
      price: 999.0, // ₹999 per year (save ₹189)
    ),
  };

  // Payment Configuration
  static const double minWalletTopUp = 50.0;
  static const double maxWalletTopUp = 10000.0;
  static const double platformFeePercentage = 20.0; // 20% platform fee
  static const double developerSharePercentage = 80.0; // 80% to developer

  // Testing Configuration
  static const bool isTestMode = true; // Set to false for production
  static const List<String> testAccountEmails = [
    '<EMAIL>',
    '<EMAIL>',
  ];

  // Security Configuration
  static const bool enableServerSideVerification = true;
  static const String verificationEndpoint = 'https://api.asatu.com/verify-purchase';
  
  // Business Rules
  static const int refundPolicyDays = 7; // 7 days refund policy
  static const int subscriptionGracePeriodDays = 3; // 3 days grace period
  static const int maxRetryAttempts = 3; // Max retry attempts for failed payments

  // Feature Flags
  static const bool enableCustomAmounts = true;
  static const bool enablePremiumSubscriptions = true;
  static const bool enableAssetPurchases = true;
  static const bool enableWalletTopUp = true;

  // Helper Methods
  static List<String> getAllProductIds() {
    return [
      ...walletProducts.values.map((p) => p.id),
      ...premiumProducts.values.map((p) => p.id),
    ];
  }

  static WalletTopUpProduct? getWalletProduct(String productId) {
    return walletProducts.values
        .where((product) => product.id == productId)
        .firstOrNull;
  }

  static PremiumProduct? getPremiumProduct(String productId) {
    return premiumProducts.values
        .where((product) => product.id == productId)
        .firstOrNull;
  }

  static String generateCustomWalletProductId(double amount) {
    return 'com.asatu.wallet.custom.${amount.toInt()}inr';
  }

  static double? parseCustomWalletAmount(String productId) {
    if (productId.startsWith('com.asatu.wallet.custom.') && 
        productId.endsWith('inr')) {
      final amountStr = productId
          .replaceFirst('com.asatu.wallet.custom.', '')
          .replaceFirst('inr', '');
      return double.tryParse(amountStr);
    }
    return null;
  }

  static bool isValidCustomAmount(double amount) {
    return amount >= minWalletTopUp && 
           amount <= maxWalletTopUp && 
           amount % 1 == 0; // Must be whole number
  }

  static String formatCurrency(double amount) {
    return '$currencySymbol${amount.toStringAsFixed(2)}';
  }

  static String formatCurrencyInt(double amount) {
    return '$currencySymbol${amount.toInt()}';
  }
}

/// Data class for wallet top-up products
class WalletTopUpProduct {
  final String id;
  final double amount;
  final String title;
  final String description;

  const WalletTopUpProduct({
    required this.id,
    required this.amount,
    required this.title,
    required this.description,
  });

  String get formattedPrice => IndusAppStoreConfig.formatCurrencyInt(amount);
}

/// Data class for premium subscription products
class PremiumProduct {
  final String id;
  final Duration duration;
  final String title;
  final String description;
  final double price;

  const PremiumProduct({
    required this.id,
    required this.duration,
    required this.title,
    required this.description,
    required this.price,
  });

  String get formattedPrice => IndusAppStoreConfig.formatCurrencyInt(price);
  
  int get durationInDays => duration.inDays;
  
  String get durationText {
    if (durationInDays >= 365) {
      final years = (durationInDays / 365).round();
      return years == 1 ? '1 Year' : '$years Years';
    } else if (durationInDays >= 30) {
      final months = (durationInDays / 30).round();
      return months == 1 ? '1 Month' : '$months Months';
    } else {
      return '$durationInDays Days';
    }
  }
}

/// Indus AppStore specific configuration
class IndusAppStoreSettings {
  // Store-specific settings that may need to be configured
  static const String storeId = 'indus_appstore';
  static const String storeName = 'Indus AppStore';
  static const String storePackageName = 'com.indusappstore.store'; // Update if known
  
  // API endpoints (update with actual Indus AppStore URLs)
  static const String baseApiUrl = 'https://api.indusappstore.com';
  static const String purchaseVerificationUrl = '$baseApiUrl/verify';
  static const String subscriptionStatusUrl = '$baseApiUrl/subscription/status';
  
  // Store-specific product ID format
  static String formatProductId(String baseId) {
    // Indus AppStore may have specific formatting requirements
    return baseId; // Update if needed
  }
  
  // Store-specific validation
  static bool isValidProductId(String productId) {
    return productId.startsWith('com.asatu.') && 
           (productId.contains('.wallet.') || 
            productId.contains('.premium.') ||
            productId.contains('.asset.'));
  }
}
