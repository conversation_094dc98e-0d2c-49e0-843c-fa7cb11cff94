import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'notification_service.dart';

class DeadlineService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final NotificationService _notificationService = NotificationService();
  
  String? get currentUserId => _auth.currentUser?.uid;
  
  // Check for approaching deadlines for the current user
  Future<void> checkApproachingDeadlines() async {
    if (currentUserId == null) return;
    
    try {
      // Get all hire requests where the current user is the developer and status is accepted
      final QuerySnapshot snapshot = await _firestore
          .collection('hire_requests')
          .where('developerId', isEqualTo: currentUserId)
          .where('status', isEqualTo: 'accepted')
          .where('isCompleted', isEqualTo: false)
          .get();
      
      debugPrint('DeadlineService - Found ${snapshot.docs.length} active hire requests');
      
      final DateTime now = DateTime.now();
      
      for (final doc in snapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        final String hireRequestId = doc.id;
        
        // Skip if there's no deadline
        if (!data.containsKey('deadline') || data['deadline'] == null) {
          continue;
        }
        
        // Get the deadline date
        final DateTime deadlineDate = (data['deadline'] as Timestamp).toDate();
        
        // Calculate days remaining
        final int daysRemaining = deadlineDate.difference(now).inDays;
        
        // Check if we've already sent a notification for this deadline
        final bool alreadyNotified = await _hasNotificationBeenSent(hireRequestId, daysRemaining);
        
        // If we've already sent a notification for this day count, skip
        if (alreadyNotified) continue;
        
        // Send notification if deadline is approaching (1-2 days left)
        if (daysRemaining <= 2 && daysRemaining >= 0) {
          await _sendDeadlineWarning(data, hireRequestId, daysRemaining);
          
          // Mark this notification as sent
          await _markNotificationAsSent(hireRequestId, daysRemaining);
        }
      }
    } catch (e) {
      debugPrint('DeadlineService - Error checking deadlines: $e');
    }
  }
  
  // Send a deadline warning notification
  Future<void> _sendDeadlineWarning(Map<String, dynamic> hireRequest, String hireRequestId, int daysRemaining) async {
    try {
      final String projectName = hireRequest['planName'] ?? 'Project';
      final String clientId = hireRequest['clientId'];
      final String conversationId = hireRequest['conversationId'] ?? '';
      
      // Skip if there's no conversation ID
      if (conversationId.isEmpty) return;
      
      // Create notification title and body
      String title = 'Deadline Approaching';
      String body = '';
      
      if (daysRemaining == 0) {
        title = 'Deadline is Today!';
        body = 'Your project "$projectName" is due today. Please complete it or request an extension.';
      } else if (daysRemaining == 1) {
        body = 'Your project "$projectName" is due tomorrow. Please complete it soon or request an extension.';
      } else {
        body = 'Your project "$projectName" is due in $daysRemaining days. Please complete it soon or request an extension.';
      }
      
      // Send notification to the developer (current user)
      await _notificationService.sendNotification(
        userId: currentUserId!,
        title: title,
        body: body,
        type: 'deadline_warning',
        data: {
          'hireRequestId': hireRequestId,
          'conversationId': conversationId,
          'daysRemaining': daysRemaining,
          'projectName': projectName,
        },
      );
      
      // Add a system message to the conversation
      await _firestore
          .collection('conversations')
          .doc(conversationId)
          .collection('messages')
          .add({
        'senderId': 'system',
        'text': 'Deadline Warning: $body',
        'timestamp': FieldValue.serverTimestamp(),
        'isRead': false,
        'type': 'system',
        'systemAction': 'deadline_warning',
        'metadata': {
          'daysRemaining': daysRemaining,
          'hireRequestId': hireRequestId,
        },
      });
      
      debugPrint('DeadlineService - Sent deadline warning for project: $projectName, days remaining: $daysRemaining');
    } catch (e) {
      debugPrint('DeadlineService - Error sending deadline warning: $e');
    }
  }
  
  // Check if we've already sent a notification for this deadline and day count
  Future<bool> _hasNotificationBeenSent(String hireRequestId, int daysRemaining) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = 'deadline_notification_${hireRequestId}_$daysRemaining';
      return prefs.getBool(key) ?? false;
    } catch (e) {
      debugPrint('DeadlineService - Error checking notification status: $e');
      return false;
    }
  }
  
  // Mark a notification as sent
  Future<void> _markNotificationAsSent(String hireRequestId, int daysRemaining) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final key = 'deadline_notification_${hireRequestId}_$daysRemaining';
      await prefs.setBool(key, true);
    } catch (e) {
      debugPrint('DeadlineService - Error marking notification as sent: $e');
    }
  }
  
  // Request deadline extension
  Future<bool> requestDeadlineExtension({
    required String hireRequestId,
    required String conversationId,
    required DateTime newDeadline,
    required String reason,
  }) async {
    if (currentUserId == null) return false;
    
    try {
      // Get the hire request
      final DocumentSnapshot hireRequestDoc = await _firestore
          .collection('hire_requests')
          .doc(hireRequestId)
          .get();
      
      if (!hireRequestDoc.exists) return false;
      
      final hireRequestData = hireRequestDoc.data() as Map<String, dynamic>;
      final String clientId = hireRequestData['clientId'];
      final String projectName = hireRequestData['planName'] ?? 'Project';
      
      // Create extension request
      final String extensionId = FirebaseFirestore.instance.collection('temp').doc().id;
      final Map<String, dynamic> extensionRequest = {
        'id': extensionId,
        'currentDeadline': hireRequestData['deadline'],
        'requestedDeadline': Timestamp.fromDate(newDeadline),
        'reason': reason,
        'status': 'pending',
        'requestedAt': FieldValue.serverTimestamp(),
        'requestedBy': currentUserId,
      };
      
      // Update the hire request with the extension request
      await _firestore.collection('hire_requests').doc(hireRequestId).update({
        'extensionRequests': FieldValue.arrayUnion([extensionRequest]),
        'hasActiveExtensionRequest': true,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      
      // Add a system message to the conversation
      await _firestore
          .collection('conversations')
          .doc(conversationId)
          .collection('messages')
          .add({
        'senderId': 'system',
        'text': 'Deadline Extension Requested: The developer has requested to extend the deadline to ${_formatDate(newDeadline)}. Reason: $reason',
        'timestamp': FieldValue.serverTimestamp(),
        'isRead': false,
        'type': 'system',
        'systemAction': 'extension_request',
        'metadata': {
          'extensionId': extensionId,
          'currentDeadline': hireRequestData['deadline'],
          'requestedDeadline': Timestamp.fromDate(newDeadline),
          'reason': reason,
          'hireRequestId': hireRequestId,
        },
      });
      
      // Send notification to the client
      await _notificationService.sendNotification(
        userId: clientId,
        title: 'Deadline Extension Request',
        body: 'The developer has requested to extend the deadline for "$projectName"',
        type: 'extension_request',
        data: {
          'hireRequestId': hireRequestId,
          'conversationId': conversationId,
          'extensionId': extensionId,
          'projectName': projectName,
        },
      );
      
      return true;
    } catch (e) {
      debugPrint('DeadlineService - Error requesting deadline extension: $e');
      return false;
    }
  }
  
  // Format date for display
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
