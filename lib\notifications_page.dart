import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_fonts/google_fonts.dart';
import 'services/notification_service.dart';
import 'utils/performance_optimizations.dart';

class NotificationsPage extends StatefulWidget {
  const NotificationsPage({Key? key}) : super(key: key);

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage> {
  final NotificationService _notificationService = NotificationService();
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final ScrollController _scrollController = ScrollController();
  bool _isLoading = false;

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // Mark all notifications as read
  Future<void> _markAllAsRead() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final userId = _auth.currentUser?.uid;
      if (userId == null) return;

      // Get all unread notifications
      final QuerySnapshot unreadNotifications = await FirebaseFirestore.instance
          .collection('notifications')
          .where('userId', isEqualTo: userId)
          .where('read', isEqualTo: false)
          .get();

      // Mark each as read
      final batch = FirebaseFirestore.instance.batch();
      for (final doc in unreadNotifications.docs) {
        batch.update(doc.reference, {'read': true});
      }
      await batch.commit();

      // Reset unread count
      await FirebaseFirestore.instance.collection('users').doc(userId).update({
        'unreadNotifications': 0,
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'All notifications marked as read',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error marking all notifications as read: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error marking notifications as read: $e',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Get notification icon based on type
  IconData _getNotificationIcon(String type) {
    switch (type) {
      case 'warning':
        return Icons.warning_amber_rounded;
      case 'account_status':
        return Icons.person_off_rounded;
      case 'asset_approval':
        return Icons.check_circle_outline;
      case 'admin_status':
        return Icons.admin_panel_settings;
      case 'message':
        return Icons.message_rounded;
      case 'like':
        return Icons.favorite_rounded;
      case 'comment':
        return Icons.comment_rounded;
      case 'follow':
        return Icons.person_add_rounded;
      default:
        return Icons.notifications_rounded;
    }
  }

  // Get notification color based on type
  Color _getNotificationColor(String type) {
    switch (type) {
      case 'warning':
        return Colors.orange;
      case 'account_status':
        return Colors.red;
      case 'asset_approval':
        return Colors.green;
      case 'admin_status':
        return Colors.blue;
      case 'message':
        return Colors.purple;
      case 'like':
        return Colors.pink;
      case 'comment':
        return Colors.teal;
      case 'follow':
        return Colors.indigo;
      default:
        return Colors.grey;
    }
  }

  // List of notifications
  List<QueryDocumentSnapshot> _notifications = [];
  DocumentSnapshot? _lastDocument;
  bool _hasMoreData = true;

  @override
  void initState() {
    super.initState();
    _loadInitialNotifications();

    // Add scroll listener for pagination
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent * 0.8 &&
          !_isLoading &&
          _hasMoreData) {
        _loadMoreNotifications();
      }
    });

    // Reset unread notification count when page is opened
    final userId = _auth.currentUser?.uid;
    if (userId != null) {
      // First refresh the count to ensure it's accurate
      _notificationService.refreshUnreadNotificationCount(userId);

      // Then reset it to zero since user is viewing notifications
      FirebaseFirestore.instance.collection('users').doc(userId).update({
        'unreadNotifications': 0,
      });
    }
  }

  // Load initial notifications with timeout and optimizations
  Future<void> _loadInitialNotifications() async {
    if (_auth.currentUser == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Add a timeout to prevent hanging
      final docs = await _notificationService.getUserNotificationsPaginated(
        _auth.currentUser!.uid,
      ).timeout(const Duration(seconds: 5), onTimeout: () {
        // Return empty list on timeout
        debugPrint('Notification loading timed out');
        return [];
      });

      if (mounted) {
        setState(() {
          _notifications = docs;
          _lastDocument = docs.isNotEmpty ? docs.last : null;
          _hasMoreData = docs.length >= 20; // If we got a full page, there might be more
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading initial notifications: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          // Show error message to user
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Error loading notifications. Pull down to retry.',
                style: GoogleFonts.poppins(),
              ),
              backgroundColor: Colors.red[700],
            ),
          );
        });
      }
    }
  }

  // Load more notifications (pagination) with timeout and optimizations
  Future<void> _loadMoreNotifications() async {
    if (_auth.currentUser == null || _lastDocument == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Add a timeout to prevent hanging
      final docs = await _notificationService.getUserNotificationsPaginated(
        _auth.currentUser!.uid,
        lastDocument: _lastDocument,
      ).timeout(const Duration(seconds: 5), onTimeout: () {
        // Return empty list on timeout
        debugPrint('Loading more notifications timed out');
        return [];
      });

      if (mounted) {
        setState(() {
          _notifications.addAll(docs);
          _lastDocument = docs.isNotEmpty ? docs.last : _lastDocument;
          _hasMoreData = docs.length >= 20; // If we got a full page, there might be more
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading more notifications: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final userId = _auth.currentUser?.uid;
    if (userId == null) {
      return const Scaffold(
        body: Center(
          child: Text('Please sign in to view notifications'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Notifications',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF6A11CB),
        iconTheme: const IconThemeData(
          color: Colors.white, // Change back arrow color to white
          size: 28, // Make it slightly larger
        ),
        actions: [
          TextButton(
            onPressed: _markAllAsRead,
            child: Text(
              'Mark all as read',
              style: GoogleFonts.poppins(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
      body: _isLoading && _notifications.isEmpty
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : _notifications.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.notifications_off_outlined,
                        size: 64,
                        color: Colors.grey,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No notifications yet',
                        style: GoogleFonts.poppins(
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                          color: Colors.grey[700],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'You\'ll see notifications here when you receive them',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                )
              : Stack(
                  children: [
                    ListView.builder(
                      controller: _scrollController,
                      itemCount: _notifications.length + (_hasMoreData ? 1 : 0),
                      itemBuilder: (context, index) {
                        // Show loading indicator at the bottom
                        if (index == _notifications.length) {
                          return const Center(
                            child: Padding(
                              padding: EdgeInsets.all(16.0),
                              child: CircularProgressIndicator(),
                            ),
                          );
                        }

                        final notification = _notifications[index].data() as Map<String, dynamic>;
                        final notificationId = _notifications[index].id;
                        final bool isRead = notification['read'] ?? false;
                        final String type = notification['type'] ?? 'default';
                        final String title = notification['title'] ?? 'Notification';
                        final String body = notification['body'] ?? '';
                        final Timestamp? createdAt = notification['createdAt'] as Timestamp?;
                        final DateTime date = createdAt?.toDate() ?? DateTime.now();
                        final String timeAgo = _getTimeAgo(date);

                        // Use OptimizedScrollItem for better performance
                        return OptimizedScrollItem(
                          keepAlive: index < 5, // Keep first 5 items alive for better scrolling
                          child: Dismissible(
                            key: Key(notificationId),
                            background: Container(
                              color: Colors.red,
                              alignment: Alignment.centerRight,
                              padding: const EdgeInsets.only(right: 20),
                              child: const Icon(
                                Icons.delete,
                                color: Colors.white,
                              ),
                            ),
                            direction: DismissDirection.endToStart,
                            onDismissed: (direction) {
                              // Remove from local list first for immediate UI update
                              setState(() {
                                _notifications.removeAt(index);
                              });

                              // Show immediate feedback
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'Notification deleted',
                                    style: GoogleFonts.poppins(),
                                  ),
                                  backgroundColor: Colors.grey[800],
                                ),
                              );

                              // Then delete from Firestore in the background without UI feedback
                              PerformanceOptimizations.scheduleTask(() {
                                _notificationService.deleteNotification(notificationId);
                              });
                            },
                            child: OptimizedListItem(
                              child: ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: _getNotificationColor(type).withAlpha(50),
                                  child: Icon(
                                    _getNotificationIcon(type),
                                    color: _getNotificationColor(type),
                                  ),
                                ),
                                title: Text(
                                  title,
                                  style: GoogleFonts.poppins(
                                    fontWeight: isRead ? FontWeight.normal : FontWeight.bold,
                                  ),
                                ),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      body,
                                      style: GoogleFonts.poppins(
                                        fontSize: 14,
                                        color: Colors.black87,
                                        height: 1.3,
                                        fontWeight: isRead ? FontWeight.normal : FontWeight.w500,
                                      ),
                                      maxLines: 3,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      timeAgo,
                                      style: GoogleFonts.poppins(
                                        fontSize: 12,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                                trailing: isRead
                                    ? null
                                    : Container(
                                        width: 12,
                                        height: 12,
                                        decoration: BoxDecoration(
                                          color: _getNotificationColor(type),
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                                onTap: () {
                                  // Update local state first for immediate UI update
                                  if (!isRead) {
                                    setState(() {
                                      final notificationData = _notifications[index].data() as Map<String, dynamic>;
                                      notificationData['read'] = true;
                                    });

                                    // Then update in Firestore in the background
                                    PerformanceOptimizations.scheduleTask(() {
                                      _notificationService.markNotificationAsRead(notificationId);
                                    });
                                  }

                                  // Handle notification tap based on type immediately
                                  _handleNotificationTap(notification);
                                },
                              ),
                            ),
                          ),
                        );
                      },
                    ),

                    // Show refresh indicator
                    if (_isLoading && _notifications.isNotEmpty)
                      const Positioned(
                        top: 0,
                        left: 0,
                        right: 0,
                        child: LinearProgressIndicator(),
                      ),
                  ],
                ),
      // Add pull-to-refresh
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Reset and reload notifications
          setState(() {
            _notifications = [];
            _lastDocument = null;
            _hasMoreData = true;
          });
          _loadInitialNotifications();
        },
        backgroundColor: const Color(0xFF6A11CB),
        child: const Icon(Icons.refresh),
      ),
    );
  }

  // Get time ago string
  String _getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()} year(s) ago';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()} month(s) ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} day(s) ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour(s) ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute(s) ago';
    } else {
      return 'Just now';
    }
  }

  // Handle notification tap based on type
  void _handleNotificationTap(Map<String, dynamic> notification) {
    // Use a try-catch block to handle any errors gracefully
    try {
      final String type = notification['type'] ?? 'default';
      final Map<String, dynamic> data = notification['data'] ?? {};

      // Handle different notification types
      switch (type) {
        case 'warning':
          // Show warning details
          _showWarningDetails(notification['title'], notification['body']);
          break;
        case 'account_status':
          // Show account status details
          _showAccountStatusDetails(data['status'] == 'banned');
          break;
        case 'appreciation':
          // Show appreciation message in a dialog
          _showAppreciationDetails(notification['title'], notification['body']);
          break;
        case 'feedback_confirmation':
          // Show feedback confirmation details
          _showFeedbackConfirmation(notification['title'], notification['body']);
          break;
        default:
          // Default action - just show a snackbar
          if (mounted) {
            final scaffoldMessenger = ScaffoldMessenger.of(context);
            scaffoldMessenger.showSnackBar(
              SnackBar(
                content: Text(
                  notification['body'] ?? 'Notification',
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                backgroundColor: Colors.grey[800],
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            );
          }
          break;
      }
    } catch (e) {
      // Log the error and show a generic message
      debugPrint('Error handling notification tap: $e');
      if (mounted) {
        final scaffoldMessenger = ScaffoldMessenger.of(context);
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(
              'Error displaying notification details',
              style: GoogleFonts.poppins(
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
            backgroundColor: Colors.red[700],
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  // Show appreciation details
  void _showAppreciationDetails(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            const Icon(
              Icons.favorite,
              color: Colors.pink,
              size: 28,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.bold,
                  fontSize: 20,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              message,
              style: GoogleFonts.poppins(
                fontSize: 16,
                height: 1.4,
              ),
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 8),
            Text(
              'Thank you for helping us improve Asatu!',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontStyle: FontStyle.italic,
                color: Colors.grey[700],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: const Color(0xFF6A11CB),
            ),
            child: Text(
              'Close',
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Show feedback confirmation
  void _showFeedbackConfirmation(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            const Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 28,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.bold,
                  fontSize: 20,
                ),
              ),
            ),
          ],
        ),
        content: Text(
          message,
          style: GoogleFonts.poppins(
            fontSize: 16,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: const Color(0xFF6A11CB),
            ),
            child: Text(
              'OK',
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Show warning details
  void _showWarningDetails(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          title,
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
            color: Colors.orange,
          ),
        ),
        content: Text(
          message,
          style: GoogleFonts.poppins(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  // Show account status details
  void _showAccountStatusDetails(bool isBanned) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          isBanned ? 'Account Banned' : 'Account Status',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
            color: isBanned ? Colors.red : Colors.green,
          ),
        ),
        content: Text(
          isBanned
              ? 'Your account has been banned. Please contact support for more information.'
              : 'Your account is in good standing.',
          style: GoogleFonts.poppins(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
