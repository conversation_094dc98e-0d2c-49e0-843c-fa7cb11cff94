import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

/// A service class to handle admin financial operations
class AdminFinancialService {
  // Singleton pattern
  static final AdminFinancialService _instance = AdminFinancialService._internal();
  factory AdminFinancialService() => _instance;
  AdminFinancialService._internal();

  // Firebase instances
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Get current user ID
  String? get _currentUserId => _auth.currentUser?.uid;

  // Check if user is admin
  Future<bool> isUserAdmin() async {
    if (_currentUserId == null) return false;

    try {
      final userDoc = await _firestore.collection('users').doc(_currentUserId).get();
      return userDoc.data()?['isAdmin'] == true;
    } catch (e) {
      debugPrint('Error checking if user is admin: $e');
      return false;
    }
  }

  // Add earnings to a user's balance (admin only)
  Future<void> addEarnings({
    required String userId,
    required double amount,
    required String reason,
    Map<String, dynamic>? metadata,
  }) async {
    // Check if current user is admin
    final isAdmin = await isUserAdmin();
    if (!isAdmin) {
      throw Exception('Only admins can add earnings');
    }

    // Validate amount
    if (amount <= 0) {
      throw Exception('Amount must be greater than zero');
    }

    try {
      // Use a transaction to ensure data consistency
      await _firestore.runTransaction(
        (transaction) async {
          // Get the current user data
          final userRef = _firestore.collection('users').doc(userId);
          final userDoc = await transaction.get(userRef);

          if (!userDoc.exists) {
            throw Exception('User not found');
          }

          final userData = userDoc.data()!;

          // Calculate new balance
          final currentBalance = (userData['balance'] as num?)?.toDouble() ?? 0.0;
          final totalEarnings = (userData['totalEarnings'] as num?)?.toDouble() ?? 0.0;
          final newBalance = currentBalance + amount;
          final newTotalEarnings = totalEarnings + amount;

          // Update user document with new balance
          transaction.update(userRef, {
            'balance': newBalance,
            'totalEarnings': newTotalEarnings,
            'updatedAt': FieldValue.serverTimestamp()
          });

          // Create a transaction record
          final transactionRef = _firestore.collection('transactions').doc();
          transaction.set(transactionRef, {
            'userId': userId,
            'type': 'credit',
            'amount': amount,
            'reason': reason,
            'metadata': metadata ?? {},
            'previousBalance': currentBalance,
            'newBalance': newBalance,
            'processedBy': _currentUserId,
            'createdAt': FieldValue.serverTimestamp()
          });
        },
      );
    } catch (e) {
      debugPrint('Error adding earnings: $e');
      rethrow;
    }
  }

  // Add earnings when a user's asset is purchased
  Future<void> addAssetPurchaseEarnings({
    required String sellerId,
    required String buyerId,
    required String assetId,
    required String assetName,
    required double amount,
    required double platformFeePercentage,
  }) async {
    // Check if current user is admin
    final isAdmin = await isUserAdmin();
    if (!isAdmin) {
      throw Exception('Only admins can add asset purchase earnings');
    }

    try {
      // Calculate platform fee
      final platformFee = amount * (platformFeePercentage / 100);
      final sellerEarnings = amount - platformFee;

      // Use a transaction to ensure data consistency
      await _firestore.runTransaction(
        (transaction) async {
          // Get the seller data
          final sellerRef = _firestore.collection('users').doc(sellerId);
          final sellerDoc = await transaction.get(sellerRef);

          if (!sellerDoc.exists) {
            throw Exception('Seller not found');
          }

          final sellerData = sellerDoc.data()!;

          // Calculate new balance for seller
          final sellerCurrentBalance = (sellerData['balance'] as num?)?.toDouble() ?? 0.0;
          final sellerTotalEarnings = (sellerData['totalEarnings'] as num?)?.toDouble() ?? 0.0;
          final sellerNewBalance = sellerCurrentBalance + sellerEarnings;
          final sellerNewTotalEarnings = sellerTotalEarnings + sellerEarnings;

          // Update seller document with new balance
          transaction.update(sellerRef, {
            'balance': sellerNewBalance,
            'totalEarnings': sellerNewTotalEarnings,
            'updatedAt': FieldValue.serverTimestamp(),
          });

          // Create a transaction record for seller
          final sellerTransactionRef = _firestore.collection('transactions').doc();
          transaction.set(sellerTransactionRef, {
            'userId': sellerId,
            'type': 'credit',
            'amount': sellerEarnings,
            'reason': 'Asset sale: $assetName',
            'metadata': {
              'assetId': assetId,
              'assetName': assetName,
              'buyerId': buyerId,
              'platformFee': platformFee,
              'platformFeePercentage': platformFeePercentage,
              'grossAmount': amount,
            },
            'previousBalance': sellerCurrentBalance,
            'newBalance': sellerNewBalance,
            'processedBy': _currentUserId,
            'createdAt': FieldValue.serverTimestamp(),
          });

          // Update asset purchase count
          final assetRef = _firestore.collection('assets').doc(assetId);
          final assetDoc = await transaction.get(assetRef);

          if (assetDoc.exists) {
            final assetData = assetDoc.data()!;
            final purchaseCount = (assetData['purchaseCount'] as num?)?.toInt() ?? 0;

            transaction.update(assetRef, {
              'purchaseCount': purchaseCount + 1,
              'updatedAt': FieldValue.serverTimestamp(),
            });
          }
        },
      );
    } catch (e) {
      debugPrint('Error adding asset purchase earnings: $e');
      rethrow;
    }
  }

  // Process payment for completed hire request
  Future<void> processHireRequestPayment({
    required String hireRequestId,
    required String developerId,
    required String clientId,
    required double budget,
    double platformFeePercentage = 20.0,
  }) async {
    // Check if current user is admin
    final isAdmin = await isUserAdmin();
    if (!isAdmin) {
      throw Exception('Only admins can process hire request payments');
    }

    try {
      // Calculate platform fee
      final platformFee = budget * (platformFeePercentage / 100);
      final developerEarnings = budget - platformFee;

      // Use a transaction to ensure atomicity
      await _firestore.runTransaction(
        (transaction) async {
          // Get the developer document
          final DocumentReference developerRef = _firestore.collection('users').doc(developerId);
          final DocumentSnapshot developerDoc = await transaction.get(developerRef);

          if (!developerDoc.exists) {
            throw Exception('Developer not found');
          }

          final developerData = developerDoc.data() as Map<String, dynamic>;

          // Calculate new balance for developer
          final developerCurrentBalance = (developerData['balance'] as num?)?.toDouble() ?? 0.0;
          final developerTotalEarnings = (developerData['totalEarnings'] as num?)?.toDouble() ?? 0.0;
          final developerNewBalance = developerCurrentBalance + developerEarnings;
          final developerNewTotalEarnings = developerTotalEarnings + developerEarnings;

          // Update developer document with new balance
          transaction.update(developerRef, {
            'balance': developerNewBalance,
            'totalEarnings': developerNewTotalEarnings,
            'updatedAt': FieldValue.serverTimestamp(),
          });

          // Create a transaction record for developer
          final developerTransactionRef = _firestore.collection('transactions').doc();
          transaction.set(developerTransactionRef, {
            'userId': developerId,
            'type': 'credit',
            'amount': developerEarnings,
            'reason': 'Project completion payment',
            'metadata': {
              'hireRequestId': hireRequestId,
              'clientId': clientId,
              'platformFee': platformFee,
              'platformFeePercentage': platformFeePercentage,
              'grossAmount': budget,
            },
            'previousBalance': developerCurrentBalance,
            'newBalance': developerNewBalance,
            'processedBy': _currentUserId,
            'createdAt': FieldValue.serverTimestamp(),
          });

          // Update hire request status
          final hireRequestRef = _firestore.collection('hire_requests').doc(hireRequestId);
          transaction.update(hireRequestRef, {
            'status': 'completed',
            'isCompleted': true,
            'completedAt': FieldValue.serverTimestamp(),
            'paymentProcessed': true,
            'paymentAmount': developerEarnings,
            'platformFee': platformFee,
            'updatedAt': FieldValue.serverTimestamp(),
          });
        },
      );
    } catch (e) {
      debugPrint('Error processing hire request payment: $e');
      rethrow;
    }
  }

  // Process payment for completed custom offer
  Future<void> processCustomOfferPayment({
    required String customOfferId,
    required String developerId,
    required String clientId,
    required double price,
    double platformFeePercentage = 20.0,
  }) async {
    // Check if current user is admin
    final isAdmin = await isUserAdmin();
    if (!isAdmin) {
      throw Exception('Only admins can process custom offer payments');
    }

    try {
      // Calculate platform fee
      final platformFee = price * (platformFeePercentage / 100);
      final developerEarnings = price - platformFee;

      // Use a transaction to ensure atomicity
      await _firestore.runTransaction(
        (transaction) async {
          // Get the developer document
          final DocumentReference developerRef = _firestore.collection('users').doc(developerId);
          final DocumentSnapshot developerDoc = await transaction.get(developerRef);

          if (!developerDoc.exists) {
            throw Exception('Developer not found');
          }

          final developerData = developerDoc.data() as Map<String, dynamic>;

          // Calculate new balance for developer
          final developerCurrentBalance = (developerData['balance'] as num?)?.toDouble() ?? 0.0;
          final developerTotalEarnings = (developerData['totalEarnings'] as num?)?.toDouble() ?? 0.0;
          final developerNewBalance = developerCurrentBalance + developerEarnings;
          final developerNewTotalEarnings = developerTotalEarnings + developerEarnings;

          // Update developer document with new balance
          transaction.update(developerRef, {
            'balance': developerNewBalance,
            'totalEarnings': developerNewTotalEarnings,
            'updatedAt': FieldValue.serverTimestamp(),
          });

          // Create a transaction record for developer
          final developerTransactionRef = _firestore.collection('transactions').doc();
          transaction.set(developerTransactionRef, {
            'userId': developerId,
            'type': 'credit',
            'amount': developerEarnings,
            'reason': 'Custom offer completion payment',
            'metadata': {
              'customOfferId': customOfferId,
              'clientId': clientId,
              'platformFee': platformFee,
              'platformFeePercentage': platformFeePercentage,
              'grossAmount': price,
            },
            'previousBalance': developerCurrentBalance,
            'newBalance': developerNewBalance,
            'processedBy': _currentUserId,
            'createdAt': FieldValue.serverTimestamp(),
          });

          // Update custom offer status
          final customOfferRef = _firestore.collection('custom_offers').doc(customOfferId);
          transaction.update(customOfferRef, {
            'status': 'completed',
            'isCompleted': true,
            'completedAt': FieldValue.serverTimestamp(),
            'paymentProcessed': true,
            'paymentAmount': developerEarnings,
            'platformFee': platformFee,
            'updatedAt': FieldValue.serverTimestamp(),
          });
        },
      );
    } catch (e) {
      debugPrint('Error processing custom offer payment: $e');
      rethrow;
    }
  }

  // Process refund for declined hire request
  Future<void> processHireRequestRefund({
    required String hireRequestId,
    required String clientId,
    required double refundAmount,
    required String reason,
  }) async {
    // Check if current user is admin
    final isAdmin = await isUserAdmin();
    if (!isAdmin) {
      throw Exception('Only admins can process hire request refunds');
    }

    try {
      // Use a transaction to ensure atomicity
      await _firestore.runTransaction(
        (transaction) async {
          // Get the client document
          final DocumentReference clientRef = _firestore.collection('users').doc(clientId);
          final DocumentSnapshot clientDoc = await transaction.get(clientRef);

          if (!clientDoc.exists) {
            throw Exception('Client not found');
          }

          final clientData = clientDoc.data() as Map<String, dynamic>;

          // Calculate new balance for client
          final clientCurrentBalance = (clientData['balance'] as num?)?.toDouble() ?? 0.0;
          final clientNewBalance = clientCurrentBalance + refundAmount;

          // Update client document with new balance
          transaction.update(clientRef, {
            'balance': clientNewBalance,
            'updatedAt': FieldValue.serverTimestamp(),
          });

          // Create a transaction record for client
          final clientTransactionRef = _firestore.collection('transactions').doc();
          transaction.set(clientTransactionRef, {
            'userId': clientId,
            'type': 'credit',
            'amount': refundAmount,
            'reason': 'Hire request refund: $reason',
            'metadata': {
              'hireRequestId': hireRequestId,
              'refundReason': reason,
              'refundAmount': refundAmount,
            },
            'previousBalance': clientCurrentBalance,
            'newBalance': clientNewBalance,
            'processedBy': _currentUserId,
            'createdAt': FieldValue.serverTimestamp(),
          });

          // Update hire request with refund information
          final hireRequestRef = _firestore.collection('hire_requests').doc(hireRequestId);
          transaction.update(hireRequestRef, {
            'isRefunded': true,
            'refundAmount': refundAmount,
            'refundedAt': FieldValue.serverTimestamp(),
            'refundReason': reason,
            'updatedAt': FieldValue.serverTimestamp(),
          });

          debugPrint('AdminFinancialService - processHireRequestRefund: Refunded $refundAmount to client $clientId for hire request $hireRequestId');
        },
      );
    } catch (e) {
      debugPrint('Error processing hire request refund: $e');
      rethrow;
    }
  }
}
