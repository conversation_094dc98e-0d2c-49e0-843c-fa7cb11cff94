import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:math' as math;
import 'dart:io';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;
import 'utils/file_compressor.dart';
import 'utils/image_compressor.dart';
import 'services/asset_upload_service.dart';
import 'services/file_service.dart';

class AssetUploadPage extends StatefulWidget {
  const AssetUploadPage({Key? key}) : super(key: key);

  @override
  State<AssetUploadPage> createState() => _AssetUploadPageState();
}

class _AssetUploadPageState extends State<AssetUploadPage> {
  @override
  void initState() {
    super.initState();
    _descriptionController = TextEditingController();
    _priceController = TextEditingController(text: _price.toString());
    _customTagController = TextEditingController();

    // Reset file upload status
    _fileUploaded = false;
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _priceController.dispose();
    _customTagController.dispose();
    super.dispose();
  }
  int _currentStep = 0;
  String _selectedType = 'UI Kit';
  String _selectedCategory = 'Mobile App';
  List<String> _selectedSubcategories = ['UI Components', 'Material Design'];
  bool _isPaid = false;
  double _price = 9.99;
  final double _platformFee = 0.2; // 20% platform fee
  String _title = '';
  String _description = '';
  List<String> _suggestedTags = [];
  final List<String> _selectedTags = [];
  bool _agreeToTerms = false;
  bool _isGeneratingPreview = false;
  final bool _isUploading = false; // Track if asset is being uploaded
  bool _fileUploaded = false; // Track if file is uploaded
  bool _isGeneratingDescription = false; // Track if description is being generated
  late TextEditingController _descriptionController; // Controller for description field

  // File upload tracking
  File? _assetFile; // The actual asset file
  List<File> _selectedFiles = []; // List of selected files before compression
  final String _assetFileName = ''; // Name of the asset file
  final String _assetFileSize = ''; // Size of the asset file
  final bool _isCompressing = false; // Track if files are being compressed

  // Thumbnail upload tracking
  File? _thumbnailFile; // The thumbnail file
  bool _thumbnailUploaded = false; // Track if thumbnail is uploaded

  // Screenshot upload tracking
  final List<File> _screenshotFiles = []; // List to store actual screenshot files
  final List<Map<String, dynamic>> _screenshots = []; // List to store screenshot information
  late TextEditingController _priceController; // Controller for price field
  late TextEditingController _customTagController; // Controller for custom tag input
  String? _priceError; // Error message for price input

  final List<String> _projectTypes = [
    'UI Kit',
    'Sketchware Project',
    'Kodular Project',
    'Android Studio Project',
    'VS Code Project',
    'Icon Pack',
    'Sketchware Blocks',
    'Sketchware Library',
    'UI/UX Design',
    'Flutter Template',
    'React Native Template',
    'Game Template',
    'Website Template',
  ];

  final List<String> _categories = [
    'Mobile App',
    'Web Development',
    'Game Development',
    'UI/UX Design',
    'Tools & Utilities',
    'Education',
    'Entertainment',
    'Productivity',
    'Social Media',
    'E-commerce',
  ];

  final Map<String, List<String>> _subcategories = {
    'Mobile App': ['UI Components', 'Material Design', 'iOS Design', 'Full App', 'App Template'],
    'Web Development': ['Landing Page', 'Dashboard', 'E-commerce', 'Portfolio', 'Blog'],
    'Game Development': ['2D Game', '3D Game', 'Puzzle', 'Arcade', 'Adventure'],
    'UI/UX Design': ['Mobile UI', 'Web UI', 'Dashboard', 'Icons', 'Illustrations'],
    'Tools & Utilities': ['Development Tools', 'Productivity', 'Utilities', 'Extensions'],
  };

  final Map<String, List<String>> _allowedFileTypes = {
    'Sketchware Project': ['.swb'],
    'Kodular Project': ['.aia'],
    'Android Studio Project': ['.zip', '.rar'],
    'VS Code Project': ['.zip', '.rar'],
    'UI Kit': ['.zip', '.rar', '.sketch', '.fig', '.xd', '.psd'],
    'Icon Pack': ['.zip', '.rar', '.svg', '.png'],
    'Sketchware Blocks': ['.sb'],
    'Sketchware Library': ['.slb'],
    'UI/UX Design': ['.sketch', '.fig', '.xd', '.psd', '.zip'],
    'Flutter Template': ['.zip', '.rar'],
    'React Native Template': ['.zip', '.rar'],
    'Game Template': ['.zip', '.rar'],
    'Website Template': ['.zip', '.rar', '.html'],
  };

  final List<String> _suggestedTagsList = [
    'Mobile App', 'UI Kit', 'Material Design', 'iOS Design', 'Android',
    'Flutter', 'React Native', 'Sketchware', 'Kodular', 'Game',
    'Education', 'Social Media', 'E-commerce', 'Dashboard', 'Admin Panel',
    'Login Screen', 'Profile UI', 'Navigation Drawer', 'Bottom Navigation',
    'Dark Mode', 'Light Mode', 'Minimalist', 'Modern', 'Colorful',
    'Responsive', 'Clean Code', 'Well Documented', 'Easy to Customize',
    'Ready to Use', 'Premium', 'Free', 'Beginner Friendly', 'Advanced',
  ];

  // Generate AI description based on title and project type
  void _generateAIDescription() {
    // Check if title is empty
    if (_title.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Please enter a title first to generate a description',
            style: GoogleFonts.poppins(),
          ),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    // Set loading state
    setState(() {
      _isGeneratingDescription = true;
    });

    // Simulate AI generating description based on title and project type
    Future.delayed(const Duration(seconds: 2), () {
      // Generate description based on title and project type
      final String generatedDescription = _generateDescriptionFromTitle(_title, _selectedType);

      // Update state with generated description
      if (mounted) {
        setState(() {
          _description = generatedDescription;
          _descriptionController.text = generatedDescription;
          _isGeneratingDescription = false;
        });

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'AI-generated description created!',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    });
  }

  // Helper method to generate description based on title and project type
  String _generateDescriptionFromTitle(String title, String projectType) {
    // In a real app, this would call an AI API
    // For this demo, we'll generate a template based on the project type

    final String cleanTitle = title.trim();
    final List<String> features = [
      'intuitive user interface',
      'clean and modern design',
      'responsive layout',
      'cross-platform compatibility',
      'customizable components',
      'well-documented code',
      'optimized performance',
      'accessibility features',
    ];

    // Shuffle features to get random ones each time
    features.shuffle();
    final selectedFeatures = features.take(3).join(', ');

    // Generate different templates based on project type
    switch (projectType) {
      case 'UI Kit':
        return '$cleanTitle is a comprehensive UI kit designed for modern applications. '
            'It includes a collection of reusable components that feature $selectedFeatures. '
            'This kit is perfect for developers looking to quickly implement a professional design system '
            'without starting from scratch. Each component is fully customizable to match your brand identity.';

      case 'Sketchware Project':
        return '$cleanTitle is a complete Sketchware project that demonstrates how to build mobile applications with ease. '
            'This project showcases $selectedFeatures, making it an excellent reference for both beginners and experienced developers. '
            'You can use this as a starting point for your own applications or study the implementation to learn best practices in Sketchware development.';

      case 'Kodular Project':
        return '$cleanTitle is a ready-to-use Kodular project that helps you create Android applications without traditional coding. '
            'This project features $selectedFeatures, allowing you to quickly deploy functional mobile apps. '
            'Perfect for entrepreneurs and developers who want to bring their ideas to life quickly without deep programming knowledge.';

      case 'Android Studio Project':
        return '$cleanTitle is a professionally structured Android Studio project built with modern development practices. '
            'It demonstrates $selectedFeatures, following the latest Android architecture guidelines. '
            'This project serves as an excellent foundation for building robust, scalable Android applications with clean code organization.';

      case 'VS Code Project':
        return '$cleanTitle is a well-organized VS Code project that showcases best practices for web development. '
            'With $selectedFeatures, this project provides a solid foundation for building modern web applications. '
            'The codebase is structured for maximum productivity and maintainability, making it ideal for teams and individual developers alike.';

      default:
        return '$cleanTitle is a high-quality $projectType that provides $selectedFeatures. '
            'This resource is designed to help developers save time and improve the quality of their projects. '
            'Whether you\'re a beginner or an experienced developer, you\'ll find this resource valuable for your development workflow.';
    }
  }

  void _generateSuggestedTags() {
    // Simulate AI generating tags based on title, description, and selected categories
    setState(() {
      _isGeneratingPreview = true;
    });

    Future.delayed(const Duration(seconds: 2), () {
      final random = math.Random();
      final suggestedCount = 5 + random.nextInt(5); // 5-9 tags
      final shuffled = List.of(_suggestedTagsList)..shuffle();

      setState(() {
        _suggestedTags = {
          _selectedType,
          _selectedCategory,
          ..._selectedSubcategories,
          ...shuffled.take(suggestedCount)
        }.toList(); // Remove duplicates

        _isGeneratingPreview = false;
      });
    });
  }

  // Pick asset file
  Future<void> _pickAssetFile() async {
    try {
      // For Sketchware Projects, don't filter by extension to avoid .bin conversion issues
      List<String> allowedExtensions = [];
      bool allowMultiple = true;

      if (_selectedType == 'Sketchware Project') {
        // For Sketchware Projects, don't filter by extension and only allow single file selection
        allowedExtensions = [];  // Empty list means accept all file types
        allowMultiple = false;   // Only allow single file selection
      } else {
        // For other project types, use the defined allowed extensions
        allowedExtensions = _allowedFileTypes[_selectedType] ?? ['.zip', '.rar'];
      }

      // Use our FileService to pick files
      final BuildContext currentContext = context;

      final files = await FileService.pickFiles(
        context: currentContext,
        allowedExtensions: allowedExtensions,
        allowMultiple: allowMultiple,
      );

      // Return if no files were picked
      if (files == null || files.isEmpty) {
        return;
      }

      // Store the selected files
      _selectedFiles = files;

      // Process the files (compress if needed)
      if (!mounted) return;

      final result = await FileService.processFiles(
        context: context,
        files: files,
      );

      // Return if processing failed
      if (result == null || !mounted) {
        return;
      }

      // Update state with the processed file
      setState(() {
        _assetFile = result['file'] as File;
        _fileUploaded = true;
      });
    } catch (e) {
      debugPrint('Error picking/processing file: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error processing file: $e',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  // Pick thumbnail
  Future<void> _pickThumbnail() async {
    try {
      final XFile? image = await ImagePicker().pickImage(
        source: ImageSource.gallery,
      );

      if (image != null) {
        // Show loading indicator
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Processing thumbnail...',
                    style: GoogleFonts.poppins(),
                  ),
                ],
              ),
              backgroundColor: Colors.blue,
              duration: const Duration(seconds: 1),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }

        final File originalFile = File(image.path);

        // Always compress thumbnails very aggressively to save storage
        final File imageFile = await ImageCompressor.compressImage(
          originalFile,
          quality: 40, // Even lower quality for thumbnails to achieve 5x compression
          minWidth: 800,
          minHeight: 600,
          aggressive: true, // Use aggressive compression for thumbnails
        );

        final String fileName = path.basename(imageFile.path);
        final int fileSize = await imageFile.length();
        final String formattedSize = FileCompressor.formatFileSize(fileSize);

        // Calculate compression ratio
        String compressionInfo = '';
        final double ratio = await ImageCompressor.getCompressionRatio(
          originalFile,
          imageFile,
        );
        compressionInfo = ' (${ratio.toStringAsFixed(1)}x smaller)';

        setState(() {
          _thumbnailFile = imageFile;
          _thumbnailUploaded = true;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Thumbnail added: $formattedSize$compressionInfo',
                style: GoogleFonts.poppins(),
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Error picking/compressing thumbnail: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error processing thumbnail: $e',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  // Pick screenshot
  Future<void> _pickScreenshot() async {
    try {
      final XFile? image = await ImagePicker().pickImage(
        source: ImageSource.gallery,
      );

      if (image != null) {
        // Show loading indicator
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Compressing image...',
                    style: GoogleFonts.poppins(),
                  ),
                ],
              ),
              backgroundColor: Colors.blue,
              duration: const Duration(seconds: 1),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }

        final File originalFile = File(image.path);

        // Always compress screenshots aggressively to save storage
        final File imageFile = await ImageCompressor.compressImage(
          originalFile,
          quality: 50, // Lower quality for screenshots to achieve better compression
          minWidth: 1080,
          minHeight: 1920,
          aggressive: true, // Use aggressive compression for screenshots
        );

        final String fileName = path.basename(imageFile.path);
        final int fileSize = await imageFile.length();
        final String formattedSize = FileCompressor.formatFileSize(fileSize);

        // Calculate compression ratio
        String compressionInfo = '';
        final double ratio = await ImageCompressor.getCompressionRatio(
          originalFile,
          imageFile,
        );
        compressionInfo = ' (${ratio.toStringAsFixed(1)}x smaller)';

        setState(() {
          _screenshotFiles.add(imageFile);
          _screenshots.add({
            'id': DateTime.now().millisecondsSinceEpoch.toString(),
            'name': fileName,
            'size': formattedSize,
            'timestamp': DateTime.now().toString(),
            'path': imageFile.path,
          });
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Screenshot added: $formattedSize$compressionInfo',
                style: GoogleFonts.poppins(),
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Error picking/compressing screenshot: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error processing screenshot: $e',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }







  double get _calculatedRevenue {
    return _price * (1 - _platformFee);
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Upload Asset',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 1,
        iconTheme: const IconThemeData(
          color: Colors.black87,
        ),
        actions: [
          TextButton.icon(
            onPressed: () {
              // Save draft logic
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Draft saved successfully',
                    style: GoogleFonts.poppins(),
                  ),
                  backgroundColor: Colors.green,
                  behavior: SnackBarBehavior.floating,
                ),
              );
            },
            icon: const Icon(Icons.save_outlined, size: 18),
            label: Text(
              'Save Draft',
              style: GoogleFonts.poppins(),
            ),
            style: TextButton.styleFrom(
              foregroundColor: Colors.black87,
            ),
          ),
        ],
      ),
      body: Stepper(
        type: MediaQuery.of(context).size.width > 600
            ? StepperType.horizontal
            : StepperType.vertical,
        physics: const ScrollPhysics(),
        currentStep: _currentStep,
        onStepTapped: (step) {
          // Validate current step before allowing navigation
          if (step > _currentStep) {
            // Trying to jump ahead - validate all steps in between
            for (int i = _currentStep; i < step; i++) {
              if (!_validateStep(i)) {
                // Show error message
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Please complete the current step before proceeding',
                      style: GoogleFonts.poppins(),
                    ),
                    backgroundColor: Colors.red,
                    behavior: SnackBarBehavior.floating,
                  ),
                );
                return;
              }
            }
          }

          // Allow navigation
          setState(() => _currentStep = step);
        },
        controlsBuilder: (context, details) {
          return Padding(
            padding: const EdgeInsets.only(top: 20),
            child: Row(
              children: [
                if (_currentStep > 0)
                  OutlinedButton(
                    onPressed: details.onStepCancel,
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.grey[800],
                      side: BorderSide(color: Colors.grey[300]!),
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                    child: Text(
                      'Back',
                      style: GoogleFonts.poppins(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: details.onStepContinue,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF6A11CB),
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  ),
                  child: Text(
                    _currentStep == 4 ? 'Submit' : 'Continue',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
        onStepContinue: () {
          // Validate current step before proceeding
          if (!_validateCurrentStep()) {
            return;
          }

          if (_currentStep < 4) {
            setState(() {
              _currentStep += 1;
              if (_currentStep == 3 && _suggestedTags.isEmpty) {
                _generateSuggestedTags();
              }
            });
          } else {
            // Submit form
            if (!_agreeToTerms) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Please agree to the terms and conditions'),
                  backgroundColor: Colors.red,
                  behavior: SnackBarBehavior.floating,
                ),
              );
              return;
            }

            // Submit logic would go here
            // Show success dialog and navigate to home
            showDialog(
              context: context,
              barrierDismissible: false,
              builder: (BuildContext dialogContext) {
                return AlertDialog(
                  backgroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.check_circle,
                        color: Colors.green,
                        size: 64,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Success!',
                        style: GoogleFonts.poppins(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Your asset has been submitted for review.',
                        textAlign: TextAlign.center,
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 24),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () async {
                            if (_assetFile == null) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'Please select a file to upload',
                                    style: GoogleFonts.poppins(),
                                  ),
                                  backgroundColor: Colors.red,
                                  behavior: SnackBarBehavior.floating,
                                ),
                              );
                              Navigator.of(dialogContext).pop();
                              return;
                            }

                            if (!_thumbnailUploaded || _thumbnailFile == null) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'Please upload a thumbnail image',
                                    style: GoogleFonts.poppins(),
                                  ),
                                  backgroundColor: Colors.red,
                                  behavior: SnackBarBehavior.floating,
                                ),
                              );
                              Navigator.of(dialogContext).pop();
                              return;
                            }

                            if (_screenshots.isEmpty) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'Please add at least one screenshot',
                                    style: GoogleFonts.poppins(),
                                  ),
                                  backgroundColor: Colors.red,
                                  behavior: SnackBarBehavior.floating,
                                ),
                              );
                              Navigator.of(dialogContext).pop();
                              return;
                            }

                            // Show loading indicator
                            Navigator.of(dialogContext).pop();
                            showDialog(
                              context: context,
                              barrierDismissible: false,
                              builder: (context) => AlertDialog(
                                content: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const CircularProgressIndicator(),
                                    const SizedBox(height: 16),
                                    Text(
                                      'Uploading asset...',
                                      style: GoogleFonts.poppins(),
                                    ),
                                  ],
                                ),
                              ),
                            );

                            try {
                              // Create asset upload service
                              final assetUploadService = AssetUploadService();

                              // Upload asset
                              final result = await assetUploadService.uploadAsset(
                                assetFile: _assetFile!,
                                thumbnailFile: _thumbnailFile!,
                                screenshotFiles: _screenshotFiles,
                                title: _title,
                                description: _description,
                                type: _selectedType,
                                category: _selectedCategory,
                                subcategories: _selectedSubcategories,
                                tags: _selectedTags,
                                price: double.tryParse(_priceController.text) ?? 0.0,
                                requiresAdminApproval: true, // Send to admin panel for approval
                              );

                              // Close loading dialog if still mounted
                              if (mounted) {
                                Navigator.pop(context);

                                if (result['success'] == true) {
                                  // Show success message
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        'Asset uploaded successfully! You can view it in your profile.',
                                        style: GoogleFonts.poppins(),
                                      ),
                                      backgroundColor: Colors.green,
                                      behavior: SnackBarBehavior.floating,
                                    ),
                                  );

                                  // Navigate to profile page to see the uploaded asset
                                  Navigator.pushReplacementNamed(context, '/profile');
                                } else {
                                  // Show error message
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        'Error uploading asset: ${result['error']}',
                                        style: GoogleFonts.poppins(),
                                      ),
                                      backgroundColor: Colors.red,
                                      behavior: SnackBarBehavior.floating,
                                    ),
                                  );
                                }
                              }
                            } catch (e) {
                              // Close loading dialog if still mounted
                              if (mounted) {
                                Navigator.pop(context);

                                // Show error message
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      'Error uploading asset: $e',
                                      style: GoogleFonts.poppins(),
                                    ),
                                    backgroundColor: Colors.red,
                                    behavior: SnackBarBehavior.floating,
                                  ),
                                );
                              }
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF6A11CB),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Text(
                            'Go to Home',
                            style: GoogleFonts.poppins(
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            );
          }
        },
        onStepCancel: () {
          if (_currentStep > 0) {
            setState(() {
              _currentStep -= 1;
            });
          }
        },
        steps: _buildSteps(),
      ),
    );
  }

  List<Step> _buildSteps() {
    return [
      // Step 1: Project Type Selection
      Step(
        title: Text(
          'Project Type',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.w600,
          ),
        ),
        content: _buildProjectTypeStep(),
        isActive: _currentStep >= 0,
        state: _currentStep > 0 ? StepState.complete : StepState.indexed,
      ),

      // Step 2: File Upload
      Step(
        title: Text(
          'Upload Files',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.w600,
          ),
        ),
        content: _buildFileUploadStep(),
        isActive: _currentStep >= 1,
        state: _currentStep > 1 ? StepState.complete : StepState.indexed,
      ),

      // Step 3: Details & Pricing
      Step(
        title: Text(
          'Details & Pricing',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.w600,
          ),
        ),
        content: _buildDetailsStep(),
        isActive: _currentStep >= 2,
        state: _currentStep > 2 ? StepState.complete : StepState.indexed,
      ),

      // Step 4: SEO & Tags
      Step(
        title: Text(
          'SEO & Tags',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.w600,
          ),
        ),
        content: _buildSeoStep(),
        isActive: _currentStep >= 3,
        state: _currentStep > 3 ? StepState.complete : StepState.indexed,
      ),

      // Step 5: Preview & Submit
      Step(
        title: Text(
          'Preview & Submit',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.w600,
          ),
        ),
        content: _buildPreviewStep(),
        isActive: _currentStep >= 4,
        state: _currentStep > 4 ? StepState.complete : StepState.indexed,
      ),
    ];
  }

  // Get icon for project type
  IconData _getIconForType(String type) {
    switch (type) {
      case 'UI Kit':
        return Icons.dashboard;
      case 'Sketchware Project':
        return Icons.phone_android;
      case 'Kodular Project':
        return Icons.apps;
      case 'Android Studio Project':
        return Icons.android;
      case 'VS Code Project':
        return Icons.code;
      case 'Icon Pack':
        return Icons.image;
      case 'Sketchware Blocks':
        return Icons.view_module;
      case 'Sketchware Library':
        return Icons.library_books;
      case 'UI/UX Design':
        return Icons.design_services;
      case 'Flutter Template':
        return Icons.flutter_dash;
      case 'React Native Template':
        return Icons.web;
      case 'Game Template':
        return Icons.sports_esports;
      case 'Website Template':
        return Icons.language;
      default:
        return Icons.file_present;
    }
  }

  Widget _buildPreviewStep() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
        Text(
          'Preview & Submit',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Review your project details and submit for review.',
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: Colors.black54,
          ),
        ),
        const SizedBox(height: 24),

        // Project preview card
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.grey[200]!),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(10),
                blurRadius: 10,
                spreadRadius: 0,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Preview header with project type and category
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFF6A11CB).withAlpha(10),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      _getIconForType(_selectedType),
                      color: const Color(0xFF6A11CB),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _selectedType,
                            style: GoogleFonts.poppins(
                              fontWeight: FontWeight.bold,
                              color: const Color(0xFF6A11CB),
                            ),
                          ),
                          Text(
                            _selectedCategory,
                            style: GoogleFonts.poppins(
                              fontSize: 13,
                              color: Colors.black54,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: _isPaid ? const Color(0xFF6A11CB).withAlpha(15) : Colors.green.withAlpha(15),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: _isPaid ? const Color(0xFF6A11CB).withAlpha(50) : Colors.green.withAlpha(50),
                        ),
                      ),
                      child: Text(
                        _isPaid ? '\$${_price.toStringAsFixed(2)}' : 'Free',
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.w600,
                          fontSize: 13,
                          color: _isPaid ? const Color(0xFF6A11CB) : Colors.green[700],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Project title and description
              Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _title.isEmpty ? 'My Awesome Project' : _title,
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _description.isEmpty
                          ? 'This is a placeholder description for your project. You should provide a detailed description to help users understand what your project does and how it can benefit them.'
                          : _description,
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.black87,
                        height: 1.5,
                      ),
                    ),

                    const SizedBox(height: 16),
                    const Divider(),
                    const SizedBox(height: 16),

                    // Thumbnail preview
                    if (_thumbnailFile != null) ...[
                      Text(
                        'Thumbnail',
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        height: 150,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey[300]!),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.file(
                            _thumbnailFile!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Center(
                                child: Icon(
                                  Icons.broken_image,
                                  size: 32,
                                  color: Colors.grey[400],
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                    ],

                    // File information
                    Row(
                      children: [
                        Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.insert_drive_file,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                _assetFile != null ? path.basename(_assetFile!.path) : 'project_file.${_allowedFileTypes[_selectedType]?.first.replaceAll('.', '') ?? 'zip'}',
                                style: GoogleFonts.poppins(
                                  fontWeight: FontWeight.w600,
                                  color: Colors.black87,
                                ),
                              ),
                              FutureBuilder<int>(
                                future: _assetFile != null ? _assetFile!.length() : Future.value(0),
                                builder: (context, snapshot) {
                                  final String fileSize = snapshot.hasData && snapshot.data! > 0
                                      ? FileCompressor.formatFileSize(snapshot.data!)
                                      : '';
                                  return Text(
                                    fileSize.isNotEmpty ? '$fileSize • Uploaded' : 'Uploaded',
                                    style: GoogleFonts.poppins(
                                      fontSize: 13,
                                      color: Colors.black54,
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Tags
                    if (_selectedTags.isNotEmpty) ...[
                      Text(
                        'Tags',
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: _selectedTags.map((tag) {
                          return Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: Colors.grey[100],
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              tag,
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                color: Colors.black87,
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ] else ...[
                      Text(
                        'No tags selected',
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.w600,
                          color: Colors.black54,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 32),

        // Terms and conditions
        Row(
          children: [
            Checkbox(
              value: _agreeToTerms,
              onChanged: (value) {
                setState(() {
                  _agreeToTerms = value ?? false;
                });
              },
              activeColor: const Color(0xFF6A11CB),
            ),
            Expanded(
              child: Text.rich(
                TextSpan(
                  text: 'I agree to the ',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                  children: [
                    TextSpan(
                      text: 'Terms of Service',
                      style: GoogleFonts.poppins(
                        color: const Color(0xFF6A11CB),
                        fontWeight: FontWeight.w600,
                      ),
                      // Add GestureDetector for terms of service
                    ),
                    const TextSpan(text: ' and '),
                    TextSpan(
                      text: 'Content Policy',
                      style: GoogleFonts.poppins(
                        color: const Color(0xFF6A11CB),
                        fontWeight: FontWeight.w600,
                      ),
                      // Add GestureDetector for content policy
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Review process info
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.amber.withAlpha(15),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.amber.withAlpha(50)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(
                    Icons.info_outline,
                    color: Colors.amber,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Review Process',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w600,
                      color: Colors.amber[800],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'Your project will be reviewed by our team to ensure it meets our quality standards and guidelines. This process typically takes 1-2 business days.',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.black87,
                  height: 1.5,
                ),
              ),
            ],
          ),
        ),
      ],
      ),
    );
  }

  Widget _buildSeoStep() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
        Text(
          'SEO & Tags',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Optimize your project for search engines and add relevant tags.',
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: Colors.black54,
          ),
        ),
        const SizedBox(height: 24),

        // AI SEO Suggestions
        Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[200]!),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(5),
                blurRadius: 10,
                spreadRadius: 0,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF6A11CB).withAlpha(15),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.auto_awesome,
                      color: Color(0xFF6A11CB),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'AI-Powered SEO Suggestions',
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        Text(
                          'Our AI analyzes your project to suggest optimal tags and keywords',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            color: Colors.black54,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // AI-generated tags
              if (_isGeneratingPreview) ...[
                Center(
                  child: Column(
                    children: [
                      const CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6A11CB)),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Analyzing your project...',
                        style: GoogleFonts.poppins(
                          color: Colors.black54,
                        ),
                      ),
                    ],
                  ),
                ),
              ] else if (_suggestedTags.isNotEmpty) ...[
                Text(
                  'Suggested Tags',
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 12),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: _suggestedTags.map((tag) {
                    final isSelected = _selectedTags.contains(tag);
                    return FilterChip(
                      label: Text(
                        tag,
                        style: GoogleFonts.poppins(
                          color: isSelected ? Colors.white : Colors.black87,
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                          fontSize: 13,
                        ),
                      ),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          if (selected) {
                            _selectedTags.add(tag);
                          } else {
                            _selectedTags.remove(tag);
                          }
                        });
                      },
                      backgroundColor: Colors.grey[100],
                      selectedColor: const Color(0xFF6A11CB),
                    );
                  }).toList(),
                ),
              ] else ...[
                Center(
                  child: Column(
                    children: [
                      ElevatedButton.icon(
                        onPressed: _generateSuggestedTags,
                        icon: const Icon(Icons.auto_awesome),
                        label: Text(
                          'Generate Tags',
                          style: GoogleFonts.poppins(),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF6A11CB),
                          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              const SizedBox(height: 24),

              // Custom tags
              Text(
                'Custom Tags',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      decoration: InputDecoration(
                        hintText: 'Add your own tag',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        suffixIcon: IconButton(
                          icon: const Icon(Icons.add),
                          onPressed: () {
                            // Get the current text from the field
                            final customTag = _customTagController.text.trim();
                            if (customTag.isNotEmpty) {
                              setState(() {
                                // Add the tag if it's not already in the list
                                if (!_selectedTags.contains(customTag)) {
                                  _selectedTags.add(customTag);
                                }
                                // Clear the text field
                                _customTagController.clear();
                              });
                            }
                          },
                        ),
                      ),
                      controller: _customTagController,
                      onSubmitted: (value) {
                        // Also add tag when user presses Enter
                        if (value.trim().isNotEmpty) {
                          setState(() {
                            if (!_selectedTags.contains(value.trim())) {
                              _selectedTags.add(value.trim());
                            }
                            _customTagController.clear();
                          });
                        }
                      },
                    ),
                  ),
                ],
              ),
              if (_selectedTags.isNotEmpty) ...[
                const SizedBox(height: 16),
                Text(
                  'Selected Tags (${_selectedTags.length})',
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 12),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: _selectedTags.map((tag) {
                    return Chip(
                      label: Text(
                        tag,
                        style: GoogleFonts.poppins(
                          fontSize: 13,
                        ),
                      ),
                      deleteIcon: const Icon(Icons.close, size: 16),
                      onDeleted: () {
                        setState(() {
                          _selectedTags.remove(tag);
                        });
                      },
                      backgroundColor: const Color(0xFF6A11CB).withAlpha(15),
                      deleteIconColor: const Color(0xFF6A11CB),
                    );
                  }).toList(),
                ),
              ],
            ],
          ),
        ),

        const SizedBox(height: 24),

        // SEO Tips
        Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.green.withAlpha(15),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.green.withAlpha(50)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'SEO Tips',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.green[800],
                ),
              ),
              const SizedBox(height: 16),
              _buildSeoTipItem(
                'Use descriptive titles',
                'Make your title clear and specific to help users find your project.',
                Icons.title,
              ),
              const SizedBox(height: 12),
              _buildSeoTipItem(
                'Add detailed descriptions',
                'Include key features, use cases, and technologies used in your project.',
                Icons.description,
              ),
              const SizedBox(height: 12),
              _buildSeoTipItem(
                'Choose relevant tags',
                'Select tags that accurately represent your project to improve discoverability.',
                Icons.label,
              ),
              const SizedBox(height: 12),
              _buildSeoTipItem(
                'Include screenshots',
                'Visual content helps users understand your project and increases engagement.',
                Icons.image,
              ),
            ],
          ),
        ),
      ],
      ),
    );
  }

  // Validate a specific step
  bool _validateStep(int step) {
    switch (step) {
      case 0:
        // Project type selection - always valid as we have a default selection
        return true;
      case 1:
        // File upload step - check if file is uploaded
        if (!_fileUploaded) {
          return false;
        }
        // Check if thumbnail is uploaded
        if (!_thumbnailUploaded) {
          return false;
        }
        // Also check if at least one screenshot is uploaded
        if (_screenshots.isEmpty) {
          return false;
        }
        return true;
      case 2:
        // Details & Pricing step - check if title and description are filled
        if (_title.trim().isEmpty) {
          return false;
        }
        if (_description.trim().isEmpty) {
          return false;
        }
        if (_isPaid && _price <= 0) {
          return false;
        }
        return true;
      case 3:
        // SEO & Tags step - check if at least one tag is selected
        return _selectedTags.isNotEmpty;
      case 4:
        // Preview & Submit step - check if terms are agreed
        return true; // We already check for terms agreement in the submit logic
      default:
        return true;
    }
  }

  // Add screenshot button
  Widget _buildAddScreenshotButton() {
    return InkWell(
      onTap: _showScreenshotSelectionDialog,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.grey[300]!,
            width: 2,
            style: BorderStyle.solid,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_photo_alternate_outlined,
              size: 32,
              color: Colors.grey[600],
            ),
            const SizedBox(height: 8),
            Text(
              'Add Screenshot',
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // Empty screenshot slot
  Widget _buildEmptyScreenshotSlot() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey[200]!,
          width: 1,
        ),
      ),
      child: Center(
        child: Icon(
          Icons.image_outlined,
          size: 24,
          color: Colors.grey[300],
        ),
      ),
    );
  }

  // Screenshot item with image preview
  Widget _buildScreenshotItem(Map<String, dynamic> screenshot) {
    final String imagePath = screenshot['path'] as String;
    final File imageFile = File(imagePath);

    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.grey[300]!,
              width: 1,
            ),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(11),
            child: imageFile.existsSync()
              ? Image.file(
                  imageFile,
                  fit: BoxFit.cover,
                  width: double.infinity,
                  height: double.infinity,
                  errorBuilder: (context, error, stackTrace) {
                    return Center(
                      child: Icon(
                        Icons.broken_image,
                        size: 32,
                        color: Colors.grey[400],
                      ),
                    );
                  },
                )
              : Center(
                  child: Icon(
                    Icons.image,
                    size: 32,
                    color: Colors.grey[400],
                  ),
                ),
          ),
        ),
        // Add a tap gesture to show a larger preview
        Positioned.fill(
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(11),
              onTap: () {
                // Show a larger preview dialog
                showDialog(
                  context: context,
                  builder: (context) => Dialog(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        AppBar(
                          title: Text(
                            'Screenshot Preview',
                            style: GoogleFonts.poppins(),
                          ),
                          backgroundColor: const Color(0xFF6A11CB),
                          leading: IconButton(
                            icon: const Icon(Icons.close),
                            onPressed: () => Navigator.pop(context),
                          ),
                        ),
                        Flexible(
                          child: Image.file(
                            imageFile,
                            fit: BoxFit.contain,
                            errorBuilder: (context, error, stackTrace) {
                              return Center(
                                child: Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Text(
                                    'Error loading image',
                                    style: GoogleFonts.poppins(),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Text(
                            'Size: ${screenshot['size']}',
                            style: GoogleFonts.poppins(),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ),
        Positioned(
          top: 8,
          right: 8,
          child: InkWell(
            onTap: () {
              setState(() {
                // Find the corresponding file in _screenshotFiles and remove it
                final int index = _screenshots.indexOf(screenshot);
                if (index >= 0 && index < _screenshotFiles.length) {
                  _screenshotFiles.removeAt(index);
                }
                _screenshots.remove(screenshot);
              });
            },
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.close,
                size: 12,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Show screenshot selection dialog
  void _showScreenshotSelectionDialog() {
    if (_screenshots.length >= 6) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'You can upload a maximum of 6 screenshots',
            style: GoogleFonts.poppins(),
          ),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    // Use image picker to select a screenshot
    _pickScreenshot();
  }

  // Validate current step before proceeding - with error messages
  bool _validateCurrentStep() {
    switch (_currentStep) {
      case 0:
        // Project type selection - always valid as we have a default selection
        return true;
      case 1:
        // File upload step - check if file is uploaded
        if (!_fileUploaded) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Please upload a file before continuing',
                style: GoogleFonts.poppins(),
              ),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
          return false;
        }
        // Check if thumbnail is uploaded
        if (!_thumbnailUploaded) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Please upload a thumbnail before continuing',
                style: GoogleFonts.poppins(),
              ),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
          return false;
        }
        // Check if at least one screenshot is uploaded
        if (_screenshots.isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Please upload at least one screenshot before continuing',
                style: GoogleFonts.poppins(),
              ),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
          return false;
        }
        return true;
      case 2:
        // Details & Pricing step - check if title and description are filled
        if (_title.trim().isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Please enter a project title',
                style: GoogleFonts.poppins(),
              ),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
          return false;
        }
        if (_description.trim().isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Please enter a project description',
                style: GoogleFonts.poppins(),
              ),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
          return false;
        }
        if (_isPaid && _price <= 0) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Please enter a valid price',
                style: GoogleFonts.poppins(),
              ),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
          return false;
        }
        return true;
      case 3:
        // SEO & Tags step - check if at least one tag is selected
        if (_selectedTags.isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Please select at least one tag',
                style: GoogleFonts.poppins(),
              ),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
          return false;
        }
        return true;
      case 4:
        // Preview & Submit step - check if terms are agreed
        return true; // We already check for terms agreement in the submit logic
      default:
        return true;
    }
  }

  // Show file selection dialog
  void _showFileSelectionDialog() {
    // Use file picker to select a file
    _pickAssetFile();
  }

  Widget _buildSeoTipItem(String title, String description, IconData icon) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: Colors.green[700],
            size: 20,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: GoogleFonts.poppins(
                  fontSize: 13,
                  color: Colors.black54,
                  height: 1.4,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDetailsStep() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Project Details & Pricing',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Provide information about your project and set pricing options.',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.black54,
            ),
          ),
          const SizedBox(height: 24),

        // Basic details section
        Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[200]!),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(5),
                blurRadius: 10,
                spreadRadius: 0,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Basic Information',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 16),

              // Title field
              TextField(
                decoration: InputDecoration(
                  labelText: 'Project Title *',
                  hintText: 'Enter a descriptive title',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  floatingLabelBehavior: FloatingLabelBehavior.always,
                  helperText: 'Required',
                  helperStyle: GoogleFonts.poppins(
                    color: Colors.red[400],
                    fontSize: 12,
                  ),
                ),
                onChanged: (value) {
                  setState(() {
                    _title = value;
                  });
                },
              ),
              const SizedBox(height: 16),

              // Category selection
              Text(
                'Category',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _categories.map((category) {
                  final isSelected = _selectedCategory == category;
                  return ChoiceChip(
                    label: Text(
                      category,
                      style: GoogleFonts.poppins(
                        color: isSelected ? Colors.white : Colors.black87,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                    selected: isSelected,
                    onSelected: (selected) {
                      if (selected) {
                        setState(() {
                          _selectedCategory = category;
                          _selectedSubcategories = [];
                        });
                      }
                    },
                    backgroundColor: Colors.grey[100],
                    selectedColor: const Color(0xFF6A11CB),
                  );
                }).toList(),
              ),
              const SizedBox(height: 16),

              // Subcategory selection
              if (_subcategories.containsKey(_selectedCategory)) ...[
                Text(
                  'Subcategories',
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: (_subcategories[_selectedCategory] ?? []).map((subcategory) {
                    final isSelected = _selectedSubcategories.contains(subcategory);
                    return FilterChip(
                      label: Text(
                        subcategory,
                        style: GoogleFonts.poppins(
                          color: isSelected ? Colors.white : Colors.black87,
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                        ),
                      ),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          if (selected) {
                            _selectedSubcategories.add(subcategory);
                          } else {
                            _selectedSubcategories.remove(subcategory);
                          }
                        });
                      },
                      backgroundColor: Colors.grey[100],
                      selectedColor: const Color(0xFF6A11CB),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 16),
              ],

              // Description field with AI generation
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          'Description *',
                          style: GoogleFonts.poppins(
                            fontWeight: FontWeight.w600,
                            color: Colors.black87,
                          ),
                        ),
                      ),
                      ElevatedButton.icon(
                        onPressed: _isGeneratingDescription
                            ? null
                            : _generateAIDescription,
                        icon: _isGeneratingDescription
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                            : const Icon(Icons.auto_awesome, size: 16),
                        label: Text(
                          _isGeneratingDescription ? 'Generating...' : 'AI Generate',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Colors.white,
                          ),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF6A11CB),
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    maxLines: 5,
                    controller: _descriptionController,
                    decoration: InputDecoration(
                      hintText: 'Describe your project in detail',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      helperText: 'Required - Provide a detailed description of your project',
                      helperStyle: GoogleFonts.poppins(
                        color: Colors.red[400],
                        fontSize: 12,
                      ),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _description = value;
                      });
                    },
                  ),
                  if (_isGeneratingDescription)
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Row(
                        children: [
                          const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6A11CB)),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'AI is generating a description based on your title...',
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                fontStyle: FontStyle.italic,
                                color: Colors.black54,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),

        const SizedBox(height: 24),

        // Pricing section
        Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[200]!),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(5),
                blurRadius: 10,
                spreadRadius: 0,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Pricing Options',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 16),

              // Free or paid selection
              Row(
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () {
                        setState(() {
                          _isPaid = false;
                        });
                      },
                      borderRadius: BorderRadius.circular(8),
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: !_isPaid ? const Color(0xFF4CAF50).withAlpha(15) : Colors.grey[100],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: !_isPaid ? const Color(0xFF4CAF50) : Colors.grey[300]!,
                            width: !_isPaid ? 2 : 1,
                          ),
                        ),
                        child: Column(
                          children: [
                            Icon(
                              Icons.card_giftcard,
                              size: 32,
                              color: !_isPaid ? const Color(0xFF4CAF50) : Colors.grey[400],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Free',
                              style: GoogleFonts.poppins(
                                fontWeight: FontWeight.w600,
                                color: !_isPaid ? const Color(0xFF4CAF50) : Colors.black87,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Available to everyone',
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                color: Colors.black54,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: InkWell(
                      onTap: () {
                        setState(() {
                          _isPaid = true;
                        });
                      },
                      borderRadius: BorderRadius.circular(8),
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: _isPaid ? const Color(0xFF6A11CB).withAlpha(15) : Colors.grey[100],
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: _isPaid ? const Color(0xFF6A11CB) : Colors.grey[300]!,
                            width: _isPaid ? 2 : 1,
                          ),
                        ),
                        child: Column(
                          children: [
                            Icon(
                              Icons.attach_money,
                              size: 32,
                              color: _isPaid ? const Color(0xFF6A11CB) : Colors.grey[400],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Paid',
                              style: GoogleFonts.poppins(
                                fontWeight: FontWeight.w600,
                                color: _isPaid ? const Color(0xFF6A11CB) : Colors.black87,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Set your own price',
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                color: Colors.black54,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),

              // Price input (only visible if paid is selected)
              if (_isPaid) ...[
                const SizedBox(height: 24),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Price (USD)',
                            style: GoogleFonts.poppins(
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 8),
                          TextField(
                            keyboardType: const TextInputType.numberWithOptions(decimal: true),
                            decoration: InputDecoration(
                              prefixText: '\$ ',
                              hintText: '0.00',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              // Make the input field more prominent
                              filled: true,
                              fillColor: Colors.grey[50],
                              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                              // Add error text if non-numeric input is detected
                              errorText: _priceError,
                            ),
                            // Use input formatters to restrict input to numbers only
                            inputFormatters: [
                              // Allow only digits and at most one decimal point
                              TextInputFormatter.withFunction((oldValue, newValue) {
                                // Check if the new value is empty or matches our pattern
                                if (newValue.text.isEmpty ||
                                    RegExp(r'^\d*\.?\d*$').hasMatch(newValue.text)) {
                                  // Clear any error
                                  if (_priceError != null) {
                                    setState(() {
                                      _priceError = null;
                                    });
                                  }
                                  return newValue;
                                }

                                // Show error message
                                setState(() {
                                  _priceError = 'Please enter numbers only';
                                });

                                // Return the old value to prevent the change
                                return oldValue;
                              }),
                            ],
                            onChanged: (value) {
                              if (value.isEmpty) {
                                setState(() {
                                  _price = 0.0;
                                });
                                return;
                              }

                              // Parse the value to a double
                              final parsedValue = double.tryParse(value);
                              if (parsedValue != null) {
                                setState(() {
                                  _price = parsedValue;
                                });
                              }
                            },
                            controller: _priceController,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Your Revenue',
                            style: GoogleFonts.poppins(
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.grey[100],
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.grey[300]!),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '\$${_calculatedRevenue.toStringAsFixed(2)}',
                                  style: GoogleFonts.poppins(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: const Color(0xFF4CAF50),
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'After ${(_platformFee * 100).toInt()}% platform fee',
                                  style: GoogleFonts.poppins(
                                    fontSize: 12,
                                    color: Colors.black54,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.withAlpha(15),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.info_outline,
                        color: Colors.blue,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'Verified and Elite users enjoy reduced platform fees.',
                          style: GoogleFonts.poppins(
                            fontSize: 13,
                            color: Colors.blue[800],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
      ),
    );
  }

  Widget _buildFileUploadStep() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
        Text(
          'Upload Files',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Upload your project files. Make sure they are in the correct format.',
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: Colors.black54,
          ),
        ),
        const SizedBox(height: 24),

        // File upload area
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.grey[300]!,
              width: 2,
              style: BorderStyle.solid,
            ),
          ),
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.cloud_upload_outlined,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                'Drag & Drop Files Here',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'or',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.black54,
                ),
              ),
              const SizedBox(height: 8),
              ElevatedButton.icon(
                onPressed: () {
                  // Show file selection dialog
                  _showFileSelectionDialog();
                },
                icon: const Icon(Icons.file_present),
                label: Text(
                  'Browse Files',
                  style: GoogleFonts.poppins(
                    color: Colors.white,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF6A11CB),
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'Allowed file types: ${_allowedFileTypes[_selectedType]?.join(', ')}',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.black54,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 24),

        // Uploaded files list - only show when a file is uploaded
        if (_fileUploaded)
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[200]!),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(5),
                  blurRadius: 10,
                  spreadRadius: 0,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(12),
                      topRight: Radius.circular(12),
                    ),
                  ),
                  child: Row(
                    children: [
                      Text(
                        'Uploaded Files',
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        '1 file',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          color: Colors.black54,
                        ),
                      ),
                    ],
                  ),
                ),
                const Divider(height: 1),

                // Sample uploaded file
                ListTile(
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: const Color(0xFF6A11CB).withAlpha(15),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.insert_drive_file,
                      color: Color(0xFF6A11CB),
                    ),
                  ),
                  title: Text(
                    _assetFile != null ? path.basename(_assetFile!.path) : 'project_file.${_allowedFileTypes[_selectedType]?.first.replaceAll('.', '') ?? 'zip'}',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  subtitle: _assetFile != null
                      ? FutureBuilder<int>(
                          future: _assetFile!.length(),
                          builder: (context, snapshot) {
                            final String fileSize = snapshot.hasData
                                ? FileCompressor.formatFileSize(snapshot.data!)
                                : '';
                            return Text(
                              fileSize.isNotEmpty ? '$fileSize • Uploaded just now' : 'Uploaded just now',
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                color: Colors.black54,
                              ),
                            );
                          },
                        )
                      : Text(
                          'Uploaded just now',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: Colors.black54,
                          ),
                        ),
                  trailing: IconButton(
                    icon: const Icon(Icons.delete_outline, color: Colors.red),
                    onPressed: () {
                      // Delete file logic
                      setState(() {
                        _fileUploaded = false;
                      });
                    },
                  ),
                ),
              ],
            ),
          ),

        const SizedBox(height: 24),

        // Thumbnail upload section
        Row(
          children: [
            Text(
              'Upload Thumbnail',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.red.withAlpha(30),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: Colors.red.withAlpha(50)),
              ),
              child: Text(
                'Required',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.red,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          'Add a thumbnail image that will be displayed on the home page in asset cards.',
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: Colors.black54,
          ),
        ),
        const SizedBox(height: 16),

        // Thumbnail upload status
        Row(
          children: [
            Icon(
              _thumbnailUploaded ? Icons.check_circle : Icons.info_outline,
              size: 16,
              color: _thumbnailUploaded ? Colors.green : Colors.amber,
            ),
            const SizedBox(width: 8),
            Text(
              _thumbnailUploaded
                ? 'Thumbnail uploaded'
                : 'No thumbnail uploaded yet',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: _thumbnailUploaded ? Colors.green : Colors.amber,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Thumbnail upload container
        InkWell(
          onTap: _pickThumbnail,
          child: Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.grey[300]!,
                width: 1,
              ),
            ),
            child: _thumbnailFile != null
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Image.file(
                    _thumbnailFile!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.broken_image,
                              size: 48,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Error loading image',
                              style: GoogleFonts.poppins(
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                )
              : Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.add_photo_alternate_outlined,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Click to upload thumbnail',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Recommended size: 800x600 pixels',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
          ),
        ),

        const SizedBox(height: 24),

        // Screenshots upload section
        Row(
          children: [
            Text(
              'Upload Screenshots',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.red.withAlpha(30),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: Colors.red.withAlpha(50)),
              ),
              child: Text(
                'Required',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.red,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          'Add screenshots to showcase your asset. These will be displayed in the asset detail page. At least one screenshot is required.',
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: Colors.black54,
          ),
        ),
        const SizedBox(height: 16),

        // Screenshot count indicator
        Row(
          children: [
            Icon(
              _screenshots.isNotEmpty ? Icons.check_circle : Icons.info_outline,
              size: 16,
              color: _screenshots.isNotEmpty ? Colors.green : Colors.amber,
            ),
            const SizedBox(width: 8),
            Text(
              _screenshots.isNotEmpty
                ? '${_screenshots.length} screenshot${_screenshots.length > 1 ? 's' : ''} uploaded'
                : 'No screenshots uploaded yet',
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: _screenshots.isNotEmpty ? Colors.green : Colors.amber,
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Screenshot upload grid
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 3,
          mainAxisSpacing: 12,
          crossAxisSpacing: 12,
          childAspectRatio: 1,
          children: [
            // Add screenshot button
            _buildAddScreenshotButton(),

            // Existing screenshots
            ..._screenshots.map((screenshot) => _buildScreenshotItem(screenshot)).toList(),

            // Empty placeholder slots (up to 6 total)
            ...List.generate(
              math.max(0, 5 - _screenshots.length),
              (index) => _buildEmptyScreenshotSlot(),
            ),
          ],
        ),

        const SizedBox(height: 24),

        // File requirements info
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.amber.withAlpha(15),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.amber.withAlpha(50)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(
                    Icons.lightbulb_outline,
                    color: Colors.amber,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'File Requirements',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w600,
                      color: Colors.amber[800],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                '• Maximum file size: 50MB\n'
                '• Files must be properly formatted and not corrupted\n'
                '• Include all necessary resources for the project to work\n'
                '• Do not include sensitive or personal information',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.black87,
                  height: 1.5,
                ),
              ),
            ],
          ),
        ),
      ],
      ),
    );
  }

  Widget _buildProjectTypeStep() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
        Text(
          'Select Project Type',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Choose the type of project you want to upload. This will determine the allowed file types and categories.',
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: Colors.black54,
          ),
        ),
        const SizedBox(height: 24),

        // Project type grid
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 3,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
          ),
          itemCount: _projectTypes.length,
          itemBuilder: (context, index) {
            final type = _projectTypes[index];
            final isSelected = _selectedType == type;

            return InkWell(
              onTap: () {
                setState(() {
                  _selectedType = type;
                });
              },
              borderRadius: BorderRadius.circular(12),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isSelected ? const Color(0xFF6A11CB) : Colors.grey[300]!,
                    width: isSelected ? 2 : 1,
                  ),
                  color: isSelected ? const Color(0xFF6A11CB).withAlpha(25) : Colors.white,
                ),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                child: Row(
                  children: [
                    Icon(
                      _getIconForType(type),
                      color: isSelected ? const Color(0xFF6A11CB) : Colors.grey[600],
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        type,
                        style: GoogleFonts.poppins(
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                          color: isSelected ? const Color(0xFF6A11CB) : Colors.black87,
                        ),
                      ),
                    ),
                    if (isSelected)
                      const Icon(
                        Icons.check_circle,
                        color: Color(0xFF6A11CB),
                        size: 20,
                      ),
                  ],
                ),
              ),
            );
          },
        ),

        const SizedBox(height: 24),

        // Allowed file types info
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.blue.withAlpha(25),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.blue.withAlpha(75)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(
                    Icons.info_outline,
                    color: Colors.blue,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Allowed File Types',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w600,
                      color: Colors.blue,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'For $_selectedType, you can upload: ${_allowedFileTypes[_selectedType]?.join(', ')}',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.black87,
                ),
              ),
              if (_selectedType == 'Sketchware Project')
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Text(
                    'Note: Only .swb files are accepted for Sketchware projects to ensure they are usable by other developers.',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontStyle: FontStyle.italic,
                      color: Colors.black54,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ],
      ),
    );
  }
}