buildscript {
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.google.gms:google-services:4.4.1")
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

val newBuildDir: Directory = rootProject.layout.buildDirectory.dir("../../build").get()
rootProject.layout.buildDirectory.value(newBuildDir)

subprojects {
    val newSubprojectBuildDir: Directory = newBuildDir.dir(project.name)
    project.layout.buildDirectory.value(newSubprojectBuildDir)
}
subprojects {
    project.evaluationDependsOn(":app")

    // Apply patch for file_picker plugin
    if (project.name == "file_picker") {
        project.afterEvaluate {
            project.apply(from = "${rootProject.projectDir}/file_picker_patch.gradle")

            // Apply the namespace to the android extension if it exists
            val androidExt = project.extensions.findByName("android")
            if (androidExt != null && androidExt is com.android.build.gradle.LibraryExtension) {
                androidExt.namespace = "com.mr.flutter.plugin.filepicker"
            }
        }
    }
}

tasks.register<Delete>("clean") {
    delete(rootProject.layout.buildDirectory)
}
