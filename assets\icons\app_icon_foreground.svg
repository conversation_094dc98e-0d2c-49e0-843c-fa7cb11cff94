<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="1024" height="1024" viewBox="0 0 1024 1024" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Inner circle -->
  <circle cx="512" cy="512" r="320" fill="url(#paint1_linear)"/>
  
  <!-- A letter shape -->
  <path d="M512 250L712 750H612L512 550L412 750H312L512 250Z" fill="#1A237E"/>
  
  <!-- Crossbar of A -->
  <line x1="412" y1="650" x2="612" y2="650" stroke="white" stroke-width="20"/>
  
  <!-- Connection dots -->
  <circle cx="412" cy="450" r="30" fill="#1565C0" filter="url(#shadow1)"/>
  <circle cx="612" cy="550" r="30" fill="#C62828" filter="url(#shadow2)"/>
  <circle cx="462" cy="650" r="30" fill="#2E7D32" filter="url(#shadow3)"/>
  
  <!-- Definitions -->
  <defs>
    <!-- Inner circle gradient -->
    <linearGradient id="paint1_linear" x1="192" y1="192" x2="832" y2="832" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#FFFFFF" stop-opacity="0.9"/>
      <stop offset="1" stop-color="#FFFFFF" stop-opacity="0.3"/>
    </linearGradient>
    
    <!-- Shadows for dots -->
    <filter id="shadow1" x="372" y="410" width="80" height="80" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="5"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.082 0 0 0 0 0.396 0 0 0 0 0.753 0 0 0 0.6 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="shadow2" x="572" y="510" width="80" height="80" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="5"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.776 0 0 0 0 0.157 0 0 0 0 0.157 0 0 0 0.6 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
    
    <filter id="shadow3" x="422" y="610" width="80" height="80" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
      <feFlood flood-opacity="0" result="BackgroundImageFix"/>
      <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/>
      <feOffset/>
      <feGaussianBlur stdDeviation="5"/>
      <feColorMatrix type="matrix" values="0 0 0 0 0.18 0 0 0 0 0.49 0 0 0 0 0.196 0 0 0 0.6 0"/>
      <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
      <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/>
    </filter>
  </defs>
</svg>
