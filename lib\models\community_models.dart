import 'package:flutter/material.dart';

class Post {
  final String id;
  final User author;
  final String content;
  final String? imageUrl;
  final DateTime timestamp;
  final int likes;
  final int comments;
  final String? projectTitle;
  final String? projectDescription;

  Post({
    required this.id,
    required this.author,
    required this.content,
    this.imageUrl,
    required this.timestamp,
    required this.likes,
    required this.comments,
    this.projectTitle,
    this.projectDescription,
  });
}

class User {
  final String id;
  final String name;
  final String avatarUrl;
  final String? badge;
  final bool isOnline;
  final String? role;

  User({
    required this.id,
    required this.name,
    required this.avatarUrl,
    this.badge,
    this.isOnline = false,
    this.role,
  });
}

class ForumThread {
  final String id;
  final String title;
  final String description;
  final User author;
  final List<String> tags;
  final int replies;
  final int views;
  final DateTime lastActivityTime;

  ForumThread({
    required this.id,
    required this.title,
    required this.description,
    required this.author,
    required this.tags,
    required this.replies,
    required this.views,
    required this.lastActivityTime,
  });
}

class Group {
  final String id;
  final String name;
  final String description;
  final int memberCount;
  final int newPosts;
  final String iconName;
  final Color iconColor;
  final Color iconBgColor;

  Group({
    required this.id,
    required this.name,
    required this.description,
    required this.memberCount,
    required this.newPosts,
    required this.iconName,
    required this.iconColor,
    required this.iconBgColor,
  });
}

class Challenge {
  final String id;
  final String title;
  final String description;
  final String category;
  final double prizePool;
  final int participants;
  final int daysRemaining;
  final List<String> tags;

  Challenge({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.prizePool,
    required this.participants,
    required this.daysRemaining,
    required this.tags,
  });
}

class Mentor {
  final User user;
  final String expertise;
  final double rating;
  final int reviewCount;
  final List<String> skills;
  final bool isTopMentor;

  Mentor({
    required this.user,
    required this.expertise,
    required this.rating,
    required this.reviewCount,
    required this.skills,
    this.isTopMentor = false,
  });
}