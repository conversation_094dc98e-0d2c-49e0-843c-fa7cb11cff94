import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Service to handle post deletion with cascade delete of related data
class PostDeletionService {
  static final PostDeletionService _instance = PostDeletionService._internal();
  factory PostDeletionService() => _instance;
  PostDeletionService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  String? get currentUserId => _auth.currentUser?.uid;

  /// Delete a post and all related data (likes, comments, saves, etc.)
  Future<Map<String, dynamic>> deletePost(String postId, {bool isAdmin = false}) async {
    try {
      debugPrint('PostDeletionService: Starting deletion of post $postId');

      // Check if user has permission to delete
      if (!isAdmin) {
        final postDoc = await _firestore.collection('posts').doc(postId).get();
        if (!postDoc.exists) {
          return {'success': false, 'error': 'Post not found'};
        }

        final postData = postDoc.data() as Map<String, dynamic>;
        final authorId = postData['authorId'] ?? '';
        
        if (authorId != currentUserId) {
          return {'success': false, 'error': 'Not authorized to delete this post'};
        }
      }

      // Use batch operations for atomic deletion
      final batch = _firestore.batch();
      int deletedItems = 0;

      // 1. Delete the main post
      batch.delete(_firestore.collection('posts').doc(postId));
      deletedItems++;

      // 2. Delete all post likes (using new collection structure)
      final postLikesQuery = await _firestore
          .collection('post_likes')
          .where('postId', isEqualTo: postId)
          .get();
      
      for (final doc in postLikesQuery.docs) {
        batch.delete(doc.reference);
        deletedItems++;
      }

      // 3. Delete all comments for this post
      final commentsQuery = await _firestore
          .collection('comments')
          .where('postId', isEqualTo: postId)
          .get();

      final commentIds = <String>[];
      for (final doc in commentsQuery.docs) {
        batch.delete(doc.reference);
        commentIds.add(doc.id);
        deletedItems++;
      }

      // 4. Delete all comment likes for the comments we're deleting
      for (final commentId in commentIds) {
        final commentLikesQuery = await _firestore
            .collection('comment_likes')
            .where('commentId', isEqualTo: commentId)
            .get();
        
        for (final doc in commentLikesQuery.docs) {
          batch.delete(doc.reference);
          deletedItems++;
        }
      }

      // 5. Delete all comment replies for the comments we're deleting
      for (final commentId in commentIds) {
        final repliesQuery = await _firestore
            .collection('comment_replies')
            .where('commentId', isEqualTo: commentId)
            .get();
        
        for (final doc in repliesQuery.docs) {
          batch.delete(doc.reference);
          deletedItems++;
        }
      }

      // 6. Delete all saved posts references
      final savedPostsQuery = await _firestore
          .collection('saved_posts')
          .where('postId', isEqualTo: postId)
          .get();
      
      for (final doc in savedPostsQuery.docs) {
        batch.delete(doc.reference);
        deletedItems++;
      }

      // 7. Delete any reported posts references
      final reportedPostsQuery = await _firestore
          .collection('reported_posts')
          .where('postId', isEqualTo: postId)
          .get();
      
      for (final doc in reportedPostsQuery.docs) {
        batch.delete(doc.reference);
        deletedItems++;
      }

      // 8. Delete legacy likes (if any exist in old structure)
      final legacyLikesQuery = await _firestore
          .collection('likes')
          .where('postId', isEqualTo: postId)
          .get();
      
      for (final doc in legacyLikesQuery.docs) {
        batch.delete(doc.reference);
        deletedItems++;
      }

      // Commit all deletions atomically
      await batch.commit();

      debugPrint('PostDeletionService: Successfully deleted post $postId and $deletedItems related items');
      
      return {
        'success': true,
        'deletedItems': deletedItems,
        'message': 'Post and all related data deleted successfully'
      };

    } catch (e) {
      debugPrint('PostDeletionService: Error deleting post $postId: $e');
      return {
        'success': false,
        'error': 'Failed to delete post: ${e.toString()}'
      };
    }
  }

  /// Delete multiple posts (for admin bulk operations)
  Future<Map<String, dynamic>> deletePosts(List<String> postIds) async {
    try {
      int successCount = 0;
      int failureCount = 0;
      final List<String> errors = [];

      for (final postId in postIds) {
        final result = await deletePost(postId, isAdmin: true);
        if (result['success'] == true) {
          successCount++;
        } else {
          failureCount++;
          errors.add('Post $postId: ${result['error']}');
        }
      }

      return {
        'success': failureCount == 0,
        'successCount': successCount,
        'failureCount': failureCount,
        'errors': errors,
        'message': 'Deleted $successCount posts successfully${failureCount > 0 ? ', $failureCount failed' : ''}'
      };

    } catch (e) {
      debugPrint('PostDeletionService: Error in bulk deletion: $e');
      return {
        'success': false,
        'error': 'Bulk deletion failed: ${e.toString()}'
      };
    }
  }

  /// Clean up orphaned data (comments without posts, likes without posts, etc.)
  Future<Map<String, dynamic>> cleanupOrphanedData() async {
    try {
      debugPrint('PostDeletionService: Starting orphaned data cleanup');
      
      int cleanedItems = 0;
      final batch = _firestore.batch();

      // Get all existing post IDs
      final postsSnapshot = await _firestore.collection('posts').get();
      final existingPostIds = postsSnapshot.docs.map((doc) => doc.id).toSet();

      // Clean up orphaned comments
      final commentsSnapshot = await _firestore.collection('comments').get();
      for (final doc in commentsSnapshot.docs) {
        final data = doc.data();
        final postId = data['postId'] as String?;
        if (postId != null && !existingPostIds.contains(postId)) {
          batch.delete(doc.reference);
          cleanedItems++;
        }
      }

      // Clean up orphaned post likes
      final postLikesSnapshot = await _firestore.collection('post_likes').get();
      for (final doc in postLikesSnapshot.docs) {
        final data = doc.data();
        final postId = data['postId'] as String?;
        if (postId != null && !existingPostIds.contains(postId)) {
          batch.delete(doc.reference);
          cleanedItems++;
        }
      }

      // Clean up orphaned saved posts
      final savedPostsSnapshot = await _firestore.collection('saved_posts').get();
      for (final doc in savedPostsSnapshot.docs) {
        final data = doc.data();
        final postId = data['postId'] as String?;
        if (postId != null && !existingPostIds.contains(postId)) {
          batch.delete(doc.reference);
          cleanedItems++;
        }
      }

      // Clean up orphaned legacy likes
      final legacyLikesSnapshot = await _firestore.collection('likes').get();
      for (final doc in legacyLikesSnapshot.docs) {
        final data = doc.data();
        final postId = data['postId'] as String?;
        if (postId != null && !existingPostIds.contains(postId)) {
          batch.delete(doc.reference);
          cleanedItems++;
        }
      }

      // Commit cleanup
      if (cleanedItems > 0) {
        await batch.commit();
      }

      debugPrint('PostDeletionService: Cleaned up $cleanedItems orphaned items');
      
      return {
        'success': true,
        'cleanedItems': cleanedItems,
        'message': 'Cleaned up $cleanedItems orphaned items'
      };

    } catch (e) {
      debugPrint('PostDeletionService: Error in cleanup: $e');
      return {
        'success': false,
        'error': 'Cleanup failed: ${e.toString()}'
      };
    }
  }

  /// Get statistics about data integrity
  Future<Map<String, dynamic>> getDataIntegrityStats() async {
    try {
      final postsCount = (await _firestore.collection('posts').get()).docs.length;
      final commentsCount = (await _firestore.collection('comments').get()).docs.length;
      final postLikesCount = (await _firestore.collection('post_likes').get()).docs.length;
      final commentLikesCount = (await _firestore.collection('comment_likes').get()).docs.length;
      final savedPostsCount = (await _firestore.collection('saved_posts').get()).docs.length;

      return {
        'success': true,
        'stats': {
          'posts': postsCount,
          'comments': commentsCount,
          'postLikes': postLikesCount,
          'commentLikes': commentLikesCount,
          'savedPosts': savedPostsCount,
        }
      };
    } catch (e) {
      return {
        'success': false,
        'error': 'Failed to get stats: ${e.toString()}'
      };
    }
  }
}
