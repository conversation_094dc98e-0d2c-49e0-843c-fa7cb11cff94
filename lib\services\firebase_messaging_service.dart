import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// A service class to handle Firebase Cloud Messaging (FCM) for push notifications
class FirebaseMessagingService {
  // Singleton pattern
  static final FirebaseMessagingService _instance = FirebaseMessagingService._internal();
  factory FirebaseMessagingService() => _instance;
  FirebaseMessagingService._internal();

  // Firebase instances
  final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Initialize the service
  Future<void> initialize() async {
    try {
      // Request permission on iOS and web
      if (!kIsWeb && (Platform.isIOS || Platform.isMacOS)) {
        await _messaging.requestPermission(
          alert: true,
          badge: true,
          sound: true,
          provisional: false,
        );
      }

      // Handle FCM token
      await _setupFCMToken();

      // Set up message handlers
      _setupMessageHandlers();

      debugPrint('Firebase Messaging Service initialized successfully');
    } catch (e) {
      debugPrint('Error initializing Firebase Messaging Service: $e');
    }
  }

  // Set up FCM token
  Future<void> _setupFCMToken() async {
    // Get the token
    String? token = await _messaging.getToken();

    if (token != null) {
      debugPrint('FCM Token: $token');

      // Save token to shared preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('fcm_token', token);

      // Save token to Firestore if user is logged in
      await _saveTokenToFirestore(token);

      // Listen for token refreshes
      _messaging.onTokenRefresh.listen((newToken) async {
        debugPrint('FCM Token refreshed: $newToken');

        // Update token in shared preferences
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('fcm_token', newToken);

        // Update token in Firestore
        await _saveTokenToFirestore(newToken);
      });
    }
  }

  // Save FCM token to Firestore
  Future<void> _saveTokenToFirestore(String token) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser != null) {
        // Save token to user document
        await _firestore.collection('users').doc(currentUser.uid).update({
          'fcmTokens': FieldValue.arrayUnion([token]),
          'lastTokenUpdate': FieldValue.serverTimestamp(),
        });

        // Also save to a separate tokens collection for easier querying
        await _firestore.collection('fcm_tokens').doc(currentUser.uid).set({
          'userId': currentUser.uid,
          'email': currentUser.email,
          'tokens': FieldValue.arrayUnion([token]),
          'lastUpdated': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));
      }
    } catch (e) {
      debugPrint('Error saving FCM token to Firestore: $e');
    }
  }

  // Set up message handlers
  void _setupMessageHandlers() {
    // Handle messages when app is in foreground
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      debugPrint('Got a message whilst in the foreground!');
      debugPrint('Message data: ${message.data}');

      if (message.notification != null) {
        debugPrint('Message also contained a notification: ${message.notification}');

        // Show local notification
        _showLocalNotification(message);
      }
    });

    // Handle messages when app is opened from terminated state
    FirebaseMessaging.instance.getInitialMessage().then((RemoteMessage? message) {
      if (message != null) {
        debugPrint('App opened from terminated state with message: ${message.data}');
        _handleNotificationTap(jsonEncode(message.data));
      }
    });

    // Handle messages when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      debugPrint('App opened from background state with message: ${message.data}');
      _handleNotificationTap(jsonEncode(message.data));
    });
  }

  // Show local notification
  void _showLocalNotification(RemoteMessage message) {
    RemoteNotification? notification = message.notification;
    AndroidNotification? android = message.notification?.android;

    // Log notification for debugging
    if (notification != null) {
      debugPrint('Showing notification: ${notification.title}');
      debugPrint('Notification body: ${notification.body}');
      debugPrint('Notification data: ${message.data}');
    }

    // Note: We're not showing local notifications due to compatibility issues
    // The system will show the notification automatically on Android and iOS
  }

  // Handle notification tap
  void _handleNotificationTap(String? payload) {
    if (payload != null) {
      try {
        final data = jsonDecode(payload) as Map<String, dynamic>;
        debugPrint('Notification tapped with data: $data');

        // Handle different notification types
        final String type = data['type'] ?? 'default';

        switch (type) {
          case 'warning':
            // Handle warning notification
            break;
          case 'account_status':
            // Handle account status notification
            break;
          case 'asset_approval':
            // Handle asset approval notification
            break;
          case 'message':
            // Handle message notification
            break;
          default:
            // Default handling
            break;
        }
      } catch (e) {
        debugPrint('Error handling notification tap: $e');
      }
    }
  }

  // Subscribe to a topic
  Future<void> subscribeToTopic(String topic) async {
    await _messaging.subscribeToTopic(topic);
    debugPrint('Subscribed to topic: $topic');
  }

  // Unsubscribe from a topic
  Future<void> unsubscribeFromTopic(String topic) async {
    await _messaging.unsubscribeFromTopic(topic);
    debugPrint('Unsubscribed from topic: $topic');
  }
}
