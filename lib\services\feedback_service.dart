import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/feedback_model.dart';
import 'notification_service.dart';

class FeedbackService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final NotificationService _notificationService = NotificationService();

  // Collection reference
  CollectionReference get _feedbackCollection =>
      _firestore.collection('feedback');

  // Submit feedback
  Future<void> submitFeedback({
    required String message,
    String? category,
    int? rating,
  }) async {
    try {
      // Get current user
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Get user data from Firestore
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      final userData = userDoc.data() ?? {};

      // Create feedback data
      final feedbackData = {
        'userId': user.uid,
        'userName': userData['displayName'] ?? user.displayName ?? 'Anonymous',
        'userEmail': userData['email'] ?? user.email ?? '',
        'userPhotoUrl': userData['photoURL'] ?? user.photoURL ?? '',
        'message': message,
        'timestamp': FieldValue.serverTimestamp(),
        'isRead': false,
        'category': category,
        'rating': rating,
      };

      // Add to Firestore
      final docRef = await _feedbackCollection.add(feedbackData);

      // Send notification to admins
      final adminSnapshot = await _firestore.collection('users')
          .where('isAdmin', isEqualTo: true)
          .get();

      for (final adminDoc in adminSnapshot.docs) {
        final adminId = adminDoc.id;
        await _notificationService.sendNotification(
          userId: adminId,
          title: 'New Feedback',
          body: 'A new feedback has been submitted by ${userData['displayName'] ?? user.displayName ?? 'a user'}',
          type: 'feedback',
          data: {
            'feedbackId': docRef.id,
          },
        );
      }

      // Send confirmation notification to user
      await _notificationService.sendNotification(
        userId: user.uid,
        title: 'Feedback Received',
        body: 'Thank you for your feedback! We will review it shortly.',
        type: 'feedback_confirmation',
        data: {
          'feedbackId': docRef.id,
        },
      );
    } catch (e) {
      rethrow;
    }
  }

  // Get all feedback (for admin)
  Stream<List<FeedbackModel>> getAllFeedback() {
    return _feedbackCollection
        .orderBy('timestamp', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => FeedbackModel.fromFirestore(doc))
          .toList();
    });
  }

  // Get limited feedback with pagination (for admin)
  Stream<List<FeedbackModel>> getLimitedFeedback(int limit) {
    return _feedbackCollection
        .orderBy('timestamp', descending: true)
        .limit(limit)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => FeedbackModel.fromFirestore(doc))
          .toList();
    });
  }

  // Mark feedback as read
  Future<void> markAsRead(String feedbackId) async {
    try {
      await _feedbackCollection.doc(feedbackId).update({
        'isRead': true,
      });
    } catch (e) {
      rethrow;
    }
  }

  // Delete feedback
  Future<void> deleteFeedback(String feedbackId) async {
    try {
      await _feedbackCollection.doc(feedbackId).delete();
    } catch (e) {
      rethrow;
    }
  }

  // Get unread feedback count
  Stream<int> getUnreadFeedbackCount() {
    return _feedbackCollection
        .where('isRead', isEqualTo: false)
        .snapshots()
        .map((snapshot) => snapshot.docs.length);
  }
}
