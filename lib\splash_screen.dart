import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_animate/flutter_animate.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  bool _showLoadingText = false;
  bool _showTagline = false;

  // Define vibrant gradient colors from community page
  final List<List<Color>> gradients = [
    [const Color(0xFF6A11CB), const Color(0xFF2575FC)], // Purple to Blue
    [const Color(0xFFFF416C), const Color(0xFFFF4B2B)], // Pink to Orange
    [const Color(0xFF00C9FF), const Color(0xFF92FE9D)], // Blue to Green
    [const Color(0xFFFF8008), const Color(0xFFFFC837)], // Orange to Yellow
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1), // Reduced animation time
    )..repeat();

    // Show loading text after a shorter delay
    Timer(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _showLoadingText = true;
        });
      }
    });

    // Show tagline after a shorter delay
    Timer(const Duration(milliseconds: 700), () {
      if (mounted) {
        setState(() {
          _showTagline = true;
        });
      }
    });

    // Don't navigate automatically - let the parent widget handle navigation
    // This prevents navigation conflicts with the main.dart navigation logic
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Animated gradient background
          AnimatedGradientBackground(gradients: gradients),

          // Content
          SafeArea(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Logo
                  AnimatedLogo(animationController: _animationController),
                  const SizedBox(height: 40),

                  // App name with gradient - using const where possible
                  ShaderMask(
                    shaderCallback: (bounds) => const LinearGradient(
                      colors: [Colors.white, Colors.white],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ).createShader(bounds),
                    child: Text(
                      'ASATU',
                      style: GoogleFonts.poppins(
                        fontSize: 48,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        letterSpacing: 4,
                      ),
                    ),
                  ).animate().fadeIn(duration: 400.ms, curve: Curves.easeOut),

                  // Tagline
                  if (_showTagline)
                    Container(
                      margin: const EdgeInsets.only(top: 16),
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: Text(
                        'Developer Community & Marketplace',
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                        ),
                      ),
                    ).animate().fadeIn(duration: 400.ms).slideY(
                      begin: 0.2,
                      end: 0,
                      duration: 500.ms,
                      curve: Curves.easeOutQuad,
                    ),

                  const SizedBox(height: 60),

                  // Loading indicator
                  if (_showLoadingText)
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white.withOpacity(0.8)),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Loading...',
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            color: Colors.white.withOpacity(0.8),
                          ),
                        ),
                      ],
                    ).animate().fadeIn(duration: 400.ms),
                ],
              ),
            ),
          ),

          // Version info
          Positioned(
            bottom: 24,
            left: 0,
            right: 0,
            child: Center(
              child: Text(
                'Version 1.0.0',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: Colors.white.withOpacity(0.6),
                ),
              ),
            ),
          ).animate().fadeIn(duration: 600.ms, delay: 1200.ms),
        ],
      ),
    );
  }
}

// Animated gradient background
class AnimatedGradientBackground extends StatefulWidget {
  final List<List<Color>> gradients;

  const AnimatedGradientBackground({
    Key? key,
    required this.gradients,
  }) : super(key: key);

  @override
  State<AnimatedGradientBackground> createState() => _AnimatedGradientBackgroundState();
}

class _AnimatedGradientBackgroundState extends State<AnimatedGradientBackground> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  int _currentIndex = 0;
  int _nextIndex = 1;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 3), // Reduced animation time
      vsync: this,
    )..repeat(reverse: true);

    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );

    // Use a more efficient approach to update indices
    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        setState(() {
          _currentIndex = (_currentIndex + 1) % widget.gradients.length;
          _nextIndex = (_currentIndex + 1) % widget.gradients.length;
        });
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color.lerp(
                  widget.gradients[_currentIndex][0],
                  widget.gradients[_nextIndex][0],
                  _animation.value,
                )!,
                Color.lerp(
                  widget.gradients[_currentIndex][1],
                  widget.gradients[_nextIndex][1],
                  _animation.value,
                )!,
              ],
            ),
          ),
        );
      },
    );
  }
}

// Animated logo
class AnimatedLogo extends StatelessWidget {
  final AnimationController animationController;

  const AnimatedLogo({
    Key? key,
    required this.animationController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: animationController,
      builder: (context, child) {
        return Container(
          width: 140,
          height: 140,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.white.withAlpha(38), // 0.15 * 255 = ~38
            boxShadow: [
              BoxShadow(
                color: Colors.white.withAlpha(51), // 0.2 * 255 = ~51
                blurRadius: 20,
                spreadRadius: 5,
              ),
            ],
          ),
          child: Center(
            child: Stack(
              alignment: Alignment.center,
              children: [
                // Outer rotating ring
                Transform.rotate(
                  angle: animationController.value * 2 * math.pi,
                  child: Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white.withAlpha(204), // 0.8 * 255 = ~204
                        width: 2,
                      ),
                    ),
                  ),
                ),

                // Inner circle with gradient
                Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: [
                        Colors.white.withAlpha(230), // 0.9 * 255 = ~230
                        Colors.white.withAlpha(77),  // 0.3 * 255 = ~77
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                ),

                // Logo elements
                Stack(
                  alignment: Alignment.center,
                  children: [
                    // "A" shape
                    Transform.translate(
                      offset: const Offset(0, -2),
                      child: CustomPaint(
                        size: const Size(50, 50),
                        painter: ALetterPainter(),
                      ),
                    ),

                    // Dots representing connection points
                    Positioned(
                      top: 25,
                      left: 25,
                      child: _buildDot(Colors.blue.shade700, 6),
                    ),
                    Positioned(
                      top: 40,
                      right: 25,
                      child: _buildDot(Colors.red.shade700, 6),
                    ),
                    Positioned(
                      bottom: 25,
                      left: 35,
                      child: _buildDot(Colors.green.shade700, 6),
                    ),

                    // Connecting lines that pulse with the animation
                    CustomPaint(
                      size: const Size(80, 80),
                      painter: ConnectionLinesPainter(
                        progress: animationController.value,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    ).animate().scale(
      begin: const Offset(0.5, 0.5),
      end: const Offset(1.0, 1.0),
      duration: 500.ms, // Reduced animation time
      curve: Curves.easeOutBack, // More efficient curve than elasticOut
    );
  }

  Widget _buildDot(Color color, double size) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: color.withAlpha(153), // 0.6 * 255 = ~153
            blurRadius: 6,
            spreadRadius: 1,
          ),
        ],
      ),
    );
  }
}

// Custom painter for the "A" letter shape
class ALetterPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.indigo.shade900
      ..style = PaintingStyle.fill
      ..strokeWidth = 3.0;

    final path = Path();

    // Draw "A" shape
    path.moveTo(size.width * 0.5, 0); // Top point
    path.lineTo(size.width, size.height); // Bottom right
    path.lineTo(size.width * 0.7, size.height); // Indent bottom right
    path.lineTo(size.width * 0.5, size.height * 0.6); // Middle right
    path.lineTo(size.width * 0.3, size.height); // Middle left
    path.lineTo(0, size.height); // Bottom left
    path.close();

    // Draw the shape
    canvas.drawPath(path, paint);

    // Add a horizontal line for the crossbar of "A"
    final crossbarPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    canvas.drawLine(
      Offset(size.width * 0.25, size.height * 0.7),
      Offset(size.width * 0.75, size.height * 0.7),
      crossbarPaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Custom painter for the connecting lines between dots
class ConnectionLinesPainter extends CustomPainter {
  final double progress;

  ConnectionLinesPainter({required this.progress});

  @override
  void paint(Canvas canvas, Size size) {
    // Calculate alpha value based on animation progress (0.5-1.0 range)
    final alpha = (128 + 127 * math.sin(progress * math.pi)).toInt(); // 0.5-1.0 * 255

    final paint = Paint()
      ..color = Colors.white.withAlpha(alpha)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    // Draw connecting lines between the dots
    canvas.drawLine(
      Offset(size.width * 0.3, size.height * 0.3),
      Offset(size.width * 0.7, size.height * 0.5),
      paint,
    );

    canvas.drawLine(
      Offset(size.width * 0.3, size.height * 0.3),
      Offset(size.width * 0.4, size.height * 0.7),
      paint,
    );

    canvas.drawLine(
      Offset(size.width * 0.7, size.height * 0.5),
      Offset(size.width * 0.4, size.height * 0.7),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant ConnectionLinesPainter oldDelegate) =>
    oldDelegate.progress != progress;
}
