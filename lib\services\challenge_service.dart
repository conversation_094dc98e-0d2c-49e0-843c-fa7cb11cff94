import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'dart:io';
import '../models/challenge_model.dart';
import 'notification_service.dart';

class ChallengeService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final NotificationService _notificationService = NotificationService();

  // Get current user ID
  String? get currentUserId => _auth.currentUser?.uid;

  // Collection references
  CollectionReference get _challengesCollection => _firestore.collection('challenges');
  CollectionReference get _submissionsCollection => _firestore.collection('challenge_submissions');

  // Create a new challenge
  Future<String> createChallenge({
    required String title,
    required String description,
    required String category,
    required double prizeAmount,
    required DateTime deadline,
    required List<String> requirements,
    required List<String> tags,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final userId = currentUserId;
      if (userId == null) throw Exception('User not authenticated');

      // Get user data
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) throw Exception('User not found');

      final userData = userDoc.data()!;
      final walletBalance = (userData['balance'] as num?)?.toDouble() ?? 0.0;

      // Check if user has sufficient balance
      if (walletBalance < prizeAmount) {
        throw Exception('INSUFFICIENT_BALANCE|You need \$${prizeAmount.toStringAsFixed(2)} but only have \$${walletBalance.toStringAsFixed(2)}');
      }

      // Create challenge document
      final challengeData = ChallengeModel(
        id: '',
        title: title,
        description: description,
        category: category,
        creatorId: userId,
        creatorName: userData['displayName'] ?? 'Unknown User',
        creatorAvatar: userData['photoURL'] ?? '',
        prizeAmount: prizeAmount,
        status: 'active',
        createdAt: DateTime.now(),
        deadline: deadline,
        requirements: requirements,
        tags: tags,
        additionalData: additionalData,
      );

      // Use batch to ensure atomicity
      final batch = _firestore.batch();

      // Add challenge
      final challengeRef = _challengesCollection.doc();
      batch.set(challengeRef, challengeData.toFirestore());

      // Deduct prize amount from user's wallet
      final userRef = _firestore.collection('users').doc(userId);
      batch.update(userRef, {
        'balance': FieldValue.increment(-prizeAmount),
        'pendingChallenges': FieldValue.increment(prizeAmount),
      });

      // Create transaction record
      final transactionRef = _firestore.collection('transactions').doc();
      batch.set(transactionRef, {
        'userId': userId,
        'type': 'challenge_creation',
        'amount': -prizeAmount,
        'description': 'Challenge creation: $title',
        'challengeId': challengeRef.id,
        'status': 'completed',
        'createdAt': FieldValue.serverTimestamp(),
      });

      await batch.commit();

      // Send notification to creator about successful challenge creation
      final newBalance = walletBalance - prizeAmount;
      await _notificationService.sendNotification(
        userId: userId,
        title: 'Challenge Created Successfully!',
        body: 'Your challenge "$title" has been created. \$${prizeAmount.toStringAsFixed(2)} deducted from wallet. New balance: \$${newBalance.toStringAsFixed(2)}',
        type: 'challenge_created',
        data: {
          'challengeId': challengeRef.id,
          'prizeAmount': prizeAmount,
          'deductedAmount': prizeAmount,
          'newBalance': newBalance,
        },
      );

      debugPrint('Challenge created successfully: ${challengeRef.id}');
      return challengeRef.id;
    } catch (e) {
      debugPrint('Error creating challenge: $e');
      rethrow;
    }
  }

  // Get all active challenges (without orderBy to avoid Firebase index requirements)
  Stream<List<ChallengeModel>> getActiveChallenges() {
    return _challengesCollection
        .where('status', isEqualTo: 'active')
        .snapshots()
        .map((snapshot) {
          final challenges = snapshot.docs
              .map((doc) => ChallengeModel.fromFirestore(doc))
              .toList();

          // Sort by createdAt in memory to avoid Firebase index
          challenges.sort((a, b) => b.createdAt.compareTo(a.createdAt));
          return challenges;
        });
  }

  // Get challenges by category (without orderBy to avoid Firebase index requirements)
  Stream<List<ChallengeModel>> getChallengesByCategory(String category) {
    return _challengesCollection
        .where('status', isEqualTo: 'active')
        .where('category', isEqualTo: category)
        .snapshots()
        .map((snapshot) {
          final challenges = snapshot.docs
              .map((doc) => ChallengeModel.fromFirestore(doc))
              .toList();

          // Sort by createdAt in memory to avoid Firebase index
          challenges.sort((a, b) => b.createdAt.compareTo(a.createdAt));
          return challenges;
        });
  }

  // Get user's created challenges (without orderBy to avoid Firebase index requirements)
  Stream<List<ChallengeModel>> getUserCreatedChallenges(String userId) {
    return _challengesCollection
        .where('creatorId', isEqualTo: userId)
        .snapshots()
        .map((snapshot) {
          final challenges = snapshot.docs
              .map((doc) => ChallengeModel.fromFirestore(doc))
              .toList();

          // Sort by createdAt in memory to avoid Firebase index
          challenges.sort((a, b) => b.createdAt.compareTo(a.createdAt));
          return challenges;
        });
  }

  // Get challenge by ID
  Future<ChallengeModel?> getChallengeById(String challengeId) async {
    try {
      final doc = await _challengesCollection.doc(challengeId).get();
      if (doc.exists) {
        return ChallengeModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting challenge: $e');
      return null;
    }
  }

  // Submit to challenge
  Future<String> submitToChallenge({
    required String challengeId,
    required String title,
    required String description,
    List<File>? attachmentFiles,
    List<File>? imageFiles,
    String? codeUrl,
    String? demoUrl,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      final userId = currentUserId;
      if (userId == null) throw Exception('User not authenticated');

      // Check if challenge exists and is active
      final challenge = await getChallengeById(challengeId);
      if (challenge == null) throw Exception('Challenge not found');
      if (!challenge.isActive) throw Exception('Challenge is not active');

      // Prevent challenge creator from submitting to their own challenge
      if (challenge.creatorId == userId) {
        throw Exception('Challenge creators cannot submit to their own challenges');
      }

      // Check if user already submitted
      final existingSubmission = await _submissionsCollection
          .where('challengeId', isEqualTo: challengeId)
          .where('participantId', isEqualTo: userId)
          .get();

      if (existingSubmission.docs.isNotEmpty) {
        throw Exception('You have already submitted to this challenge');
      }

      // Get user data
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (!userDoc.exists) throw Exception('User not found');
      final userData = userDoc.data()!;

      // Upload files
      final attachmentUrls = <String>[];
      final imageUrls = <String>[];

      if (attachmentFiles != null) {
        for (final file in attachmentFiles) {
          final url = await _uploadFile(file, 'challenge_attachments/$challengeId/$userId');
          attachmentUrls.add(url);
        }
      }

      if (imageFiles != null) {
        for (final file in imageFiles) {
          final url = await _uploadFile(file, 'challenge_images/$challengeId/$userId');
          imageUrls.add(url);
        }
      }

      // Create submission
      final submissionData = ChallengeSubmissionModel(
        id: '',
        challengeId: challengeId,
        participantId: userId,
        participantName: userData['displayName'] ?? 'Unknown User',
        participantAvatar: userData['photoURL'] ?? '',
        title: title,
        description: description,
        attachmentUrls: attachmentUrls,
        imageUrls: imageUrls,
        codeUrl: codeUrl,
        demoUrl: demoUrl,
        submittedAt: DateTime.now(),
        voterIds: [],
        voteCount: 0,
        additionalData: additionalData,
      );

      // Use batch for atomicity
      final batch = _firestore.batch();

      // Add submission
      final submissionRef = _submissionsCollection.doc();
      batch.set(submissionRef, submissionData.toFirestore());

      // Update challenge submission count
      final challengeRef = _challengesCollection.doc(challengeId);
      batch.update(challengeRef, {
        'submissionCount': FieldValue.increment(1),
      });

      await batch.commit();

      // Send notification to challenge creator about new submission
      await _notificationService.sendNotification(
        userId: challenge.creatorId,
        title: 'New Challenge Submission',
        body: '${userData['displayName']} submitted "$title" to your challenge: ${challenge.title}',
        type: 'challenge_submission',
        data: {
          'challengeId': challengeId,
          'submissionId': submissionRef.id,
          'participantId': userId,
          'submissionTitle': title,
        },
      );

      // Send notification to participant about successful submission
      await _notificationService.sendNotification(
        userId: userId,
        title: 'Submission Successful!',
        body: 'Your submission "$title" has been added to challenge: ${challenge.title}. Winner will be selected by the challenge creator.',
        type: 'challenge_joined',
        data: {
          'challengeId': challengeId,
          'submissionId': submissionRef.id,
          'challengeTitle': challenge.title,
          'submissionTitle': title,
        },
      );

      debugPrint('Challenge submission created successfully: ${submissionRef.id}');
      return submissionRef.id;
    } catch (e) {
      debugPrint('Error submitting to challenge: $e');
      rethrow;
    }
  }

  // Get submissions for a challenge (without orderBy to avoid Firebase index requirements)
  Stream<List<ChallengeSubmissionModel>> getChallengeSubmissions(String challengeId) {
    return _submissionsCollection
        .where('challengeId', isEqualTo: challengeId)
        .snapshots()
        .map((snapshot) {
          final submissions = snapshot.docs
              .map((doc) => ChallengeSubmissionModel.fromFirestore(doc))
              .toList();

          // Sort by submittedAt in memory to avoid Firebase index
          submissions.sort((a, b) => b.submittedAt.compareTo(a.submittedAt));
          return submissions;
        });
  }

  // Get user's submissions (without orderBy to avoid Firebase index requirements)
  Stream<List<ChallengeSubmissionModel>> getUserSubmissions(String userId) {
    return _submissionsCollection
        .where('participantId', isEqualTo: userId)
        .snapshots()
        .map((snapshot) {
          final submissions = snapshot.docs
              .map((doc) => ChallengeSubmissionModel.fromFirestore(doc))
              .toList();

          // Sort by submittedAt in memory to avoid Firebase index
          submissions.sort((a, b) => b.submittedAt.compareTo(a.submittedAt));
          return submissions;
        });
  }

  // Select multiple winners for challenge (10% of participants)
  Future<void> selectWinners(String challengeId, List<String> submissionIds) async {
    try {
      final userId = currentUserId;
      if (userId == null) throw Exception('User not authenticated');

      // Get challenge and verify ownership
      final challenge = await getChallengeById(challengeId);
      if (challenge == null) throw Exception('Challenge not found');
      if (challenge.creatorId != userId) throw Exception('Only challenge creator can select winners');
      if (challenge.status != 'active') throw Exception('Challenge is not active');

      if (submissionIds.isEmpty) throw Exception('No submissions selected');

      // Calculate prize distribution (no platform fee as it was already deducted at creation)
      final totalPrize = challenge.prizeAmount;
      final prizeDistribution = _calculatePrizeDistribution(totalPrize, submissionIds.length);

      // Use batch for atomicity
      final batch = _firestore.batch();

      // Update challenge
      final challengeRef = _challengesCollection.doc(challengeId);
      batch.update(challengeRef, {
        'status': 'completed',
        'winnerIds': submissionIds,
        'completedAt': FieldValue.serverTimestamp(),
      });

      // Update creator's pending challenges
      final creatorRef = _firestore.collection('users').doc(challenge.creatorId);
      batch.update(creatorRef, {
        'pendingChallenges': FieldValue.increment(-challenge.prizeAmount),
      });

      // Process each winner
      for (int i = 0; i < submissionIds.length; i++) {
        final submissionId = submissionIds[i];
        final prizeAmount = prizeDistribution[i];
        final rank = i + 1;

        // Get submission
        final submissionDoc = await _submissionsCollection.doc(submissionId).get();
        if (!submissionDoc.exists) continue;
        final submission = ChallengeSubmissionModel.fromFirestore(submissionDoc);

        // Update submission as winner with rank
        final submissionRef = _submissionsCollection.doc(submissionId);
        batch.update(submissionRef, {
          'isWinner': true,
          'rank': rank,
          'prizeAmount': prizeAmount,
        });

        // Transfer prize to winner (full amount as platform fee already deducted)
        final winnerRef = _firestore.collection('users').doc(submission.participantId);
        batch.update(winnerRef, {
          'balance': FieldValue.increment(prizeAmount),
        });

        // Create transaction for winner
        final transactionRef = _firestore.collection('transactions').doc();
        batch.set(transactionRef, {
          'userId': submission.participantId,
          'type': 'challenge_win',
          'amount': prizeAmount,
          'description': 'Challenge win: ${challenge.title} - Rank $rank (\$${prizeAmount.toStringAsFixed(2)})',
          'challengeId': challengeId,
          'rank': rank,
          'status': 'completed',
          'createdAt': FieldValue.serverTimestamp(),
        });

        // Send notification to winner
        await _notificationService.sendNotification(
          userId: submission.participantId,
          title: 'Congratulations! You Won!',
          body: 'You won rank $rank in challenge "${challenge.title}" and earned \$${prizeAmount.toStringAsFixed(2)}!',
          type: 'challenge_win',
          data: {
            'challengeId': challengeId,
            'submissionId': submissionId,
            'prizeAmount': prizeAmount,
            'rank': rank,
          },
        );
      }

      await batch.commit();
      debugPrint('Winners selected successfully for challenge: $challengeId');
    } catch (e) {
      debugPrint('Error selecting winners: $e');
      rethrow;
    }
  }

  // Calculate prize distribution for multiple winners
  List<double> _calculatePrizeDistribution(double totalPrize, int winnerCount) {
    if (winnerCount == 1) {
      return [totalPrize];
    }

    final List<double> distribution = [];

    // Prize distribution: 1st place gets 50%, 2nd gets 30%, 3rd gets 20%
    // For more winners, distribute remaining amounts proportionally
    final List<double> percentages = [];

    if (winnerCount >= 1) percentages.add(0.50); // 1st place: 50%
    if (winnerCount >= 2) percentages.add(0.30); // 2nd place: 30%
    if (winnerCount >= 3) percentages.add(0.20); // 3rd place: 20%

    // For additional winners beyond 3rd place, distribute remaining amount equally
    if (winnerCount > 3) {
      const remainingPercentage = 0.20; // 20% for 3rd place and beyond
      final additionalWinners = winnerCount - 2; // Excluding 1st and 2nd
      final eachAdditionalPercentage = remainingPercentage / additionalWinners;

      // Replace 3rd place percentage and add for additional winners
      percentages[2] = eachAdditionalPercentage;
      for (int i = 3; i < winnerCount; i++) {
        percentages.add(eachAdditionalPercentage);
      }
    }

    // Calculate actual amounts
    for (int i = 0; i < winnerCount; i++) {
      final amount = totalPrize * percentages[i];
      distribution.add(amount);
    }

    return distribution;
  }

  // Get recommended number of winners (10% of participants, minimum 1)
  int getRecommendedWinnerCount(int totalSubmissions) {
    if (totalSubmissions == 0) return 0;
    final tenPercent = (totalSubmissions * 0.1).ceil();
    return tenPercent < 1 ? 1 : tenPercent;
  }

  // Legacy method for backward compatibility (single winner)
  Future<void> selectWinner(String challengeId, String submissionId) async {
    await selectWinners(challengeId, [submissionId]);
  }

  // Vote for submission
  Future<void> voteForSubmission(String submissionId) async {
    try {
      final userId = currentUserId;
      if (userId == null) throw Exception('User not authenticated');

      // Check if user already voted for this submission
      final voteQuery = await _firestore
          .collection('submission_votes')
          .where('submissionId', isEqualTo: submissionId)
          .where('voterId', isEqualTo: userId)
          .get();

      if (voteQuery.docs.isNotEmpty) {
        throw Exception('You have already voted for this submission');
      }

      // Use batch for atomicity
      final batch = _firestore.batch();

      // Add vote record
      final voteRef = _firestore.collection('submission_votes').doc();
      batch.set(voteRef, {
        'submissionId': submissionId,
        'voterId': userId,
        'createdAt': FieldValue.serverTimestamp(),
      });

      // Update submission vote count
      final submissionRef = _submissionsCollection.doc(submissionId);
      batch.update(submissionRef, {
        'voteCount': FieldValue.increment(1),
      });

      await batch.commit();
      debugPrint('Vote added successfully for submission: $submissionId');
    } catch (e) {
      debugPrint('Error voting for submission: $e');
      rethrow;
    }
  }

  // Remove vote for submission
  Future<void> removeVoteForSubmission(String submissionId) async {
    try {
      final userId = currentUserId;
      if (userId == null) throw Exception('User not authenticated');

      // Find existing vote
      final voteQuery = await _firestore
          .collection('submission_votes')
          .where('submissionId', isEqualTo: submissionId)
          .where('voterId', isEqualTo: userId)
          .get();

      if (voteQuery.docs.isEmpty) {
        throw Exception('No vote found to remove');
      }

      // Use batch for atomicity
      final batch = _firestore.batch();

      // Remove vote record
      final voteDoc = voteQuery.docs.first;
      batch.delete(voteDoc.reference);

      // Update submission vote count
      final submissionRef = _submissionsCollection.doc(submissionId);
      batch.update(submissionRef, {
        'voteCount': FieldValue.increment(-1),
      });

      await batch.commit();
      debugPrint('Vote removed successfully for submission: $submissionId');
    } catch (e) {
      debugPrint('Error removing vote for submission: $e');
      rethrow;
    }
  }

  // Check if user has voted for submission
  Future<bool> hasUserVotedForSubmission(String submissionId) async {
    try {
      final userId = currentUserId;
      if (userId == null) return false;

      final voteQuery = await _firestore
          .collection('submission_votes')
          .where('submissionId', isEqualTo: submissionId)
          .where('voterId', isEqualTo: userId)
          .get();

      return voteQuery.docs.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking vote status: $e');
      return false;
    }
  }

  // Edit submission
  Future<void> editSubmission({
    required String submissionId,
    required String title,
    required String description,
    required String codeUrl,
    String? demoUrl,
    List<File>? newImages,
  }) async {
    try {
      final userId = currentUserId;
      if (userId == null) throw Exception('User not authenticated');

      // Get submission and verify ownership
      final submissionDoc = await _submissionsCollection.doc(submissionId).get();
      if (!submissionDoc.exists) throw Exception('Submission not found');

      final submission = ChallengeSubmissionModel.fromFirestore(submissionDoc);
      if (submission.participantId != userId) {
        throw Exception('You can only edit your own submissions');
      }

      // Prepare update data
      final Map<String, dynamic> updateData = {
        'title': title,
        'description': description,
        'codeUrl': codeUrl,
        'demoUrl': demoUrl,
        'updatedAt': FieldValue.serverTimestamp(),
      };

      // Upload new images if provided
      if (newImages != null && newImages.isNotEmpty) {
        final List<String> imageUrls = [];
        for (final image in newImages) {
          final imageUrl = await _uploadFile(image, 'challenge_submissions/$submissionId/images');
          imageUrls.add(imageUrl);
        }
        updateData['imageUrls'] = imageUrls;
      }

      await _submissionsCollection.doc(submissionId).update(updateData);
      debugPrint('Submission edited successfully: $submissionId');
    } catch (e) {
      debugPrint('Error editing submission: $e');
      rethrow;
    }
  }

  // Check if user already has a submission for this challenge
  Future<bool> hasUserSubmittedToChallenge(String challengeId, String userId) async {
    try {
      final submissionQuery = await _submissionsCollection
          .where('challengeId', isEqualTo: challengeId)
          .where('participantId', isEqualTo: userId)
          .get();

      return submissionQuery.docs.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking user submission: $e');
      return false;
    }
  }

  // Get featured challenges (top high amount challenges)
  Stream<List<ChallengeModel>> getFeaturedChallenges({int limit = 5}) {
    return _challengesCollection
        .where('status', isEqualTo: 'active')
        .snapshots()
        .map((snapshot) {
          final challenges = snapshot.docs
              .map((doc) => ChallengeModel.fromFirestore(doc))
              .toList();

          // Sort by prize amount in memory to avoid Firebase index
          challenges.sort((a, b) => b.prizeAmount.compareTo(a.prizeAmount));

          // Return top challenges (featured)
          return challenges.take(limit).toList();
        });
  }

  // Get top 3 highest amount challenges for reference
  Future<List<ChallengeModel>> getTopThreeChallenges() async {
    try {
      final challengesSnapshot = await _challengesCollection
          .where('status', isEqualTo: 'active')
          .get();

      final challenges = challengesSnapshot.docs
          .map((doc) => ChallengeModel.fromFirestore(doc))
          .toList();

      // Sort by prize amount descending
      challenges.sort((a, b) => b.prizeAmount.compareTo(a.prizeAmount));

      // Return top 3
      return challenges.take(3).toList();
    } catch (e) {
      debugPrint('Error getting top challenges: $e');
      return [];
    }
  }

  // Check if amount qualifies for featured section
  Future<bool> isAmountFeatured(double amount) async {
    try {
      final topChallenges = await getTopThreeChallenges();
      if (topChallenges.isEmpty) return true; // First challenge is always featured

      // If amount is higher than the lowest in top 3, it will be featured
      if (topChallenges.length < 3) return true;

      final lowestTopAmount = topChallenges.last.prizeAmount;
      return amount > lowestTopAmount;
    } catch (e) {
      debugPrint('Error checking featured status: $e');
      return false;
    }
  }

  // Cancel challenge (only if no submissions)
  Future<void> cancelChallenge(String challengeId) async {
    try {
      final userId = currentUserId;
      if (userId == null) throw Exception('User not authenticated');

      // Get challenge and verify ownership
      final challenge = await getChallengeById(challengeId);
      if (challenge == null) throw Exception('Challenge not found');
      if (challenge.creatorId != userId) throw Exception('Only challenge creator can cancel');
      if (challenge.status != 'active') throw Exception('Challenge is not active');
      if (challenge.submissionCount > 0) throw Exception('Cannot cancel challenge with submissions');

      // Use batch for atomicity
      final batch = _firestore.batch();

      // Update challenge status
      final challengeRef = _challengesCollection.doc(challengeId);
      batch.update(challengeRef, {
        'status': 'cancelled',
        'completedAt': FieldValue.serverTimestamp(),
      });

      // Refund prize to creator
      final creatorRef = _firestore.collection('users').doc(challenge.creatorId);
      batch.update(creatorRef, {
        'balance': FieldValue.increment(challenge.prizeAmount),
        'pendingChallenges': FieldValue.increment(-challenge.prizeAmount),
      });

      // Create refund transaction
      final transactionRef = _firestore.collection('transactions').doc();
      batch.set(transactionRef, {
        'userId': challenge.creatorId,
        'type': 'challenge_refund',
        'amount': challenge.prizeAmount,
        'description': 'Challenge cancellation refund: ${challenge.title}',
        'challengeId': challengeId,
        'status': 'completed',
        'createdAt': FieldValue.serverTimestamp(),
      });

      await batch.commit();

      // Send notification to creator about successful cancellation
      await _notificationService.sendNotification(
        userId: challenge.creatorId,
        title: 'Challenge Cancelled Successfully',
        body: 'Your challenge "${challenge.title}" has been cancelled. Full refund of \$${challenge.prizeAmount.toStringAsFixed(2)} has been transferred to your Asatu wallet without any fee deduction.',
        type: 'challenge_cancelled',
        data: {
          'challengeId': challengeId,
          'refundAmount': challenge.prizeAmount,
          'challengeTitle': challenge.title,
        },
      );

      debugPrint('Challenge cancelled successfully: $challengeId');
    } catch (e) {
      debugPrint('Error cancelling challenge: $e');
      rethrow;
    }
  }

  // Check and refund expired challenges with no participants
  Future<void> checkAndRefundExpiredChallenges() async {
    try {
      // Get all active challenges
      final challengesSnapshot = await _challengesCollection
          .where('status', isEqualTo: 'active')
          .get();

      final now = DateTime.now();

      for (final challengeDoc in challengesSnapshot.docs) {
        final challenge = ChallengeModel.fromFirestore(challengeDoc);

        // Check if challenge is expired and has no submissions
        if (now.isAfter(challenge.deadline) && challenge.submissionCount == 0) {
          await _refundExpiredChallenge(challenge.id);
        }
      }
    } catch (e) {
      debugPrint('Error checking expired challenges: $e');
    }
  }

  // Refund expired challenge with no participants
  Future<void> _refundExpiredChallenge(String challengeId) async {
    try {
      // Get challenge details
      final challenge = await getChallengeById(challengeId);
      if (challenge == null || challenge.status != 'active') return;

      // Use batch for atomicity
      final batch = _firestore.batch();

      // Update challenge status to expired
      final challengeRef = _challengesCollection.doc(challengeId);
      batch.update(challengeRef, {
        'status': 'expired',
        'completedAt': FieldValue.serverTimestamp(),
      });

      // Refund full amount to creator (no platform fee for expired challenges)
      final creatorRef = _firestore.collection('users').doc(challenge.creatorId);
      batch.update(creatorRef, {
        'balance': FieldValue.increment(challenge.prizeAmount),
        'pendingChallenges': FieldValue.increment(-challenge.prizeAmount),
      });

      // Create refund transaction
      final transactionRef = _firestore.collection('transactions').doc();
      batch.set(transactionRef, {
        'userId': challenge.creatorId,
        'type': 'challenge_refund_expired',
        'amount': challenge.prizeAmount,
        'description': 'Full refund for expired challenge: ${challenge.title} (no participants)',
        'challengeId': challengeId,
        'status': 'completed',
        'createdAt': FieldValue.serverTimestamp(),
      });

      await batch.commit();

      // Send notification to creator
      await _notificationService.sendNotification(
        userId: challenge.creatorId,
        title: 'Challenge Expired - Full Refund',
        body: 'Your challenge "${challenge.title}" expired with no participants. Full amount \$${challenge.prizeAmount.toStringAsFixed(2)} has been refunded to your wallet.',
        type: 'challenge_expired_refund',
        data: {
          'challengeId': challengeId,
          'refundAmount': challenge.prizeAmount,
        },
      );

      debugPrint('Expired challenge refunded successfully: $challengeId');
    } catch (e) {
      debugPrint('Error refunding expired challenge: $e');
      rethrow;
    }
  }

  // Send deadline approaching notification to challenge creator
  Future<void> sendDeadlineNotification(String challengeId) async {
    try {
      final challenge = await getChallengeById(challengeId);
      if (challenge == null || challenge.status != 'active') return;

      final now = DateTime.now();
      final timeRemaining = challenge.deadline.difference(now);

      if (timeRemaining.inHours <= 24 && timeRemaining.inHours > 0) {
        await _notificationService.sendNotification(
          userId: challenge.creatorId,
          title: 'Challenge Deadline Approaching!',
          body: 'Your challenge "${challenge.title}" expires in ${timeRemaining.inHours} hours. Please select winners soon, or prizes will be distributed automatically based on votes.',
          type: 'deadline_approaching',
          data: {
            'challengeId': challengeId,
            'hoursRemaining': timeRemaining.inHours,
            'challengeTitle': challenge.title,
          },
        );
      }
    } catch (e) {
      debugPrint('Error sending deadline notification: $e');
    }
  }

  // Auto-select winners based on votes when deadline passes
  Future<void> autoSelectWinnersByVotes(String challengeId) async {
    try {
      final challenge = await getChallengeById(challengeId);
      if (challenge == null || challenge.status != 'active') return;

      // Get all submissions for this challenge
      final submissionsSnapshot = await _submissionsCollection
          .where('challengeId', isEqualTo: challengeId)
          .get();

      if (submissionsSnapshot.docs.isEmpty) {
        // No submissions, refund the challenge
        await _refundExpiredChallenge(challengeId);
        return;
      }

      final submissions = submissionsSnapshot.docs
          .map((doc) => ChallengeSubmissionModel.fromFirestore(doc))
          .toList();

      // Sort by vote count (highest first)
      submissions.sort((a, b) => b.voteCount.compareTo(a.voteCount));

      // Calculate 10% of participants for winners
      final winnerCount = getRecommendedWinnerCount(submissions.length);
      final winnerSubmissions = submissions.take(winnerCount).toList();

      if (winnerSubmissions.isNotEmpty) {
        // Select winners automatically
        final winnerIds = winnerSubmissions.map((s) => s.id).toList();
        await selectWinners(challengeId, winnerIds);

        // Send notification to creator about auto-selection
        await _notificationService.sendNotification(
          userId: challenge.creatorId,
          title: 'Winners Auto-Selected',
          body: 'Your challenge "${challenge.title}" deadline passed. Winners were automatically selected based on votes.',
          type: 'auto_winner_selection',
          data: {
            'challengeId': challengeId,
            'winnerCount': winnerCount,
            'challengeTitle': challenge.title,
          },
        );
      }
    } catch (e) {
      debugPrint('Error auto-selecting winners: $e');
    }
  }

  // Upload file to Firebase Storage
  Future<String> _uploadFile(File file, String path) async {
    try {
      final ref = _storage.ref().child(path).child('${DateTime.now().millisecondsSinceEpoch}_${file.path.split('/').last}');
      final uploadTask = ref.putFile(file);
      final snapshot = await uploadTask;
      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      debugPrint('Error uploading file: $e');
      rethrow;
    }
  }
}
