import 'package:cloud_firestore/cloud_firestore.dart';

/// Payment method model class to handle user payment methods
class PaymentMethodModel {
  final String id;
  final String userId;
  final String type; // 'paypal', 'google_pay', 'phone_pe', 'paytm', etc.
  final String accountId; // Email, phone number, or account ID
  final String displayName; // User-friendly name for this payment method
  final Map<String, dynamic> additionalDetails; // Additional details specific to the payment method
  final bool isDefault;
  final bool isVerified;
  final DateTime createdAt;
  final DateTime updatedAt;

  PaymentMethodModel({
    required this.id,
    required this.userId,
    required this.type,
    required this.accountId,
    required this.displayName,
    Map<String, dynamic>? additionalDetails,
    required this.isDefault,
    required this.isVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : 
    additionalDetails = additionalDetails ?? {},
    createdAt = createdAt ?? DateTime.now(),
    updatedAt = updatedAt ?? DateTime.now();

  // Create a PaymentMethodModel from a Firestore document
  factory PaymentMethodModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>? ?? {};
    
    final Timestamp createdAtTimestamp = data['createdAt'] as Timestamp? ?? Timestamp.now();
    final Timestamp updatedAtTimestamp = data['updatedAt'] as Timestamp? ?? Timestamp.now();
    
    return PaymentMethodModel(
      id: doc.id,
      userId: data['userId'] as String? ?? '',
      type: data['type'] as String? ?? '',
      accountId: data['accountId'] as String? ?? '',
      displayName: data['displayName'] as String? ?? '',
      additionalDetails: data['additionalDetails'] as Map<String, dynamic>? ?? {},
      isDefault: data['isDefault'] as bool? ?? false,
      isVerified: data['isVerified'] as bool? ?? false,
      createdAt: createdAtTimestamp.toDate(),
      updatedAt: updatedAtTimestamp.toDate(),
    );
  }

  // Convert PaymentMethodModel to a Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'type': type,
      'accountId': accountId,
      'displayName': displayName,
      'additionalDetails': additionalDetails,
      'isDefault': isDefault,
      'isVerified': isVerified,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(DateTime.now()),
    };
  }

  // Create a copy of this PaymentMethodModel with updated fields
  PaymentMethodModel copyWith({
    String? id,
    String? userId,
    String? type,
    String? accountId,
    String? displayName,
    Map<String, dynamic>? additionalDetails,
    bool? isDefault,
    bool? isVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PaymentMethodModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      accountId: accountId ?? this.accountId,
      displayName: displayName ?? this.displayName,
      additionalDetails: additionalDetails ?? this.additionalDetails,
      isDefault: isDefault ?? this.isDefault,
      isVerified: isVerified ?? this.isVerified,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Get icon for this payment method type
  String get iconAsset {
    switch (type) {
      case 'paypal':
        return 'assets/icons/paypal.png';
      case 'google_pay':
        return 'assets/icons/google_pay.png';
      case 'phone_pe':
        return 'assets/icons/phonepe.png';
      case 'paytm':
        return 'assets/icons/paytm.png';
      case 'upi':
        return 'assets/icons/upi.png';
      default:
        return 'assets/icons/payment.png';
    }
  }

  // Get display name for this payment method type
  String get typeDisplayName {
    switch (type) {
      case 'paypal':
        return 'PayPal';
      case 'google_pay':
        return 'Google Pay';
      case 'phone_pe':
        return 'PhonePe';
      case 'paytm':
        return 'Paytm';
      case 'upi':
        return 'UPI';
      default:
        return type.split('_').map((word) => word.isEmpty ? '' : '${word[0].toUpperCase()}${word.substring(1)}').join(' ');
    }
  }
}
