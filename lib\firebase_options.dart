// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyA-wuJncazZMOulh2AGTwUFuRcBCtjzD0A',
    appId: '1:808255690080:web:a8f9df47d004e403003a05',
    messagingSenderId: '808255690080',
    projectId: 'asatu-f1852',
    authDomain: 'asatu-f1852.firebaseapp.com',
    databaseURL: 'https://asatu-f1852-default-rtdb.firebaseio.com',
    storageBucket: 'asatu-f1852.firebasestorage.app',
    measurementId: 'G-SC7H2TM8L0',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyA-wuJncazZMOulh2AGTwUFuRcBCtjzD0A',
    appId: '1:808255690080:android:a8f9df47d004e403003a05',
    messagingSenderId: '808255690080',
    projectId: 'asatu-f1852',
    databaseURL: 'https://asatu-f1852-default-rtdb.firebaseio.com',
    storageBucket: 'asatu-f1852.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyA-wuJncazZMOulh2AGTwUFuRcBCtjzD0A',
    appId: '1:808255690080:ios:a8f9df47d004e403003a05',
    messagingSenderId: '808255690080',
    projectId: 'asatu-f1852',
    databaseURL: 'https://asatu-f1852-default-rtdb.firebaseio.com',
    storageBucket: 'asatu-f1852.firebasestorage.app',
    iosBundleId: 'com.asatu.com',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyA-wuJncazZMOulh2AGTwUFuRcBCtjzD0A',
    appId: '1:808255690080:macos:a8f9df47d004e403003a05',
    messagingSenderId: '808255690080',
    projectId: 'asatu-f1852',
    databaseURL: 'https://asatu-f1852-default-rtdb.firebaseio.com',
    storageBucket: 'asatu-f1852.firebasestorage.app',
    iosBundleId: 'com.asatu.com',
  );
}
