import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import 'notification_service.dart';

/// A service class to handle admin operations
class AdminService {
  // Singleton pattern
  static final AdminService _instance = AdminService._internal();
  factory AdminService() => _instance;
  AdminService._internal();

  // Firebase instances
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final NotificationService _notificationService = NotificationService();

  // Check if current user is admin
  Future<bool> isCurrentUserAdmin() async {
    try {
      final User? currentUser = _auth.currentUser;
      if (currentUser == null) {
        return false;
      }

      final DocumentSnapshot userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (!userDoc.exists) {
        return false;
      }

      final userData = userDoc.data() as Map<String, dynamic>?;
      return userData?['isAdmin'] == true;
    } catch (e) {
      debugPrint('Error checking admin status: $e');
      return false;
    }
  }

  // Get all users
  Future<List<UserModel>> getAllUsers({int limit = 50, String? lastUserId}) async {
    try {
      Query query = _firestore.collection('users').orderBy('createdAt', descending: true);

      if (lastUserId != null) {
        final lastUserDoc = await _firestore.collection('users').doc(lastUserId).get();
        query = query.startAfterDocument(lastUserDoc);
      }

      query = query.limit(limit);

      final QuerySnapshot snapshot = await query.get();

      final List<UserModel> users = [];

      for (final doc in snapshot.docs) {
        final userData = doc.data() as Map<String, dynamic>;

        // Get profile data if available
        Map<String, dynamic>? profileData;
        try {
          final DocumentSnapshot profileDoc = await _firestore
              .collection('user_profiles')
              .doc(userData['uid'] as String)
              .get();

          if (profileDoc.exists) {
            profileData = profileDoc.data() as Map<String, dynamic>?;
          }
        } catch (e) {
          debugPrint('Error fetching profile data: $e');
        }

        users.add(UserModel.fromFirebaseUser(userData, profileData));
      }

      return users;
    } catch (e) {
      debugPrint('Error getting users: $e');
      return [];
    }
  }

  // Search users
  Future<List<UserModel>> searchUsers(String query, {int limit = 20}) async {
    try {
      // Search by display name, email, or username
      final QuerySnapshot displayNameSnapshot = await _firestore
          .collection('users')
          .where('displayName', isGreaterThanOrEqualTo: query)
          .where('displayName', isLessThanOrEqualTo: '$query\uf8ff')
          .limit(limit)
          .get();

      final QuerySnapshot emailSnapshot = await _firestore
          .collection('users')
          .where('email', isGreaterThanOrEqualTo: query)
          .where('email', isLessThanOrEqualTo: '$query\uf8ff')
          .limit(limit)
          .get();

      final QuerySnapshot usernameSnapshot = await _firestore
          .collection('users')
          .where('username', isGreaterThanOrEqualTo: query)
          .where('username', isLessThanOrEqualTo: '$query\uf8ff')
          .limit(limit)
          .get();

      // Combine results
      final Set<String> userIds = {};
      final List<UserModel> users = [];

      for (final snapshot in [displayNameSnapshot, emailSnapshot, usernameSnapshot]) {
        for (final doc in snapshot.docs) {
          final userData = doc.data() as Map<String, dynamic>;
          final userId = userData['uid'] as String;

          if (!userIds.contains(userId)) {
            userIds.add(userId);

            // Get profile data if available
            Map<String, dynamic>? profileData;
            try {
              final DocumentSnapshot profileDoc = await _firestore
                  .collection('user_profiles')
                  .doc(userId)
                  .get();

              if (profileDoc.exists) {
                profileData = profileDoc.data() as Map<String, dynamic>?;
              }
            } catch (e) {
              debugPrint('Error fetching profile data: $e');
            }

            users.add(UserModel.fromFirebaseUser(userData, profileData));
          }
        }
      }

      return users;
    } catch (e) {
      debugPrint('Error searching users: $e');
      return [];
    }
  }

  // Ban user
  Future<void> banUser(String userId, bool isBanned) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'isBanned': isBanned,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error banning user: $e');
      rethrow;
    }
  }

  // Make user admin
  Future<void> makeUserAdmin(String userId, bool isAdmin) async {
    try {
      await _firestore.collection('users').doc(userId).update({
        'isAdmin': isAdmin,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error making user admin: $e');
      rethrow;
    }
  }

  // Delete user
  Future<void> deleteUser(String userId) async {
    try {
      // Delete user document
      await _firestore.collection('users').doc(userId).delete();

      // Delete user profile
      await _firestore.collection('user_profiles').doc(userId).delete();

      // Delete user settings
      await _firestore.collection('user_settings').doc(userId).delete();

      // Note: This doesn't delete the actual Firebase Auth user
      // That would require admin SDK or Cloud Functions
    } catch (e) {
      debugPrint('Error deleting user: $e');
      rethrow;
    }
  }

  // Get user analytics
  Future<Map<String, dynamic>> getUserAnalytics() async {
    try {
      // Get total users count
      final QuerySnapshot usersSnapshot = await _firestore
          .collection('users')
          .get();

      // Get new users in the last 7 days
      final DateTime sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7));
      final QuerySnapshot newUsersSnapshot = await _firestore
          .collection('users')
          .where('createdAt', isGreaterThanOrEqualTo: Timestamp.fromDate(sevenDaysAgo))
          .get();

      // Get active users in the last 7 days
      final QuerySnapshot activeUsersSnapshot = await _firestore
          .collection('users')
          .where('lastLogin', isGreaterThanOrEqualTo: Timestamp.fromDate(sevenDaysAgo))
          .get();

      // Get banned users count
      final QuerySnapshot bannedUsersSnapshot = await _firestore
          .collection('users')
          .where('isBanned', isEqualTo: true)
          .get();

      return {
        'totalUsers': usersSnapshot.size,
        'newUsers': newUsersSnapshot.size,
        'activeUsers': activeUsersSnapshot.size,
        'bannedUsers': bannedUsersSnapshot.size,
      };
    } catch (e) {
      debugPrint('Error getting user analytics: $e');
      return {
        'totalUsers': 0,
        'newUsers': 0,
        'activeUsers': 0,
        'bannedUsers': 0,
      };
    }
  }

  // Get pending asset approvals
  Stream<List<Map<String, dynamic>>> getPendingAssetApprovals() {
    try {
      // Use a simpler query that doesn't require a complex index
      // We'll filter the results in memory instead
      return _firestore
          .collection('assets')
          .where('status', isEqualTo: 'pending')
          .snapshots()
          .map((snapshot) {
            // Filter the results in memory
            final filteredDocs = snapshot.docs.where((doc) {
              final data = doc.data();
              return data['requiresAdminApproval'] == true &&
                     data['adminReviewed'] == false;
            }).toList();

            // Sort by createdAt in descending order
            filteredDocs.sort((a, b) {
              final aTime = a.data()['createdAt'] as Timestamp?;
              final bTime = b.data()['createdAt'] as Timestamp?;

              if (aTime == null && bTime == null) return 0;
              if (aTime == null) return 1;
              if (bTime == null) return -1;

              return bTime.compareTo(aTime); // Descending order
            });

            // Map to the required format
            return filteredDocs.map((doc) {
              final data = doc.data();
              return {
                'id': doc.id,
                'title': data['title'] ?? 'Untitled Asset',
                'type': data['type'] ?? 'Asset',
                'creator': data['authorName'] ?? 'Unknown',
                'creatorId': data['authorId'] ?? '',
                'submittedDate': (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
                'price': (data['price'] as num?)?.toDouble() ?? 0.0,
                'description': data['description'] ?? '',
                'thumbnailUrl': data['thumbnailUrl'] ?? '',
                'fileUrl': data['fileUrl'] ?? '',
                'screenshotUrls': List<String>.from(data['screenshotUrls'] ?? []),
                'category': data['category'] ?? '',
                'subcategories': List<String>.from(data['subcategories'] ?? []),
                'tags': List<String>.from(data['tags'] ?? []),
              };
            }).toList();
          });
    } catch (e) {
      debugPrint('Error getting pending asset approvals: $e');
      return Stream.value([]);
    }
  }

  // Approve an asset
  Future<bool> approveAsset(String assetId) async {
    try {
      // Check if current user is admin
      final isAdmin = await isCurrentUserAdmin();
      if (!isAdmin) {
        return false;
      }

      // Update asset status
      await _firestore.collection('assets').doc(assetId).update({
        'status': 'approved',
        'adminReviewed': true,
        'reviewedAt': FieldValue.serverTimestamp(),
        'reviewedBy': _auth.currentUser?.uid,
      });

      // Add to admin activity log
      await _firestore.collection('admin_activity_log').add({
        'action': 'Asset Approved',
        'assetId': assetId,
        'adminId': _auth.currentUser?.uid,
        'timestamp': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      debugPrint('Error approving asset: $e');
      return false;
    }
  }

  // Reject an asset
  Future<bool> rejectAsset(String assetId, String reason) async {
    try {
      // Check if current user is admin
      final isAdmin = await isCurrentUserAdmin();
      if (!isAdmin) {
        return false;
      }

      // Update asset status
      await _firestore.collection('assets').doc(assetId).update({
        'status': 'rejected',
        'adminReviewed': true,
        'rejectionReason': reason,
        'reviewedAt': FieldValue.serverTimestamp(),
        'reviewedBy': _auth.currentUser?.uid,
      });

      // Add to admin activity log
      await _firestore.collection('admin_activity_log').add({
        'action': 'Asset Rejected',
        'assetId': assetId,
        'reason': reason,
        'adminId': _auth.currentUser?.uid,
        'timestamp': FieldValue.serverTimestamp(),
      });

      return true;
    } catch (e) {
      debugPrint('Error rejecting asset: $e');
      return false;
    }
  }

  /// Check and automatically restore account if user logs back in within 30 days
  /// This method is called during login process
  Future<Map<String, dynamic>> checkAndAutoRestoreAccount(String userId) async {
    try {
      debugPrint('AdminService: Checking account restoration for user: $userId');

      // Check if there's a pending deletion record for this user
      final deletionDoc = await _firestore.collection('deleted_accounts').doc(userId).get();

      if (!deletionDoc.exists) {
        debugPrint('AdminService: No deletion record found for user: $userId');
        return {
          'success': true,
          'message': 'No deletion record found. Account is active.',
        };
      }

      final deletionData = deletionDoc.data() as Map<String, dynamic>;
      final String status = deletionData['status'] ?? '';

      debugPrint('AdminService: Found deletion record with status: $status for user: $userId');

      // Only restore if status is 'pending' (not already processed or completed)
      if (status != 'pending') {
        debugPrint('AdminService: Account deletion is not in pending status: $status');
        return {
          'success': true,
          'message': 'Account deletion is not in pending status.',
        };
      }

      final Timestamp? deletionRequestedAt = deletionData['deletionRequestedAt'] as Timestamp?;

      if (deletionRequestedAt == null) {
        debugPrint('AdminService: Invalid deletion record - missing timestamp');
        return {
          'success': false,
          'error': 'Invalid deletion record - missing timestamp.',
        };
      }

      final DateTime requestDate = deletionRequestedAt.toDate();
      final DateTime now = DateTime.now();
      final int daysSinceRequest = now.difference(requestDate).inDays;

      debugPrint('AdminService: Days since deletion request: $daysSinceRequest for user: $userId');

      // If within 30 days, restore the account
      if (daysSinceRequest <= 30) {
        debugPrint('AdminService: Restoring account for user: $userId (within 30 days)');

        // Use a batch to ensure all operations succeed or fail together
        final WriteBatch batch = _firestore.batch();

        // Update user record to restore account
        final userRef = _firestore.collection('users').doc(userId);

        // First check if user document exists
        final userDoc = await userRef.get();
        if (!userDoc.exists) {
          debugPrint('AdminService: User document does not exist for: $userId');
          return {
            'success': false,
            'error': 'User document not found.',
          };
        }

        // Use set with merge to ensure the update works
        batch.set(userRef, {
          'accountStatus': 'active',
          'deletionRequestedAt': FieldValue.delete(),
          'scheduledDeletionDate': FieldValue.delete(),
          'restoredAt': FieldValue.serverTimestamp(),
          'autoRestored': true,
          'updatedAt': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true));

        // Update deletion record to mark as canceled
        final deletionRef = _firestore.collection('deleted_accounts').doc(userId);
        batch.update(deletionRef, {
          'status': 'canceled',
          'canceledAt': FieldValue.serverTimestamp(),
          'cancelReason': 'User logged back in within 30 days',
          'autoRestored': true,
        });

        // Commit the batch
        await batch.commit();
        debugPrint('AdminService: Batch operations completed for user: $userId');

        // Send notification about account restoration
        try {
          final notificationResult = await _notificationService.sendNotification(
            userId: userId,
            title: 'Account Restored',
            body: 'Welcome back! Your account deletion request has been canceled and your account has been restored.',
            type: 'account',
          );
          debugPrint('AdminService: Notification sent result: $notificationResult for user: $userId');

          // Also create a direct notification document as fallback
          await _firestore.collection('notifications').add({
            'userId': userId,
            'title': 'Account Restored',
            'body': 'Welcome back! Your account deletion request has been canceled and your account has been restored.',
            'type': 'account',
            'read': false,
            'createdAt': FieldValue.serverTimestamp(),
            'data': {
              'action': 'account_restored',
              'restoredAt': DateTime.now().toIso8601String(),
            },
          });
          debugPrint('AdminService: Direct notification document created for user: $userId');

        } catch (notificationError) {
          debugPrint('AdminService: Error sending notification: $notificationError');

          // Try to create notification document directly as fallback
          try {
            await _firestore.collection('notifications').add({
              'userId': userId,
              'title': 'Account Restored',
              'body': 'Welcome back! Your account deletion request has been canceled and your account has been restored.',
              'type': 'account',
              'read': false,
              'createdAt': FieldValue.serverTimestamp(),
              'data': {
                'action': 'account_restored',
                'restoredAt': DateTime.now().toIso8601String(),
              },
            });
            debugPrint('AdminService: Fallback notification document created for user: $userId');
          } catch (fallbackError) {
            debugPrint('AdminService: Failed to create fallback notification: $fallbackError');
          }
        }

        debugPrint('AdminService: Account auto-restored successfully for user: $userId');

        return {
          'success': true,
          'message': 'Account automatically restored.',
          'restored': true,
        };
      } else {
        debugPrint('AdminService: Account deletion period has expired (>30 days) for user: $userId');
        return {
          'success': true,
          'message': 'Account deletion period has expired (>30 days).',
          'restored': false,
        };
      }
    } catch (e) {
      debugPrint('AdminService: Error checking/restoring account: $e');
      return {
        'success': false,
        'error': 'Failed to check account status: $e',
      };
    }
  }

  /// Restore a user account that was scheduled for deletion (admin only)
  Future<Map<String, dynamic>> restoreUserAccount(String userId) async {
    try {
      // Check if current user is admin
      if (!await isCurrentUserAdmin()) {
        return {
          'success': false,
          'error': 'Insufficient permissions. Admin access required.',
        };
      }

      // Check if there's a pending deletion record for this user
      final deletionDoc = await _firestore.collection('deleted_accounts').doc(userId).get();

      if (!deletionDoc.exists) {
        return {
          'success': false,
          'error': 'No deletion record found for this user.',
        };
      }

      final deletionData = deletionDoc.data() as Map<String, dynamic>;
      final String status = deletionData['status'] ?? '';

      if (status != 'pending') {
        return {
          'success': false,
          'error': 'Account deletion is not in pending status. Current status: $status',
        };
      }

      // Use a batch to ensure all operations succeed or fail together
      final WriteBatch batch = _firestore.batch();

      // Update user record to restore account
      final userRef = _firestore.collection('users').doc(userId);
      batch.update(userRef, {
        'accountStatus': 'active',
        'deletionRequestedAt': FieldValue.delete(),
        'scheduledDeletionDate': FieldValue.delete(),
        'restoredAt': FieldValue.serverTimestamp(),
        'restoredBy': _auth.currentUser?.uid,
      });

      // Update deletion record to mark as canceled
      final deletionRef = _firestore.collection('deleted_accounts').doc(userId);
      batch.update(deletionRef, {
        'status': 'canceled',
        'canceledAt': FieldValue.serverTimestamp(),
        'cancelReason': 'Account restored by admin',
        'restoredBy': _auth.currentUser?.uid,
      });

      // Commit the batch
      await batch.commit();

      // Send notification about account restoration
      await _notificationService.sendNotification(
        userId: userId,
        title: 'Account Restored',
        body: 'Welcome back! Your account deletion request has been canceled and your account has been restored.',
        type: 'account',
      );

      debugPrint('AdminService: Account restored for user: $userId');

      return {
        'success': true,
        'message': 'Account successfully restored.',
      };
    } catch (e) {
      debugPrint('AdminService: Error restoring account: $e');
      return {
        'success': false,
        'error': 'Failed to restore account: $e',
      };
    }
  }


}
