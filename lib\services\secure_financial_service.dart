import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import '../models/transaction_model.dart';
import '../models/withdrawal_model.dart';

/// A service class to handle financial transactions securely
/// This is a client-side implementation that relies on Firestore security rules
class SecureFinancialService {
  // Singleton pattern
  static final SecureFinancialService _instance = SecureFinancialService._internal();
  factory SecureFinancialService() => _instance;
  SecureFinancialService._internal();

  // Firebase instances
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Get current user ID
  String? get _currentUserId => _auth.currentUser?.uid;

  // Check if user is admin
  Future<bool> isUserAdmin() async {
    if (_currentUserId == null) return false;

    try {
      final userDoc = await _firestore.collection('users').doc(_currentUserId).get();
      return userDoc.data()?['isAdmin'] == true;
    } catch (e) {
      debugPrint('Error checking if user is admin: $e');
      return false;
    }
  }

  // Get user balance
  Future<double> getUserBalance() async {
    if (_currentUserId == null) return 0.0;

    try {
      final userDoc = await _firestore.collection('users').doc(_currentUserId).get();
      return (userDoc.data()?['balance'] as num?)?.toDouble() ?? 0.0;
    } catch (e) {
      debugPrint('Error getting user balance: $e');
      return 0.0;
    }
  }

  // Get user's transaction history (without Firebase indexes)
  Stream<List<TransactionModel>> getUserTransactions() {
    if (_currentUserId == null) {
      return Stream.value([]);
    }

    return _firestore
        .collection('transactions')
        .where('userId', isEqualTo: _currentUserId)
        .snapshots()
        .map((snapshot) {
          final transactions = snapshot.docs
              .map((doc) => TransactionModel.fromFirestore(doc))
              .toList();

          // Sort in memory to avoid Firebase index requirement
          transactions.sort((a, b) => b.createdAt.compareTo(a.createdAt));

          return transactions;
        });
  }

  // Get user's withdrawal history
  Stream<List<WithdrawalModel>> getUserWithdrawals() {
    if (_currentUserId == null) {
      return Stream.value([]);
    }

    return _firestore
        .collection('withdrawals')
        .where('userId', isEqualTo: _currentUserId)
        .orderBy('requestedAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => WithdrawalModel.fromFirestore(doc))
            .toList());
  }

  // Request a withdrawal
  Future<WithdrawalModel> requestWithdrawal({
    required double amount,
    required String paymentMethod,
    required Map<String, dynamic> paymentDetails,
  }) async {
    if (_currentUserId == null) {
      throw Exception('User not authenticated');
    }

    // Validate amount
    if (amount <= 0) {
      throw Exception('Amount must be greater than zero');
    }

    // Check if user has sufficient balance
    final userBalance = await getUserBalance();
    if (userBalance < amount) {
      throw Exception('Insufficient balance');
    }

    try {
      // Use a transaction to ensure data consistency
      return await _firestore.runTransaction<WithdrawalModel>(
        (transaction) async {
          // Get the current user data
          final userRef = _firestore.collection('users').doc(_currentUserId);
          final userDoc = await transaction.get(userRef);

          if (!userDoc.exists) {
            throw Exception('User not found');
          }

          final userData = userDoc.data()!;

          // Calculate new balance and pending withdrawals
          final currentBalance = (userData['balance'] as num?)?.toDouble() ?? 0.0;
          final pendingWithdrawals = (userData['pendingWithdrawals'] as num?)?.toDouble() ?? 0.0;

          if (currentBalance < amount) {
            throw Exception('Insufficient balance');
          }

          final newBalance = currentBalance - amount;
          final newPendingWithdrawals = pendingWithdrawals + amount;

          // Update user document
          transaction.update(userRef, {
            'balance': newBalance,
            'pendingWithdrawals': newPendingWithdrawals,
            'updatedAt': FieldValue.serverTimestamp(),
          });

          // Create a withdrawal request
          final withdrawalRef = _firestore.collection('withdrawals').doc();
          final withdrawalData = {
            'userId': _currentUserId,
            'amount': amount,
            'paymentMethod': paymentMethod,
            'paymentDetails': paymentDetails,
            'status': 'pending',
            'requestedAt': FieldValue.serverTimestamp(),
            'updatedAt': FieldValue.serverTimestamp(),
          };

          transaction.set(withdrawalRef, withdrawalData);

          // Create a transaction record
          final transactionRef = _firestore.collection('transactions').doc();
          final transactionData = {
            'userId': _currentUserId,
            'type': 'withdrawal_request',
            'amount': amount,
            'reason': 'Withdrawal request',
            'previousBalance': currentBalance,
            'newBalance': newBalance,
            'withdrawalId': withdrawalRef.id,
            'status': 'pending',
            'createdAt': FieldValue.serverTimestamp(),
          };

          transaction.set(transactionRef, transactionData);

          // Return the withdrawal model
          return WithdrawalModel(
            id: withdrawalRef.id,
            userId: _currentUserId!,
            amount: amount,
            paymentMethod: paymentMethod,
            paymentDetails: paymentDetails,
            status: 'pending',
            requestedAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );
        },
      );
    } catch (e) {
      debugPrint('Error requesting withdrawal: $e');
      rethrow;
    }
  }

  // Admin: Get all pending withdrawals
  Stream<List<WithdrawalModel>> getAllPendingWithdrawals() async* {
    final isAdmin = await isUserAdmin();
    if (!isAdmin) {
      yield [];
      return;
    }

    yield* _firestore
        .collection('withdrawals')
        .where('status', isEqualTo: 'pending')
        .orderBy('requestedAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => WithdrawalModel.fromFirestore(doc))
            .toList());
  }

  // Admin: Process a withdrawal (approve or reject)
  Future<void> processWithdrawal({
    required String withdrawalId,
    required String action, // 'approve' or 'reject'
    String? notes,
  }) async {
    if (_currentUserId == null) {
      throw Exception('User not authenticated');
    }

    // Check if user is admin
    final isAdmin = await isUserAdmin();
    if (!isAdmin) {
      throw Exception('Only admins can process withdrawals');
    }

    if (action != 'approve' && action != 'reject') {
      throw Exception('Action must be either approve or reject');
    }

    try {
      // Use a transaction to ensure data consistency
      await _firestore.runTransaction(
        (transaction) async {
          // Get the withdrawal request
          final withdrawalRef = _firestore.collection('withdrawals').doc(withdrawalId);
          final withdrawalDoc = await transaction.get(withdrawalRef);

          if (!withdrawalDoc.exists) {
            throw Exception('Withdrawal request not found');
          }

          final withdrawalData = withdrawalDoc.data()!;

          // Check if withdrawal is already processed
          if (withdrawalData['status'] != 'pending') {
            throw Exception('Withdrawal has already been processed');
          }

          final userId = withdrawalData['userId'] as String;
          final amount = (withdrawalData['amount'] as num).toDouble();

          // Get the user data
          final userRef = _firestore.collection('users').doc(userId);
          final userDoc = await transaction.get(userRef);

          if (!userDoc.exists) {
            throw Exception('User not found');
          }

          final userData = userDoc.data()!;
          final pendingWithdrawals = (userData['pendingWithdrawals'] as num?)?.toDouble() ?? 0.0;

          // Update withdrawal status
          transaction.update(withdrawalRef, {
            'status': action,
            'processedBy': _currentUserId,
            'processedAt': FieldValue.serverTimestamp(),
            'notes': notes,
            'updatedAt': FieldValue.serverTimestamp(),
          });

          // Update user's pending withdrawals
          final newPendingWithdrawals = (pendingWithdrawals - amount) < 0 ? 0 : (pendingWithdrawals - amount);
          transaction.update(userRef, {
            'pendingWithdrawals': newPendingWithdrawals,
            'updatedAt': FieldValue.serverTimestamp(),
          });

          // Create a transaction record
          final transactionRef = _firestore.collection('transactions').doc();
          transaction.set(transactionRef, {
            'userId': userId,
            'type': action == 'approve' ? 'withdrawal_approved' : 'withdrawal_rejected',
            'amount': amount,
            'reason': action == 'approve' ? 'Withdrawal approved' : 'Withdrawal rejected',
            'withdrawalId': withdrawalId,
            'notes': notes,
            'processedBy': _currentUserId,
            'createdAt': FieldValue.serverTimestamp(),
          });
        },
      );
    } catch (e) {
      debugPrint('Error processing withdrawal: $e');
      rethrow;
    }
  }
}
