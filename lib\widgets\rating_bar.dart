import 'package:flutter/material.dart';

class RatingBar extends StatelessWidget {
  final double rating;
  final double size;
  final Color color;
  final bool showText;
  final TextStyle? textStyle;

  const RatingBar({
    Key? key,
    required this.rating,
    this.size = 16,
    this.color = Colors.amber,
    this.showText = false,
    this.textStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(5, (index) {
            if (index < rating.floor()) {
              // Full star
              return Icon(
                Icons.star,
                color: color,
                size: size,
              );
            } else if (index == rating.floor() && rating % 1 > 0) {
              // Half star
              return Icon(
                Icons.star_half,
                color: color,
                size: size,
              );
            } else {
              // Empty star
              return Icon(
                Icons.star_border,
                color: color,
                size: size,
              );
            }
          }),
        ),
        if (showText) ...[
          const SizedBox(width: 4),
          Text(
            rating.toStringAsFixed(1),
            style: textStyle ??
                TextStyle(
                  fontSize: size * 0.8,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
          ),
        ],
      ],
    );
  }
}
