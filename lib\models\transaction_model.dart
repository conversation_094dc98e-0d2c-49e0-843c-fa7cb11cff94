import 'package:cloud_firestore/cloud_firestore.dart';

/// Transaction model class to handle financial transactions
class TransactionModel {
  final String id;
  final String userId;
  final String type; // 'credit', 'debit', 'withdrawal_request', 'withdrawal_approved', 'withdrawal_rejected'
  final double amount;
  final String reason;
  final Map<String, dynamic> metadata;
  final double previousBalance;
  final double newBalance;
  final String? withdrawalId;
  final String? status;
  final String? processedBy;
  final DateTime createdAt;

  TransactionModel({
    required this.id,
    required this.userId,
    required this.type,
    required this.amount,
    required this.reason,
    this.metadata = const {},
    required this.previousBalance,
    required this.newBalance,
    this.withdrawalId,
    this.status,
    this.processedBy,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  // Create a TransactionModel from Firestore data
  factory TransactionModel.fromFirestore(
    DocumentSnapshot doc,
  ) {
    final data = doc.data() as Map<String, dynamic>;
    final Timestamp createdAtTimestamp = data['createdAt'] as Timestamp? ?? 
        Timestamp.fromDate(DateTime.now());

    return TransactionModel(
      id: doc.id,
      userId: data['userId'] as String? ?? '',
      type: data['type'] as String? ?? '',
      amount: (data['amount'] as num?)?.toDouble() ?? 0.0,
      reason: data['reason'] as String? ?? '',
      metadata: data['metadata'] as Map<String, dynamic>? ?? {},
      previousBalance: (data['previousBalance'] as num?)?.toDouble() ?? 0.0,
      newBalance: (data['newBalance'] as num?)?.toDouble() ?? 0.0,
      withdrawalId: data['withdrawalId'] as String?,
      status: data['status'] as String?,
      processedBy: data['processedBy'] as String?,
      createdAt: createdAtTimestamp.toDate(),
    );
  }

  // Convert TransactionModel to a Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'type': type,
      'amount': amount,
      'reason': reason,
      'metadata': metadata,
      'previousBalance': previousBalance,
      'newBalance': newBalance,
      'withdrawalId': withdrawalId,
      'status': status,
      'processedBy': processedBy,
      'createdAt': Timestamp.fromDate(createdAt),
    };
  }
}
