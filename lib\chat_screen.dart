import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:image_picker/image_picker.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:path/path.dart' as path;
import 'package:file_selector/file_selector.dart';
import 'dart:io';
import 'dart:async';

import 'public_profile_page.dart';
import 'project_progress_page.dart';
import 'services/messaging_service.dart';
import 'services/ai_moderation_service.dart';
import 'services/hire_service.dart';
import 'services/admin_financial_service.dart';
import 'services/user_content_service.dart';

class ChatScreen extends StatefulWidget {
  final Map<String, dynamic> conversation;

  const ChatScreen({
    super.key,
    required this.conversation,
  });

  @override
  ChatScreenState createState() => ChatScreenState();
}

class ChatScreenState extends State<ChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool _isTyping = false;
  bool _isSending = false;
  bool _isLoading = true;
  String? _errorMessage;

  // Services
  final MessagingService _messagingService = MessagingService();
  final AIModerationService _moderationService = AIModerationService();
  final HireService _hireService = HireService();
  final UserContentService _userContentService = UserContentService();
  Timer? _markAsReadTimer;

  // Flag to track if a message has been flagged for moderation
  bool _messageWasFlagged = false;

  // Store information about flagged messages
  Map<String, dynamic>? _flaggedMessageInfo;

  // Messages data
  List<Map<String, dynamic>> _messages = [];



  // Track if we need to show the scroll button
  bool _showScrollButton = false;

  @override
  void initState() {
    super.initState();
    _loadMessages();
    _checkHireRequestStatus();
    _testAIModeration();

    // Explicitly mark messages as read when the screen is opened
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      try {
        // First, get the current unreadCount map to preserve other users' counts
        final DocumentSnapshot conversationDoc = await FirebaseFirestore.instance
            .collection('conversations')
            .doc(widget.conversation['id'])
            .get();

        // Extract the current unreadCount map
        Map<String, dynamic> unreadCountMap = {};
        if (conversationDoc.exists) {
          final data = conversationDoc.data() as Map<String, dynamic>;
          if (data.containsKey('unreadCount') && data['unreadCount'] is Map) {
            unreadCountMap = Map<String, dynamic>.from(data['unreadCount']);
          }
        }

        // Update only the current user's unread count
        unreadCountMap[_messagingService.currentUserId.toString()] = 0;

        // Update the conversation document
        await FirebaseFirestore.instance
            .collection('conversations')
            .doc(widget.conversation['id'])
            .update({
              'unreadCount': unreadCountMap,
              'lastReadAt': FieldValue.serverTimestamp(),
            });

        // Mark messages as read and update the conversation object
        await _messagingService.markMessagesAsRead(widget.conversation['id']);

        // Force update the user's total unread counts
        await _messagingService.updateUserUnreadMessageCount();

        // Update the conversation object to reflect zero unread messages
        if (mounted) {
          setState(() {
            widget.conversation['unread'] = 0;
          });
        }

        debugPrint('ChatScreen - Marked messages as read for conversation ${widget.conversation['id']}');
      } catch (e) {
        debugPrint('ChatScreen - Error marking messages as read: $e');
      }
    });

    // Set up a timer to periodically mark messages as read
    _markAsReadTimer = Timer.periodic(const Duration(seconds: 10), (timer) async {
      if (mounted) {
        try {
          // First, get the current unreadCount map to preserve other users' counts
          final DocumentSnapshot conversationDoc = await FirebaseFirestore.instance
              .collection('conversations')
              .doc(widget.conversation['id'])
              .get();

          // Extract the current unreadCount map
          Map<String, dynamic> unreadCountMap = {};
          if (conversationDoc.exists) {
            final data = conversationDoc.data() as Map<String, dynamic>;
            if (data.containsKey('unreadCount') && data['unreadCount'] is Map) {
              unreadCountMap = Map<String, dynamic>.from(data['unreadCount']);
            }
          }

          // Update only the current user's unread count
          unreadCountMap[_messagingService.currentUserId.toString()] = 0;

          // Update the conversation document
          await FirebaseFirestore.instance
              .collection('conversations')
              .doc(widget.conversation['id'])
              .update({
                'unreadCount': unreadCountMap,
                'lastReadAt': FieldValue.serverTimestamp(),
              });

          // Mark messages as read and update the conversation object
          await _messagingService.markMessagesAsRead(widget.conversation['id']);

          // Force update the user's total unread counts
          await _messagingService.updateUserUnreadMessageCount();

          // Update only the unread count in the conversation object, not the entire state
          // This prevents the hire request details from refreshing
          if (mounted) {
            // Use a more targeted setState that only updates the unread count
            // without triggering a rebuild of the hire request details
            widget.conversation['unread'] = 0;

            // We're not calling setState here to avoid refreshing the entire UI
            // This prevents the hire request details from refreshing periodically
          }

          debugPrint('ChatScreen - Periodic mark messages as read for conversation ${widget.conversation['id']}');
        } catch (e) {
          debugPrint('ChatScreen - Error in periodic mark messages as read: $e');
        }
      }
    });

    // Add scroll listener to show/hide scroll button
    _scrollController.addListener(() {
      // Show button only when user has scrolled up
      final shouldShowButton = _scrollController.hasClients &&
          _scrollController.position.pixels < (_scrollController.position.maxScrollExtent - 300);

      if (shouldShowButton != _showScrollButton) {
        setState(() {
          _showScrollButton = shouldShowButton;
        });
      }
    });
  }

  // Test AI moderation
  Future<void> _testAIModeration() async {
    try {
      final bool isWorking = await _moderationService.testGeminiConnection();
      debugPrint('ChatScreen - AI Moderation test: ${isWorking ? 'WORKING' : 'NOT WORKING'}');

      if (!isWorking) {
        debugPrint('ChatScreen - AI Moderation is not working, will use fallback pattern-based moderation');
      }
    } catch (e) {
      debugPrint('ChatScreen - Error testing AI moderation: $e');
    }
  }

  // Check and update hire request status
  Future<void> _checkHireRequestStatus() async {
    try {
      // If this is a hire request conversation, make sure the status is correctly set
      if (widget.conversation['type'] == 'hire' || widget.conversation['hireRequestId'] != null) {
        String? hireRequestId = widget.conversation['hireRequestId'];

        // If we have a hire request ID, fetch the latest status
        if (hireRequestId != null) {
          final hireRequest = await _hireService.getHireRequest(hireRequestId);

          if (hireRequest != null && hireRequest['status'] != widget.conversation['status']) {
            // Store the hire request data in the conversation to avoid refreshing
            if (widget.conversation['cachedHireData'] == null) {
              widget.conversation['cachedHireData'] = hireRequest;
            }

            // Update the local conversation status to match the hire request
            // without triggering a full UI refresh
            widget.conversation['status'] = hireRequest['status'];
            widget.conversation['developerId'] = hireRequest['developerId'];

            // Only update the UI if the status has changed significantly
            // This prevents constant refreshing of the hire request details
            if (mounted && _isStatusChangeSignificant(widget.conversation['status'])) {
              setState(() {
                // The status and developerId are already updated above
                // This setState is just to trigger a UI refresh for significant changes
              });
            }

            debugPrint('Updated conversation status to ${hireRequest['status']}');
            debugPrint('Developer ID: ${hireRequest['developerId']}');
            debugPrint('Current user ID: ${_hireService.currentUserId}');
          }
        } else {
          // Try to find the hire request by conversation ID
          final hireRequest = await _hireService.findHireRequestByConversationId(widget.conversation['id']);

          if (hireRequest != null) {
            // Store the hire request data in the conversation to avoid refreshing
            widget.conversation['cachedHireData'] = hireRequest;

            // Update the local conversation with the hire request info
            widget.conversation['hireRequestId'] = hireRequest['id'];
            widget.conversation['status'] = hireRequest['status'];
            widget.conversation['developerId'] = hireRequest['developerId'];
            widget.conversation['type'] = 'hire';

            // Only update the UI if this is the first time we're setting the hire request
            if (mounted) {
              setState(() {
                // The fields are already updated above
                // This setState is just to trigger the initial UI refresh
              });
            }

            // Also update the conversation document in Firestore
            await FirebaseFirestore.instance
                .collection('conversations')
                .doc(widget.conversation['id'])
                .update({
                  'hireRequestId': hireRequest['id'],
                  'status': hireRequest['status'],
                  'developerId': hireRequest['developerId'],
                  'type': 'hire',
                });
          }
        }
      }
    } catch (e) {
      debugPrint('Error checking hire request status: $e');
    }
  }

  // Helper method to determine if a status change is significant enough to refresh the UI
  bool _isStatusChangeSignificant(String status) {
    // Only refresh for major status changes like accepted, rejected, completed
    return ['accepted', 'rejected', 'declined', 'completed'].contains(status.toLowerCase());
  }

  // Get color for plan type
  Color _getPlanTypeColor(String planType) {
    switch (planType.toLowerCase()) {
      case 'basic':
        return Colors.blue;
      case 'standard':
        return Colors.green;
      case 'premium':
        return Colors.purple;
      default:
        return Colors.orange;
    }
  }

  // Build hire request details widget
  Widget _buildHireRequestDetails(Map<String, dynamic> data) {
    // Extract plan type
    String planType = 'Custom';
    if (data['planType'] != null && data['planType'].toString().isNotEmpty) {
      planType = data['planType'].toString();
    } else if (data['planName'] != null) {
      final String planName = data['planName'].toString();
      if (planName.toLowerCase().contains('basic')) {
        planType = 'Basic';
      } else if (planName.toLowerCase().contains('standard')) {
        planType = 'Standard';
      } else if (planName.toLowerCase().contains('premium')) {
        planType = 'Premium';
      }
    }

    // Format budget
    String budget = 'Not specified';
    if (data['budget'] != null) {
      if (data['budget'] is num) {
        budget = '\$${(data['budget'] as num).toStringAsFixed(2)}';
      } else if (data['budget'] is String) {
        final String budgetStr = data['budget'] as String;
        if (!budgetStr.startsWith('\$')) {
          budget = '\$$budgetStr';
        } else {
          budget = budgetStr;
        }
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Plan type
        Row(
          children: [
            Icon(Icons.assignment_outlined, size: 16, color: _getPlanTypeColor(planType)),
            const SizedBox(width: 8),
            Text(
              'Plan Type:',
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: _getPlanTypeColor(planType).withAlpha(30),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                planType,
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: _getPlanTypeColor(planType),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),

        // Budget
        Row(
          children: [
            Icon(Icons.attach_money, size: 16, color: Colors.green[700]),
            const SizedBox(width: 8),
            Text(
              'Budget:',
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(width: 8),
            Text(
              budget,
              style: GoogleFonts.poppins(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Colors.green[700],
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
      ],
    );
  }

  // Load messages from Firebase
  void _loadMessages() {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    final String conversationId = widget.conversation['id'];

    // Check if this is a new conversation
    final bool isNewConversation = widget.conversation['isNew'] == true;

    // If this is a new conversation, don't add the default message
    if (isNewConversation) {
      // Mark conversation as not new to prevent showing the message again
      FirebaseFirestore.instance.collection('conversations').doc(conversationId).update({
        'isNew': false,
      }).catchError((error) {
        debugPrint('Error updating conversation: $error');
      });
    }

    // Subscribe to messages stream
    _messagingService.getMessages(conversationId).listen((messages) {
      if (mounted) {
        setState(() {
          _messages = messages;
          _isLoading = false;
        });

        // Scroll to bottom after loading messages with a slight delay to ensure rendering is complete
        Future.delayed(const Duration(milliseconds: 100), () {
          _scrollToBottom();
        });
      }
    }, onError: (error) {
      debugPrint('Error loading messages: $error');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to load messages. Please try again.';
        });
      }
    });
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();

    // Cancel the mark as read timer
    _markAsReadTimer?.cancel();

    super.dispose();
  }

  Future<void> _sendMessage() async {
    final String text = _messageController.text.trim();
    if (text.isEmpty) return;

    // Clear input field and update UI state
    _messageController.clear();
    setState(() {
      _isTyping = false;
      _isSending = true;
    });

    try {
      // Get conversation ID and receiver ID
      final String conversationId = widget.conversation['id'];
      final String? receiverId = widget.conversation['otherUserId'];

      // Check message for potential fee bypass or policy violations
      debugPrint('ChatScreen - Checking message for moderation: $text');
      final moderationResult = await _moderationService.moderateMessage(text);
      debugPrint('ChatScreen - Moderation result: ${moderationResult['flagged'] ? 'FLAGGED' : 'clean'}, severity: ${moderationResult['severity'] ?? 'none'}, reason: ${moderationResult['reason'] ?? 'none'}');

      // If the message is flagged, handle it based on severity
      if (moderationResult['flagged'] == true) {
        debugPrint('ChatScreen - Message flagged by moderation: ${moderationResult['reason']}');

        // Set the flag to track that this message was flagged
        _messageWasFlagged = true;

        // Check severity level
        final String severity = moderationResult['severity'] ?? 'minor';
        debugPrint('ChatScreen - Flagged message severity: $severity');

        // Test message for fee bypass detection
        if (text.contains('phone') || text.contains('email') || text.contains('contact') ||
            text.contains('outside') || text.contains('direct') || text.contains('fee') ||
            text.contains('payment') || text.contains('paypal') || text.contains('venmo')) {
          debugPrint('ChatScreen - Detected potential fee bypass keywords in message');

          // Force severe violation for testing
          if (text.contains('phone') && text.contains('payment')) {
            debugPrint('ChatScreen - Forcing severe violation for testing');

            // For severe violations, block the message and show a warning
            if (mounted) {
              // Remove any temporary message that might have been added
              setState(() {
                _messages.removeWhere((msg) => msg['id'].toString().startsWith('temp_'));
                _isSending = false;
              });

              // Show policy violation dialog
              _showPolicyViolationDialog(
                text: text,
                reason: 'Potential fee bypass with contact information',
                severity: 'severe',
              );
            }

            // Report the flagged message to admin
            await _messagingService.reportFlaggedMessage(
              conversationId: conversationId,
              messageText: text,
              reason: 'Potential fee bypass with contact information',
              severity: 'severe',
              receiverId: receiverId,
            );

            // Don't send the message
            return;
          }
        }

        if (severity == 'severe') {
          // For severe violations, block the message and show a warning
          if (mounted) {
            // Remove any temporary message that might have been added
            setState(() {
              _messages.removeWhere((msg) => msg['id'].toString().startsWith('temp_'));
              _isSending = false;
            });

            // Show policy violation dialog
            _showPolicyViolationDialog(
              text: text,
              reason: moderationResult['reason'] ?? 'Policy violation detected',
              severity: severity,
            );
          }

          // Report the flagged message to admin
          await _messagingService.reportFlaggedMessage(
            conversationId: conversationId,
            messageText: text,
            reason: moderationResult['reason'] ?? 'Policy violation',
            severity: severity,
            receiverId: receiverId,
          );

          // Don't send the message
          return;
        }

        // For non-severe violations, continue with sending but report to admin
        await _messagingService.reportFlaggedMessage(
          conversationId: conversationId,
          messageText: text,
          reason: moderationResult['reason'] ?? 'Policy violation',
          severity: severity,
          receiverId: receiverId,
        );

        // For moderate violations, show a warning after sending
        if (severity == 'moderate' && mounted) {
          // We'll show the warning after the message is sent
          setState(() {
            _flaggedMessageInfo = {
              'text': text,
              'reason': moderationResult['reason'] ?? 'Potential policy violation',
              'severity': severity,
            };
          });
        }
      }

      // Create a temporary message for optimistic UI update
      final tempMessage = {
        'id': 'temp_${DateTime.now().millisecondsSinceEpoch}',
        'sender': _messagingService.currentUserId,
        'text': text,
        'time': 'Sending...',
        'timestamp': Timestamp.now(),
        'isRead': false,
        'isDelivered': false,
        'type': 'text',
        'flagged': moderationResult['flagged'] == true,
      };

      // Add reply information if replying to a message
      if (_replyToMessage != null) {
        tempMessage['replyTo'] = {
          'id': _replyToMessage!['id'],
          'text': _replyToMessage!['text'],
          'sender': _replyToMessage!['sender'],
        };
      }

      // Add the temporary message to the UI
      setState(() {
        _messages.add(tempMessage); // Add to the end since we're not using reverse
      });

      // Scroll to bottom after adding the temporary message
      _scrollToBottom();

      // Check if this is a hire request conversation
      final bool isHireRequest = widget.conversation['type'] == 'hire';
      final String? hireRequestId = widget.conversation['hireRequestId'];

      // Prepare additional data for the message
      Map<String, dynamic> additionalData = {};

      // Add moderation data if message was flagged
      if (moderationResult['flagged'] == true) {
        additionalData['flagged'] = true;
        additionalData['flagReason'] = moderationResult['reason'];
        additionalData['flagSeverity'] = moderationResult['severity'];
      }

      // Add reply data if replying to a message
      if (_replyToMessage != null) {
        additionalData['replyTo'] = {
          'id': _replyToMessage!['id'],
          'text': _replyToMessage!['text'],
          'sender': _replyToMessage!['sender'],
        };
      }

      // Send message to Firebase
      final result = await _messagingService.sendMessage(
        conversationId: conversationId,
        text: text,
        receiverId: receiverId,
        additionalData: additionalData.isNotEmpty ? additionalData : null,
      );

      // Don't clear the reply immediately - we'll clear it after we confirm the message was sent successfully

      // If this is a hire request conversation, update the hire request document
      // But make this optional - if it fails, the conversation will still work
      if (isHireRequest && hireRequestId != null && hireRequestId.isNotEmpty) {
        try {
          // Update the hire request with the last message info
          await FirebaseFirestore.instance
              .collection('hire_requests')
              .doc(hireRequestId)
              .update({
                'lastMessage': text,
                'lastMessageTimestamp': FieldValue.serverTimestamp(),
                'lastMessageSender': _messagingService.currentUserId,
              });
        } catch (e) {
          // Log the error but continue - this is not critical
          debugPrint('Error updating hire request: $e');
          // The conversation will still work even if this fails
        }
      }

      if (result['success']) {
        // Show warning for flagged message if it was moderate severity
        if (_flaggedMessageInfo != null && _flaggedMessageInfo!['severity'] == 'moderate') {
          _showFlaggedMessageWarning();
        }

        // Update the conversation's last message in the widget
        widget.conversation['lastMessage'] = text;
        widget.conversation['time'] = 'Just now';

        // We've already added the reply information to the message in additionalData
        // So we can safely clear the reply UI now

        // Clear the reply UI after sending a message
        setState(() {
          _replyToMessage = null;
        });

        // The real message will be added by the stream listener, so we don't need to update _messages here
      } else {
        // Remove the temporary message if sending failed
        setState(() {
          _messages.removeWhere((msg) => msg['id'] == tempMessage['id']);
        });

        // Show error
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Error sending message: ${result['error']}',
                style: GoogleFonts.poppins(),
              ),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Error sending message: $e');

      // Show error
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error sending message: $e',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      // Update UI state
      if (mounted) {
        setState(() {
          _isSending = false;
        });
      }
    }
  }

  // Build report option
  Widget _buildReportOption(String text) {
    return InkWell(
      onTap: () {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Reported for $text'),
            behavior: SnackBarBehavior.floating,
            backgroundColor: Colors.red,
          ),
        );
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Text(
          text,
          style: GoogleFonts.poppins(),
        ),
      ),
    );
  }

  // Clear chat history
  Future<void> _clearChatHistory() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final String conversationId = widget.conversation['id'];

      // Delete all messages in the conversation
      final QuerySnapshot messagesSnapshot = await FirebaseFirestore.instance
          .collection('conversations')
          .doc(conversationId)
          .collection('messages')
          .get();

      // Create a batch to delete all messages
      final WriteBatch batch = FirebaseFirestore.instance.batch();
      for (final doc in messagesSnapshot.docs) {
        batch.delete(doc.reference);
      }

      // Commit the batch
      await batch.commit();

      // Update conversation with empty last message
      await FirebaseFirestore.instance.collection('conversations').doc(conversationId).update({
        'lastMessage': 'No messages',
        'lastMessageTimestamp': FieldValue.serverTimestamp(),
      });

      // Reload messages
      _loadMessages();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Chat history cleared'),
            behavior: SnackBarBehavior.floating,
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error clearing chat history: $e');

      // Show error
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error clearing chat history: $e'),
            behavior: SnackBarBehavior.floating,
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      // Update UI state
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Check if a user is online
  Future<bool> _checkUserOnlineStatus(String? userId) async {
    if (userId == null) return false;

    try {
      // Get the user's online status from Firestore
      final userDoc = await FirebaseFirestore.instance.collection('users').doc(userId).get();
      if (!userDoc.exists) return false;

      final userData = userDoc.data() as Map<String, dynamic>;

      // Check if the user has been active in the last 5 minutes
      final lastActive = userData['lastActive'] as Timestamp?;
      if (lastActive == null) return false;

      final now = Timestamp.now();
      final difference = now.seconds - lastActive.seconds;

      // Consider user online if active in the last 3 minutes (more accurate)
      return difference < 180; // 3 minutes = 180 seconds
    } catch (e) {
      debugPrint('Error checking user online status: $e');
      return false;
    }
  }

  // Get a user's name by ID
  Future<String> _getUserName(String? userId) async {
    if (userId == null) return 'User';

    try {
      final DocumentSnapshot userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(userId)
          .get();

      if (!userDoc.exists) return 'User';

      final userData = userDoc.data() as Map<String, dynamic>;
      final String name = userData['name'] ?? userData['displayName'] ?? 'User';
      return name;
    } catch (e) {
      debugPrint('Error getting user name: $e');
      return 'User';
    }
  }

  // Get file icon based on file extension
  IconData _getFileIcon(String fileExtension) {
    switch (fileExtension.toLowerCase()) {
      case '.pdf':
        return Icons.picture_as_pdf;
      case '.doc':
      case '.docx':
        return Icons.description;
      case '.xls':
      case '.xlsx':
        return Icons.table_chart;
      case '.txt':
        return Icons.text_snippet;
      case '.jpg':
      case '.jpeg':
      case '.png':
      case '.gif':
        return Icons.image;
      case '.mp4':
      case '.mov':
      case '.avi':
        return Icons.video_file;
      case '.mp3':
      case '.wav':
      case '.aac':
        return Icons.audio_file;
      default:
        return Icons.insert_drive_file;
    }
  }

  // Show custom offer dialog
  void _showCustomOfferDialog() {
    final TextEditingController priceController = TextEditingController();
    final TextEditingController descriptionController = TextEditingController();
    final TextEditingController deliveryTimeController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Send Custom Offer',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Create a custom offer for this client',
                style: GoogleFonts.poppins(),
              ),
              const SizedBox(height: 16),

              // Price field
              Text(
                'Price (USD)',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: priceController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  hintText: 'Enter price',
                  prefixText: '\$ ',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Delivery time field
              Text(
                'Delivery Time',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: deliveryTimeController,
                decoration: InputDecoration(
                  hintText: 'e.g., 3 days',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Description field
              Text(
                'Description',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: descriptionController,
                maxLines: 3,
                decoration: InputDecoration(
                  hintText: 'Describe what you will deliver',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(
                color: Colors.grey[700],
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              // Validate inputs
              final price = priceController.text.trim();
              final description = descriptionController.text.trim();
              final deliveryTime = deliveryTimeController.text.trim();

              if (price.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please enter a price'),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
                return;
              }

              if (description.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please enter a description'),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
                return;
              }

              // Close dialog
              Navigator.pop(context);

              // Send custom offer message
              _sendCustomOffer(
                price: price,
                description: description,
                deliveryTime: deliveryTime,
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF6A11CB),
            ),
            child: Text(
              'Send Offer',
              style: GoogleFonts.poppins(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Send custom offer
  Future<void> _sendCustomOffer({
    required String price,
    required String description,
    String? deliveryTime,
  }) async {
    setState(() {
      _isSending = true;
    });

    try {
      // Get conversation ID and receiver ID
      final String conversationId = widget.conversation['id'];
      final String? receiverId = widget.conversation['otherUserId'];

      // Create a temporary message ID
      final String tempId = 'temp_${DateTime.now().millisecondsSinceEpoch}';

      // Create a temporary message for optimistic UI update
      final tempMessage = {
        'id': tempId,
        'sender': _messagingService.currentUserId,
        'text': description,
        'time': 'Sending...',
        'timestamp': Timestamp.now(),
        'isRead': false,
        'isDelivered': false,
        'type': 'custom_offer',
        'isCustomOffer': true,
        'offerPrice': price,
        'offerStatus': 'pending',
        'offerDeliveryTime': deliveryTime,
      };

      // Add the temporary message to the UI
      setState(() {
        _messages.add(tempMessage); // Add to the end since we're not using reverse
      });

      // Scroll to bottom after adding the temporary message
      _scrollToBottom();

      // Create message data with all required fields
      final Map<String, dynamic> messageData = {
        'text': description,
        'isCustomOffer': true,
        'offerPrice': price,
        'offerStatus': 'pending',
        'timestamp': FieldValue.serverTimestamp(),
        'senderId': _messagingService.currentUserId,
        'type': 'custom_offer',
      };

      // Add delivery time if provided
      if (deliveryTime != null && deliveryTime.isNotEmpty) {
        messageData['offerDeliveryTime'] = deliveryTime;
      }

      // Log the custom offer data for debugging
      debugPrint('Sending custom offer: $messageData');

      // Send notification to the receiver
      if (receiverId != null) {
        final currentUserDoc = await FirebaseFirestore.instance.collection('users').doc(_messagingService.currentUserId).get();
        final currentUserData = currentUserDoc.data() ?? {};
        final currentUserName = currentUserData['displayName'] ?? 'Someone';

        // Send notification
        await FirebaseFirestore.instance.collection('notifications').add({
          'userId': receiverId,
          'title': 'New Custom Offer',
          'body': '$currentUserName sent you a custom offer for \$$price',
          'type': 'custom_offer',
          'read': false,
          'timestamp': FieldValue.serverTimestamp(),
          'data': {
            'conversationId': conversationId,
            'senderId': _messagingService.currentUserId,
            'senderName': currentUserName,
            'price': price,
          },
        });
      }

      // Send message directly to Firestore to ensure all fields are set correctly
      final messageRef = FirebaseFirestore.instance
          .collection('conversations')
          .doc(conversationId)
          .collection('messages')
          .doc();

      // Add message ID to the data
      messageData['id'] = messageRef.id;
      messageData['sender'] = _messagingService.currentUserId;
      messageData['receiver'] = receiverId;
      messageData['isRead'] = false;
      messageData['time'] = DateTime.now().toString();

      // Save the message to Firestore
      await messageRef.set(messageData);

      // Update conversation with last message
      await FirebaseFirestore.instance
          .collection('conversations')
          .doc(conversationId)
          .update({
        'lastMessage': "Custom offer: $price${deliveryTime != null ? ' ($deliveryTime)' : ''} - $description",
        'lastMessageTimestamp': FieldValue.serverTimestamp(),
        'lastMessageSender': _messagingService.currentUserId,
      });

      // Remove the temporary message since the real one will be added by the stream listener
      setState(() {
        _messages.removeWhere((msg) => msg['id'] == tempId);
      });

      // Update the conversation's last message in the widget
      widget.conversation['lastMessage'] = "Custom offer: $price${deliveryTime != null ? ' ($deliveryTime)' : ''} - $description";
      widget.conversation['time'] = 'Just now';
    } catch (e) {
      debugPrint('Error sending custom offer: $e');

      // Remove the temporary message if sending failed
      setState(() {
        _messages.removeWhere((msg) => msg['id'].toString().startsWith('temp_'));
      });

      // Show error
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error sending offer: $e',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      // Update UI state
      if (mounted) {
        setState(() {
          _isSending = false;
        });
      }
    }
  }

  // Show policy violation dialog for severe violations
  void _showPolicyViolationDialog({
    required String text,
    required String reason,
    required String severity,
  }) {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text(
          'Message Blocked',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
            color: Colors.red[700],
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Your message has been blocked because it appears to violate our platform policies:',
              style: GoogleFonts.poppins(),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Text(
                text,
                style: GoogleFonts.poppins(
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Reason: $reason',
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.w500,
                color: Colors.red[700],
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Asatu takes platform policy violations seriously. Repeated violations may result in account suspension.',
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: Colors.grey[700],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: Text(
              'I Understand',
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Show warning dialog for policy violations that allow sending
  Future<bool> _showPolicyWarningDialog({
    required String messageText,
    required String reason,
    required String severity,
  }) async {
    if (!mounted) return false;

    final completer = Completer<bool>();

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text(
          'Policy Warning',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
            color: Colors.orange[700],
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Your message may violate our platform policies:',
              style: GoogleFonts.poppins(),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Text(
                messageText,
                style: GoogleFonts.poppins(
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Reason: $reason',
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.w500,
                color: Colors.orange[700],
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Asatu requires all transactions to be conducted through the platform. Bypassing platform fees is a violation of our terms of service.',
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: Colors.grey[700],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              completer.complete(false); // Don't send the message
            },
            child: Text(
              'Edit Message',
              style: GoogleFonts.poppins(),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              completer.complete(true); // Send the message anyway
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange[700],
            ),
            child: Text(
              'Send Anyway',
              style: GoogleFonts.poppins(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );

    return completer.future;
  }

  // Show warning for flagged message
  void _showFlaggedMessageWarning() {
    if (_flaggedMessageInfo == null || !mounted) return;

    final String severity = _flaggedMessageInfo!['severity'];
    final String reason = _flaggedMessageInfo!['reason'];

    // Only show warning for moderate or severe violations
    if (severity == 'minor') return;

    // Show warning dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Message Flagged',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
            color: severity == 'severe' ? Colors.red[700] : Colors.orange[700],
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Your message has been flagged for review:',
              style: GoogleFonts.poppins(),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Text(
                reason,
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Please review our platform policies. Repeated violations may result in account restrictions.',
              style: GoogleFonts.poppins(
                fontSize: 13,
                color: Colors.grey[700],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Dismiss',
              style: GoogleFonts.poppins(
                color: const Color(0xFF6A11CB),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Navigate to platform policies page
            },
            child: Text(
              'View Policies',
              style: GoogleFonts.poppins(
                color: const Color(0xFF6A11CB),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _scrollToBottom() {
    // Since we're not using reverse anymore, scroll to the maximum extent
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeOut,
      );
    }
  }



  // Accept hire request
  Future<void> _acceptHireRequest() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get the hire request ID from the conversation
      String? hireRequestId = widget.conversation['hireRequestId'];

      // If hireRequestId is null, try to find it in the messages
      if (hireRequestId == null) {
        // Find the hire request message
        final hireRequestMessage = _messages.firstWhere(
          (message) => message['isHireRequest'] == true,
          orElse: () => <String, dynamic>{},
        );

        if (hireRequestMessage.isNotEmpty && hireRequestMessage['hireRequestId'] != null) {
          // Use the hireRequestId from the message
          hireRequestId = hireRequestMessage['hireRequestId'];
        } else if (hireRequestMessage.isNotEmpty && hireRequestMessage['id'] != null) {
          // As a fallback, use the message ID
          hireRequestId = hireRequestMessage['id'];
        }

        // If we found a hireRequestId, update the conversation
        if (hireRequestId != null) {
          await FirebaseFirestore.instance
              .collection('conversations')
              .doc(widget.conversation['id'])
              .update({
                'hireRequestId': hireRequestId,
              });
        }
      }

      // If still null, try to query the hire_requests collection
      if (hireRequestId == null) {
        final QuerySnapshot hireRequests = await FirebaseFirestore.instance
            .collection('hire_requests')
            .where('conversationId', isEqualTo: widget.conversation['id'])
            .limit(1)
            .get();

        if (hireRequests.docs.isNotEmpty) {
          hireRequestId = hireRequests.docs.first.id;

          // Update the conversation with the found hire request ID
          await FirebaseFirestore.instance
              .collection('conversations')
              .doc(widget.conversation['id'])
              .update({
                'hireRequestId': hireRequestId,
              });
        }
      }

      if (hireRequestId == null) {
        throw Exception('Hire request ID not found');
      }

      // Call the hire service to accept the request
      final result = await _hireService.acceptHireRequest(hireRequestId);

      if (result['success']) {
        // Update conversation status in Firestore
        await FirebaseFirestore.instance
            .collection('conversations')
            .doc(widget.conversation['id'])
            .update({
              'status': 'accepted',
              'lastStatusUpdate': FieldValue.serverTimestamp(),
            });

        // Update the hire request message status
        final hireRequestMessage = _messages.firstWhere(
          (message) => message['isHireRequest'] == true,
          orElse: () => <String, dynamic>{},
        );

        if (hireRequestMessage.isNotEmpty && hireRequestMessage['id'] != null) {
          await FirebaseFirestore.instance
              .collection('conversations')
              .doc(widget.conversation['id'])
              .collection('messages')
              .doc(hireRequestMessage['id'])
              .update({
                'status': 'accepted',
              });
        }

        // Get the developer's name
        final String developerName = await _getUserName(_hireService.currentUserId);

        // Send a message to the conversation with the developer's name
        await _messagingService.sendMessage(
          conversationId: widget.conversation['id'],
          text: "$developerName has accepted your hire request. You can now discuss the project details.",
          receiverId: widget.conversation['otherUserId'],
          additionalData: {
            'type': 'system',
            'systemAction': 'hire_request_accepted',
            'actorId': _hireService.currentUserId,
          },
        );

        // Update local state
        setState(() {
          widget.conversation['status'] = 'accepted';
        });

        // Reload messages to show updated status
        _loadMessages();

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Hire request accepted successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error accepting hire request: ${result['error']}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Error accepting hire request: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error accepting hire request: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Decline hire request
  Future<void> _declineHireRequest() async {
    final TextEditingController reasonController = TextEditingController();

    // Show dialog to get decline reason
    final String? reason = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Decline Hire Request',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Please provide a reason for declining this hire request:',
              style: GoogleFonts.poppins(),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: InputDecoration(
                hintText: 'Enter reason',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              final reason = reasonController.text.trim();
              if (reason.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please provide a reason'),
                    backgroundColor: Colors.red,
                  ),
                );
                return;
              }
              Navigator.pop(context, reason);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: Text(
              'Decline',
              style: GoogleFonts.poppins(
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );

    if (reason == null || reason.isEmpty) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Get the hire request ID from the conversation
      String? hireRequestId = widget.conversation['hireRequestId'];

      // If hireRequestId is null, try to find it in the messages
      if (hireRequestId == null) {
        // Find the hire request message
        final hireRequestMessage = _messages.firstWhere(
          (message) => message['isHireRequest'] == true,
          orElse: () => <String, dynamic>{},
        );

        if (hireRequestMessage.isNotEmpty && hireRequestMessage['hireRequestId'] != null) {
          // Use the hireRequestId from the message
          hireRequestId = hireRequestMessage['hireRequestId'];
        } else if (hireRequestMessage.isNotEmpty && hireRequestMessage['id'] != null) {
          // As a fallback, use the message ID
          hireRequestId = hireRequestMessage['id'];
        }

        // If we found a hireRequestId, update the conversation
        if (hireRequestId != null) {
          await FirebaseFirestore.instance
              .collection('conversations')
              .doc(widget.conversation['id'])
              .update({
                'hireRequestId': hireRequestId,
              });
        }
      }

      // If still null, try to query the hire_requests collection
      if (hireRequestId == null) {
        final QuerySnapshot hireRequests = await FirebaseFirestore.instance
            .collection('hire_requests')
            .where('conversationId', isEqualTo: widget.conversation['id'])
            .limit(1)
            .get();

        if (hireRequests.docs.isNotEmpty) {
          hireRequestId = hireRequests.docs.first.id;

          // Update the conversation with the found hire request ID
          await FirebaseFirestore.instance
              .collection('conversations')
              .doc(widget.conversation['id'])
              .update({
                'hireRequestId': hireRequestId,
              });
        }
      }

      if (hireRequestId == null) {
        throw Exception('Hire request ID not found');
      }

      final result = await _hireService.declineHireRequest(hireRequestId, reason);

      if (result['success']) {
        // Update conversation status in Firestore
        await FirebaseFirestore.instance
            .collection('conversations')
            .doc(widget.conversation['id'])
            .update({
              'status': 'declined',
              'lastStatusUpdate': FieldValue.serverTimestamp(),
            });

        // Update the hire request message status
        final hireRequestMessage = _messages.firstWhere(
          (message) => message['isHireRequest'] == true,
          orElse: () => <String, dynamic>{},
        );

        if (hireRequestMessage.isNotEmpty && hireRequestMessage['id'] != null) {
          await FirebaseFirestore.instance
              .collection('conversations')
              .doc(widget.conversation['id'])
              .collection('messages')
              .doc(hireRequestMessage['id'])
              .update({
                'status': 'declined',
                'declineReason': reason,
              });
        }

        // Get the developer's name
        final String developerName = await _getUserName(_hireService.currentUserId);

        // Send a message to the conversation with the developer's name
        await _messagingService.sendMessage(
          conversationId: widget.conversation['id'],
          text: "$developerName has declined your hire request. Reason: $reason",
          receiverId: widget.conversation['otherUserId'],
          additionalData: {
            'type': 'system',
            'systemAction': 'hire_request_declined',
            'declineReason': reason,
            'actorId': _hireService.currentUserId,
          },
        );

        // Update local state
        setState(() {
          widget.conversation['status'] = 'declined';
        });

        // Reload messages to show updated status
        _loadMessages();

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Hire request declined'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error declining hire request: ${result['error']}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Error declining hire request: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error declining hire request: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final bool isHireRequest = widget.conversation['type'] == 'hire';
    final String status = widget.conversation['status'] ?? '';

    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black87),
          onPressed: () => Navigator.pop(context),
        ),
        title: InkWell(
          onTap: () {
            // Navigate to public profile with the other user's ID
            if (widget.conversation['otherUserId'] != null) {
              debugPrint('ChatScreen - Navigating to public profile for user: ${widget.conversation['otherUserId']}');
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => PublicProfilePage(
                    userId: widget.conversation['otherUserId'],
                  ),
                ),
              );
            } else {
              debugPrint('ChatScreen - Cannot navigate to profile: otherUserId is null');
            }
          },
          child: Row(
            children: [
              Stack(
                children: [
                  CircleAvatar(
                    radius: 18,
                    backgroundColor: Colors.grey[200],
                    child: Text(
                      widget.conversation['name'].substring(0, 1),
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF6A11CB),
                      ),
                    ),
                  ),
                  // Only show online indicator if user is actually online
                  // We'll check this in real-time instead of using the passed value
                  FutureBuilder<bool>(
                    future: _checkUserOnlineStatus(widget.conversation['otherUserId']),
                    builder: (context, snapshot) {
                      final bool isOnline = snapshot.data ?? false;
                      return Positioned(
                        right: 0,
                        bottom: 0,
                        child: Container(
                          width: 10,
                          height: 10,
                          decoration: BoxDecoration(
                            color: isOnline ? Colors.green : Colors.grey,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: Colors.white,
                              width: 2,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
              const SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.conversation['name'],
                    style: GoogleFonts.poppins(
                      color: Colors.black87,
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                  FutureBuilder<bool>(
                    future: _checkUserOnlineStatus(widget.conversation['otherUserId']),
                    builder: (context, snapshot) {
                      final bool isOnline = snapshot.data ?? false;
                      return Text(
                        isOnline ? 'Online' : 'Offline',
                        style: GoogleFonts.poppins(
                          color: isOnline ? Colors.green : Colors.grey[600],
                          fontSize: 12,
                        ),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
        actions: [
          // Project progress button (only for hire requests)
          if (isHireRequest && status == 'accepted')
            IconButton(
              icon: const Icon(Icons.work_outline, color: Colors.black87),
              tooltip: 'Project Progress',
              onPressed: () {
                // Use a separate method to handle async operations
                _navigateToProjectProgress(context);
              },
            ),

          // More options button
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'view_profile':
                  // Navigate to user profile
                  if (widget.conversation['otherUserId'] != null) {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => PublicProfilePage(
                          userId: widget.conversation['otherUserId'],
                        ),
                      ),
                    );
                  }
                  break;
                case 'clear_chat':
                  // Show confirmation dialog
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: Text(
                        'Clear Chat History',
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      content: Text(
                        'Are you sure you want to clear all messages in this conversation? This action cannot be undone.',
                        style: GoogleFonts.poppins(),
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: Text(
                            'Cancel',
                            style: GoogleFonts.poppins(),
                          ),
                        ),
                        TextButton(
                          onPressed: () {
                            Navigator.pop(context);
                            // Clear chat history
                            _clearChatHistory();
                          },
                          child: Text(
                            'Clear',
                            style: GoogleFonts.poppins(
                              color: Colors.red,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                  break;
                case 'report':
                  // Show report dialog
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: Text(
                        'Report Conversation',
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      content: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Why are you reporting this conversation?',
                            style: GoogleFonts.poppins(),
                          ),
                          const SizedBox(height: 16),
                          _buildReportOption('Inappropriate content'),
                          _buildReportOption('Spam or scam'),
                          _buildReportOption('Harassment or bullying'),
                          _buildReportOption('Impersonation'),
                          _buildReportOption('Other'),
                        ],
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: Text(
                            'Cancel',
                            style: GoogleFonts.poppins(),
                          ),
                        ),
                      ],
                    ),
                  );
                  break;
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem<String>(
                value: 'view_profile',
                child: Row(
                  children: [
                    const Icon(Icons.person, color: Color(0xFF6A11CB)),
                    const SizedBox(width: 12),
                    Text(
                      'View Profile',
                      style: GoogleFonts.poppins(),
                    ),
                  ],
                ),
              ),
              PopupMenuItem<String>(
                value: 'clear_chat',
                child: Row(
                  children: [
                    const Icon(Icons.delete_sweep, color: Colors.orange),
                    const SizedBox(width: 12),
                    Text(
                      'Clear Chat History',
                      style: GoogleFonts.poppins(),
                    ),
                  ],
                ),
              ),
              PopupMenuItem<String>(
                value: 'report',
                child: Row(
                  children: [
                    const Icon(Icons.report_problem, color: Colors.red),
                    const SizedBox(width: 12),
                    Text(
                      'Report',
                      style: GoogleFonts.poppins(),
                    ),
                  ],
                ),
              ),
            ],
            icon: const Icon(Icons.more_vert, color: Colors.black87),
          ),
        ],
      ),
      body: Column(
        children: [
          // Hire request info banner
          if (isHireRequest) _buildHireRequestBanner(status),

          // Messages list
          Expanded(
            child: _isLoading
                ? _buildLoadingIndicator()
                : _errorMessage != null
                    ? _buildErrorMessage()
                    : Stack(
                        children: [
                          Column(
                            children: [
                              Expanded(
                                child: ListView.builder(
                                  controller: _scrollController,
                                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
                                  itemCount: _messages.length,
                                  reverse: false, // Don't reverse the list
                                  itemBuilder: (context, index) {
                                    final message = _messages[index];
                                    return _buildMessageBubble(message);
                                  },
                                ),
                              ),
                            ],
                          ),
                          // Add a scroll to bottom button (only when needed)
                          if (_showScrollButton)
                            Positioned(
                              right: 16,
                              bottom: 16,
                              child: FloatingActionButton(
                                mini: true,
                                backgroundColor: Colors.white,
                                elevation: 2,
                                onPressed: _scrollToBottom,
                                child: const Icon(
                                  Icons.keyboard_arrow_down,
                                  color: Color(0xFF6A11CB),
                                ),
                              ),
                            ),
                        ],
                      ),
          ),

          // Message input
          _buildMessageInput(),
        ],
      ),
    );
  }

  Widget _buildHireRequestBanner(String status) {
    Color bannerColor;
    IconData bannerIcon;
    String bannerText;

    // Check if current user is the developer
    final bool isCurrentUserDeveloper = _hireService.currentUserId == widget.conversation['developerId'];

    // Cache the status to avoid refreshing
    if (widget.conversation['cachedStatus'] == null) {
      widget.conversation['cachedStatus'] = status;
    }

    // Use the cached status if available, unless there's a significant change
    final String statusToUse = _isStatusChangeSignificant(status) ?
        status : (widget.conversation['cachedStatus'] ?? status);

    switch (statusToUse.toLowerCase()) {
      case 'pending':
        bannerColor = Colors.orange[700]!;
        bannerIcon = Icons.pending_outlined;
        if (isCurrentUserDeveloper) {
          bannerText = 'Hire request pending. Accept or decline this request.';
        } else {
          bannerText = 'Hire request pending. Waiting for developer to respond.';
        }
        break;
      case 'accepted':
        bannerColor = Colors.green[700]!;
        bannerIcon = Icons.check_circle_outline;
        bannerText = 'This hire request has been accepted. Discuss project details below.';
        break;
      case 'rejected':
      case 'declined':
        bannerColor = Colors.red[700]!;
        bannerIcon = Icons.cancel_outlined;
        bannerText = 'This hire request has been declined.';
        break;
      case 'in_progress':
        bannerColor = Colors.blue[700]!;
        bannerIcon = Icons.timelapse;
        bannerText = 'Project in progress. Track milestones in the hire tab.';
        break;
      case 'submitted':
        bannerColor = Colors.purple[700]!;
        bannerIcon = Icons.task_alt;
        bannerText = 'Work has been submitted. Review and accept in the hire tab.';
        break;
      case 'completed':
        bannerColor = Colors.teal[700]!;
        bannerIcon = Icons.done_all;
        bannerText = 'Project completed. Leave a review in the hire tab.';
        break;
      default:
        bannerColor = Colors.grey[700]!;
        bannerIcon = Icons.info_outline;
        bannerText = 'Hire request';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      color: bannerColor.withAlpha(25),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(bannerIcon, color: bannerColor, size: 20),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  bannerText,
                  style: GoogleFonts.poppins(
                    fontSize: 13,
                    color: bannerColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),

          // Show project progress button for accepted or completed projects
          if ((status.toLowerCase() == 'accepted' || status.toLowerCase() == 'completed') &&
              widget.conversation['hireRequestId'] != null) ...[
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                icon: Icon(
                  status.toLowerCase() == 'completed' && isCurrentUserDeveloper
                      ? Icons.star_rate
                      : Icons.track_changes,
                  size: 16,
                  color: bannerColor,
                ),
                label: Text(
                  status.toLowerCase() == 'completed'
                      ? (isCurrentUserDeveloper ? 'View Project Review' : 'View Project Details')
                      : (isCurrentUserDeveloper ? 'Update Project Progress' : 'View Project Progress'),
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: bannerColor,
                  ),
                ),
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: bannerColor),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 8),
                ),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ProjectProgressPage(
                        hireRequestId: widget.conversation['hireRequestId'],
                        conversation: widget.conversation,
                      ),
                    ),
                  );
                },
              ),
            ),
          ],

          // Show hire request details for pending requests
          if (status.toLowerCase() == 'pending') ...[
            const SizedBox(height: 8),
            const Divider(height: 1, thickness: 1, color: Color(0xFFEEEEEE)),
            const SizedBox(height: 8),

            // Project details section - Use a static widget instead of FutureBuilder
            Builder(
              builder: (context) {
                // Check if we already have cached hire data
                if (widget.conversation['cachedHireData'] != null) {
                  // Use the cached data directly
                  final Map<String, dynamic> dataToUse = widget.conversation['cachedHireData'];
                  return _buildHireRequestDetails(dataToUse);
                }

                // If no cached data, fetch it once and cache it
                return FutureBuilder<DocumentSnapshot>(
                  future: FirebaseFirestore.instance
                      .collection('hire_requests')
                      .doc(widget.conversation['hireRequestId'])
                      .get(),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Center(
                        child: Padding(
                          padding: EdgeInsets.all(8.0),
                          child: SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                        ),
                      );
                    }

                    if (snapshot.hasError || !snapshot.hasData || !snapshot.data!.exists) {
                      return const SizedBox.shrink();
                    }

                    // Cache the hire data to avoid refreshing in the future
                    final hireData = snapshot.data!.data() as Map<String, dynamic>;
                    widget.conversation['cachedHireData'] = hireData;

                    // Build the UI with the data
                    return _buildHireRequestDetails(hireData);
                  },
                );
              },
            ),

            // Only show accept/decline buttons for developers
            if (widget.conversation['developerId'] != null &&
                _hireService.currentUserId == widget.conversation['developerId'])
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ElevatedButton.icon(
                    icon: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 16,
                    ),
                    label: Text(
                      'Decline',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red[700],
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      minimumSize: const Size(0, 32),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                    onPressed: _declineHireRequest,
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton.icon(
                    icon: const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 16,
                    ),
                    label: Text(
                      'Accept',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green[700],
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      minimumSize: const Size(0, 32),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                    onPressed: _acceptHireRequest,
                  ),
                ],
              ),
          ],
        ],
      ),
    );
  }



  // Store the message being replied to
  Map<String, dynamic>? _replyToMessage;

  Widget _buildMessageBubble(Map<String, dynamic> message) {
    // Check if the message is from the current user
    final bool isMe = message['sender'] == _messagingService.currentUserId;
    final bool isHireRequest = message['isHireRequest'] == true;
    final bool isCustomOffer = message['isCustomOffer'] == true;

    return Dismissible(
      key: Key(message['id']),
      direction: DismissDirection.startToEnd,
      confirmDismiss: (direction) async {
        // Set the message as the one being replied to
        setState(() {
          _replyToMessage = message;
          // Focus the text field
          FocusScope.of(context).requestFocus(FocusNode());
        });
        // Don't actually dismiss the message
        return false;
      },
      background: Container(
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.only(left: 16),
        color: Colors.blue.withAlpha(51), // 0.2 opacity = 51/255
        child: const Icon(
          Icons.reply,
          color: Colors.blue,
        ),
      ),
      child: Align(
        alignment: isMe ? Alignment.centerRight : Alignment.centerLeft,
        child: Container(
          margin: EdgeInsets.only(
            top: 8,
            bottom: 8,
            left: isMe ? 80 : 0,
            right: isMe ? 0 : 80,
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: isMe
              ? const Color(0xFF6A11CB) // Purple for current user's messages
              : (isHireRequest
                  ? Colors.orange[50] // Light orange for hire requests
                  : (isCustomOffer
                      ? Colors.green[50] // Light green for custom offers
                      : Colors.grey[100])), // Light grey for other user's messages
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(isMe ? 16 : 4),
              topRight: Radius.circular(isMe ? 4 : 16),
              bottomLeft: const Radius.circular(16),
              bottomRight: const Radius.circular(16),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(13),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
            children: [
              // Reply content if this is a reply
              if (message['replyTo'] != null)
                Container(
                  padding: const EdgeInsets.all(10),
                  margin: const EdgeInsets.only(bottom: 10),
                  decoration: BoxDecoration(
                    color: isMe
                        ? Colors.white.withAlpha(77) // More visible on purple (30% opacity)
                        : Colors.grey[300], // Darker background for better visibility
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(
                      color: isMe
                          ? Colors.white.withAlpha(102) // 40% opacity
                          : Colors.grey[400]!,
                      width: 1.5,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(10),
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.reply,
                            size: 14,
                            color: isMe ? Colors.white : Colors.blue[700],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Reply to',
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: isMe ? Colors.white : Colors.blue[700],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 6),
                        decoration: BoxDecoration(
                          color: isMe
                              ? Colors.white.withAlpha(51) // 20% opacity
                              : Colors.white.withAlpha(153), // 60% opacity
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Text(
                          message['replyTo']['text'] ?? '',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: isMe ? Colors.white : Colors.black87,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),

              // Message content based on type
              if (isCustomOffer)
                _buildCustomOfferContent(message, isMe)
              else if (isHireRequest)
                _buildHireRequestContent(message, isMe)
              else
                _buildRegularMessageContent(message, isMe),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRegularMessageContent(Map<String, dynamic> message, bool isMe) {
    return Column(
      crossAxisAlignment: isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        // Check if message is an image
        if (message['type'] == 'image' && message['fileUrl'] != null)
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: GestureDetector(
              onTap: () {
                // Show full image in dialog
                showDialog(
                  context: context,
                  builder: (context) => Dialog(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        AppBar(
                          backgroundColor: Colors.transparent,
                          elevation: 0,
                          leading: IconButton(
                            icon: const Icon(Icons.close, color: Colors.white),
                            onPressed: () => Navigator.pop(context),
                          ),
                          actions: [
                            IconButton(
                              icon: const Icon(Icons.download, color: Colors.white),
                              onPressed: () {
                                // Download image
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Downloading image...'),
                                    backgroundColor: Colors.green,
                                  ),
                                );
                                Navigator.pop(context);
                              },
                            ),
                          ],
                        ),
                        Image.network(
                          message['fileUrl'],
                          fit: BoxFit.contain,
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Center(
                              child: CircularProgressIndicator(
                                value: loadingProgress.expectedTotalBytes != null
                                    ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                                    : null,
                              ),
                            );
                          },
                          errorBuilder: (context, error, stackTrace) {
                            return const Center(
                              child: Icon(Icons.error, color: Colors.red, size: 50),
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                );
              },
              child: Image.network(
                message['fileUrl'],
                width: 200,
                height: 150,
                fit: BoxFit.cover,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return SizedBox(
                    width: 200,
                    height: 150,
                    child: Center(
                      child: CircularProgressIndicator(
                        value: loadingProgress.expectedTotalBytes != null
                            ? loadingProgress.cumulativeBytesLoaded / loadingProgress.expectedTotalBytes!
                            : null,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          isMe ? Colors.white : const Color(0xFF6A11CB),
                        ),
                      ),
                    ),
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: 200,
                    height: 150,
                    color: Colors.grey[300],
                    child: const Center(
                      child: Icon(Icons.error, color: Colors.red),
                    ),
                  );
                },
              ),
            ),
          )
        // Check if message is a document
        else if (message['type'] == 'document' && message['fileUrl'] != null)
          GestureDetector(
            onTap: () {
              // Open document
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Opening document...'),
                  backgroundColor: Colors.blue,
                ),
              );
            },
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isMe ? Colors.white.withAlpha(50) : Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    _getFileIcon(message['fileType'] ?? ''),
                    color: isMe ? Colors.white : Colors.blue,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          message['fileName'] ?? 'Document',
                          style: GoogleFonts.poppins(
                            color: isMe ? Colors.white : Colors.black87,
                            fontWeight: FontWeight.w500,
                            fontSize: 13,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 2),
                        Text(
                          'Tap to open',
                          style: GoogleFonts.poppins(
                            color: isMe ? Colors.white70 : Colors.grey[600],
                            fontSize: 11,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          )
        // Regular text message
        else
          Text(
            message['text'] ?? '',
            style: GoogleFonts.poppins(
              color: isMe ? Colors.white : Colors.black87,
              fontSize: 14,
            ),
            textAlign: isMe ? TextAlign.right : TextAlign.left,
          ),

        const SizedBox(height: 4),

        // Message timestamp and delivery status
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              message['time'] ?? '',
              style: GoogleFonts.poppins(
                color: isMe ? Colors.white70 : Colors.grey[600],
                fontSize: 10,
              ),
            ),
            if (isMe) ...[
              const SizedBox(width: 4),
              Icon(
                message['isDelivered'] == false
                  ? Icons.done
                  : (message['isRead'] ? Icons.done_all : Icons.done_all),
                size: 12,
                color: message['isRead'] == true
                  ? Colors.blue[300]!
                  : (message['isDelivered'] == true ? Colors.white70 : Colors.white54),
              ),
            ],
          ],
        ),
      ],
    );
  }

  Widget _buildCustomOfferContent(Map<String, dynamic> message, bool isMe) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: isMe ? Colors.white.withAlpha(50) : Colors.green[100],
        borderRadius: BorderRadius.circular(8),
        border: !isMe ? Border.all(color: Colors.green[200]!) : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.local_offer,
                size: 16,
                color: isMe ? Colors.white : Colors.green[800],
              ),
              const SizedBox(width: 6),
              Text(
                'Custom Offer',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.bold,
                  color: isMe ? Colors.white : Colors.green[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text(
                '\$${message['offerPrice'] ?? '0'}',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w600,
                  fontSize: 18,
                  color: isMe ? Colors.white : Colors.green[800],
                ),
              ),
              if (message['offerDeliveryTime'] != null) ...[
                const SizedBox(width: 8),
                Text(
                  '(${message['offerDeliveryTime']})',
                  style: GoogleFonts.poppins(
                    color: isMe ? Colors.white70 : Colors.green[700],
                    fontSize: 12,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 8),

          // Add status indicator for custom offers
          if (message['offerStatus'] != null)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: _getStatusColor(message['offerStatus']).withAlpha(50),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                _getStatusText(message['offerStatus']),
                style: GoogleFonts.poppins(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: _getStatusColor(message['offerStatus']),
                ),
              ),
            ),

          const SizedBox(height: 8),

          // Description
          Text(
            message['text'] ?? '',
            style: GoogleFonts.poppins(
              fontSize: 13,
              color: isMe ? Colors.white : Colors.black87,
            ),
          ),

          const SizedBox(height: 12),

          // Add accept/decline buttons for custom offers (for receiver)
          if (message['offerStatus'] == 'pending' && !isMe)
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                OutlinedButton(
                  onPressed: () => _declineCustomOffer(message['id']),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.red[700],
                    side: BorderSide(color: Colors.red[300]!),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  child: Text(
                    'Decline',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () => _acceptCustomOffer(message['id']),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF6A11CB),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  child: Text(
                    'Accept',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),

          // Add complete button for accepted custom offers (for client)
          if (message['offerStatus'] == 'accepted' &&
              !isMe &&
              message['customOfferId'] != null) ...[
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                icon: const Icon(
                  Icons.check_circle,
                  size: 16,
                ),
                label: Text(
                  'Mark as Complete',
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green[700],
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                onPressed: () => _completeCustomOffer(message['id'], message['customOfferId']),
              ),
            ),
          ],

          // Show payment details for completed custom offers
          if (message['offerStatus'] == 'completed' && message['customOfferId'] != null) ...[
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        size: 14,
                        color: Colors.green[700],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Completed',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Colors.green[700],
                        ),
                      ),
                      if (message['completedAt'] != null) ...[
                        const Spacer(),
                        Text(
                          _formatTimestamp(message['completedAt']),
                          style: GoogleFonts.poppins(
                            fontSize: 10,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ],
                  ),
                  if (isMe) ...[
                    const SizedBox(height: 4),
                    Text(
                      'Payment processed to your Asatu wallet',
                      style: GoogleFonts.poppins(
                        fontSize: 11,
                        color: Colors.green[700],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildHireRequestContent(Map<String, dynamic> message, bool isMe) {
    // Extract plan type from plan name if available
    String planType = 'Custom';
    final String planName = message['planName'] ?? 'Custom Project';

    // First check if planType is directly specified
    if (message['planType'] != null && message['planType'].toString().isNotEmpty) {
      planType = message['planType'].toString();
    }
    // Otherwise infer from plan name
    else if (planName.toLowerCase().contains('basic')) {
      planType = 'Basic';
    } else if (planName.toLowerCase().contains('standard')) {
      planType = 'Standard';
    } else if (planName.toLowerCase().contains('premium')) {
      planType = 'Premium';
    }

    // Format budget with dollar sign if needed
    String budget = 'Not specified';
    if (message['budget'] != null) {
      if (message['budget'] is num) {
        budget = '\$${(message['budget'] as num).toStringAsFixed(2)}';
      } else if (message['budget'] is String) {
        final String budgetStr = message['budget'] as String;
        if (!budgetStr.startsWith('\$')) {
          budget = '\$$budgetStr';
        } else {
          budget = budgetStr;
        }
      }
    }

    return Column(
      crossAxisAlignment: isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: MediaQuery.of(context).size.width - 80, // Prevent overflow
          ),
          child: Container(
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.only(bottom: 10),
            decoration: BoxDecoration(
              color: isMe ? Colors.white.withAlpha(50) : Colors.orange[50],
              borderRadius: BorderRadius.circular(8),
              border: !isMe ? Border.all(color: Colors.orange[200]!) : null,
            ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.work_outline,
                        size: 16,
                        color: isMe ? Colors.white : Colors.orange[800],
                      ),
                      const SizedBox(width: 6),
                      Text(
                        'Hire Request',
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.bold,
                          color: isMe ? Colors.white : Colors.orange[800],
                        ),
                      ),
                    ],
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: _getStatusColor(message['status'] ?? 'pending').withAlpha(50),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _getStatusText(message['status'] ?? 'pending'),
                      style: GoogleFonts.poppins(
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        color: _getStatusColor(message['status'] ?? 'pending'),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Project details
              if (message['projectName'] != null) ...[
                Text(
                  'Project: ${message['projectName']}',
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                    color: isMe ? Colors.white : Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
              ],

              // Budget
              if (message['budget'] != null) ...[
                Row(
                  children: [
                    Icon(
                      Icons.attach_money,
                      size: 16,
                      color: isMe ? Colors.white70 : Colors.green[700],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Budget: $budget',
                      style: GoogleFonts.poppins(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                        color: isMe ? Colors.white : Colors.green[700],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
              ],

              // Deadline
              if (message['deadline'] != null) ...[
                Row(
                  children: [
                    Icon(
                      Icons.calendar_today,
                      size: 16,
                      color: isMe ? Colors.white70 : Colors.orange[700],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Deadline: ${message['deadline']}',
                      style: GoogleFonts.poppins(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                        color: isMe ? Colors.white : Colors.orange[700],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
              ],

              // Plan details
              if (message['planName'] != null) ...[
                Row(
                  children: [
                    Icon(
                      Icons.assignment,
                      size: 16,
                      color: isMe ? Colors.white70 : Colors.blue[700],
                    ),
                    const SizedBox(width: 4),
                    Flexible(
                      child: Row(
                        children: [
                          Text(
                            'Plan: ${message['planName']}',
                            style: GoogleFonts.poppins(
                              fontWeight: FontWeight.w500,
                              fontSize: 14,
                              color: isMe ? Colors.white : Colors.blue[700],
                            ),
                          ),
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: _getPlanTypeColor(planType).withAlpha(isMe ? 70 : 30),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              planType,
                              style: GoogleFonts.poppins(
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                                color: isMe ? Colors.white : _getPlanTypeColor(planType),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
              ],

              // Description
              if (message['text'] != null && message['text'].isNotEmpty) ...[
                const Divider(height: 16),
                Text(
                  message['text'],
                  style: GoogleFonts.poppins(
                    fontSize: 13,
                    color: isMe ? Colors.white.withAlpha(230) : Colors.black87.withAlpha(204),
                  ),
                ),
              ],
            ],
          ),
        ),
        ),

        // Hire request actions - only show for developers
        if (message['status'] == 'pending' && !isMe &&
            _hireService.currentUserId == widget.conversation['developerId']) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              OutlinedButton(
                onPressed: _declineHireRequest,
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.red[700],
                  side: BorderSide(color: Colors.red[300]!),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                child: Text(
                  'Decline',
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: _acceptHireRequest,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF6A11CB),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                child: Text(
                  'Accept',
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
        ],

        // Message timestamp
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              message['time'] ?? '',
              style: GoogleFonts.poppins(
                color: isMe ? Colors.white70 : Colors.grey[600],
                fontSize: 10,
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Loading indicator
  Widget _buildLoadingIndicator() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6A11CB)),
          ),
          const SizedBox(height: 16),
          Text(
            'Loading messages...',
            style: GoogleFonts.poppins(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  // Error message
  Widget _buildErrorMessage() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[300],
          ),
          const SizedBox(height: 16),
          Text(
            _errorMessage ?? 'An error occurred',
            style: GoogleFonts.poppins(
              fontSize: 16,
              color: Colors.grey[700],
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _loadMessages,
            icon: const Icon(Icons.refresh),
            label: Text(
              'Try Again',
              style: GoogleFonts.poppins(),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF6A11CB),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // This method has been moved to line 280

  // Helper method to get status color
  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange[700]!;
      case 'accepted':
        return Colors.green[700]!;
      case 'rejected':
      case 'declined':
        return Colors.red[700]!;
      case 'in_progress':
        return Colors.blue[700]!;
      case 'submitted':
        return Colors.purple[700]!;
      case 'completed':
        return Colors.teal[700]!;
      default:
        return Colors.grey[700]!;
    }
  }

  // Helper method to get status text
  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'Pending';
      case 'accepted':
        return 'Accepted';
      case 'rejected':
      case 'declined':
        return 'Declined';
      case 'in_progress':
        return 'In Progress';
      case 'submitted':
        return 'Submitted';
      case 'completed':
        return 'Completed';
      default:
        return 'Unknown';
    }
  }

  // Format timestamp for display
  String _formatTimestamp(dynamic timestamp) {
    if (timestamp == null) return '';

    DateTime dateTime;

    if (timestamp is Timestamp) {
      dateTime = timestamp.toDate();
    } else if (timestamp is DateTime) {
      dateTime = timestamp;
    } else {
      return '';
    }

    // Format the date
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    final dateToCheck = DateTime(dateTime.year, dateTime.month, dateTime.day);

    if (dateToCheck == today) {
      // Today, show time only
      return 'Today at ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else if (dateToCheck == yesterday) {
      // Yesterday, show "Yesterday" and time
      return 'Yesterday at ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else {
      // Other days, show date and time
      final months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      return '${dateTime.day} ${months[dateTime.month - 1]} ${dateTime.year} at ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    }
  }





  // Accept custom offer
  Future<void> _acceptCustomOffer(String messageId) async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Get conversation ID
      final String conversationId = widget.conversation['id'];

      // Get the message document
      final messageDoc = await FirebaseFirestore.instance
          .collection('conversations')
          .doc(conversationId)
          .collection('messages')
          .doc(messageId)
          .get();

      if (!messageDoc.exists) {
        throw Exception('Message not found');
      }

      // Get the message data
      final messageData = messageDoc.data() as Map<String, dynamic>;

      // Create a custom offer document in the custom_offers collection
      final customOfferRef = await FirebaseFirestore.instance.collection('custom_offers').add({
        'messageId': messageId,
        'conversationId': conversationId,
        'senderId': messageData['senderId'],
        'receiverId': _messagingService.currentUserId,
        'price': messageData['offerPrice'],
        'description': messageData['text'],
        'deliveryTime': messageData['offerDeliveryTime'],
        'status': 'accepted',
        'createdAt': messageData['timestamp'],
        'acceptedAt': FieldValue.serverTimestamp(),
        'isCompleted': false,
        'isPaid': false,
        'deadline': _calculateDeadline(messageData['offerDeliveryTime']),
      });

      // Update the message with accepted status and custom offer ID
      await FirebaseFirestore.instance
          .collection('conversations')
          .doc(conversationId)
          .collection('messages')
          .doc(messageId)
          .update({
        'offerStatus': 'accepted',
        'acceptedAt': FieldValue.serverTimestamp(),
        'customOfferId': customOfferRef.id,
      });

      // Add system message about acceptance
      await _messagingService.sendMessage(
        conversationId: conversationId,
        text: 'Custom offer accepted. Work will begin now.',
        receiverId: widget.conversation['otherUserId'],
        additionalData: {
          'type': 'system',
          'systemAction': 'offer_accepted',
          'offerId': messageId,
          'customOfferId': customOfferRef.id,
        },
      );

      // Send notification to the other user
      final currentUserDoc = await FirebaseFirestore.instance.collection('users').doc(_messagingService.currentUserId).get();
      final currentUserData = currentUserDoc.data() ?? {};
      final currentUserName = currentUserData['displayName'] ?? 'Someone';

      await FirebaseFirestore.instance.collection('notifications').add({
        'userId': widget.conversation['otherUserId'],
        'title': 'Offer Accepted',
        'body': '$currentUserName accepted your custom offer',
        'type': 'offer_accepted',
        'read': false,
        'timestamp': FieldValue.serverTimestamp(),
        'data': {
          'conversationId': conversationId,
          'messageId': messageId,
          'customOfferId': customOfferRef.id,
          'senderId': _messagingService.currentUserId,
          'senderName': currentUserName,
        },
      });

      // Reload messages to show updated status
      _loadMessages();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Offer accepted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error accepting custom offer: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error accepting offer: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Calculate deadline from delivery time string
  DateTime _calculateDeadline(String? deliveryTimeStr) {
    if (deliveryTimeStr == null || deliveryTimeStr.isEmpty) {
      // Default to 7 days if no delivery time specified
      return DateTime.now().add(const Duration(days: 7));
    }

    // Parse the delivery time string
    final RegExp daysRegex = RegExp(r'(\d+)\s*days?', caseSensitive: false);
    final RegExp weeksRegex = RegExp(r'(\d+)\s*weeks?', caseSensitive: false);
    final RegExp monthsRegex = RegExp(r'(\d+)\s*months?', caseSensitive: false);

    try {
      if (daysRegex.hasMatch(deliveryTimeStr)) {
        final match = daysRegex.firstMatch(deliveryTimeStr);
        final days = int.parse(match!.group(1)!);
        return DateTime.now().add(Duration(days: days));
      } else if (weeksRegex.hasMatch(deliveryTimeStr)) {
        final match = weeksRegex.firstMatch(deliveryTimeStr);
        final weeks = int.parse(match!.group(1)!);
        return DateTime.now().add(Duration(days: weeks * 7));
      } else if (monthsRegex.hasMatch(deliveryTimeStr)) {
        final match = monthsRegex.firstMatch(deliveryTimeStr);
        final months = int.parse(match!.group(1)!);
        // Approximate months as 30 days
        return DateTime.now().add(Duration(days: months * 30));
      } else {
        // Default to 7 days if format not recognized
        return DateTime.now().add(const Duration(days: 7));
      }
    } catch (e) {
      debugPrint('Error parsing delivery time: $e');
      // Default to 7 days if parsing fails
      return DateTime.now().add(const Duration(days: 7));
    }
  }

  // Complete custom offer
  Future<void> _completeCustomOffer(String messageId, String customOfferId) async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Get conversation ID
      final String conversationId = widget.conversation['id'];

      // Get the custom offer document
      final customOfferDoc = await FirebaseFirestore.instance
          .collection('custom_offers')
          .doc(customOfferId)
          .get();

      if (!customOfferDoc.exists) {
        throw Exception('Custom offer not found');
      }

      // Get the custom offer data
      final customOfferData = customOfferDoc.data() as Map<String, dynamic>;

      // Check if the offer is already completed
      if (customOfferData['isCompleted'] == true) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('This offer has already been completed'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      // Get the price from the custom offer
      final double price = double.parse(customOfferData['price'].toString());

      // Get the developer ID (sender of the offer)
      final String developerId = customOfferData['senderId'];

      // Get the client ID (receiver of the offer)
      final String clientId = customOfferData['receiverId'];

      // Check if the current user is the client
      if (_messagingService.currentUserId != clientId) {
        throw Exception('Only the client can mark a custom offer as complete');
      }

      // Calculate the developer's earnings (80% of the price)
      final double developerEarnings = price * 0.8;

      // Process the payment using the admin financial service
      final adminFinancialService = AdminFinancialService();

      await adminFinancialService.processCustomOfferPayment(
        customOfferId: customOfferId,
        developerId: developerId,
        clientId: clientId,
        price: price,
        platformFeePercentage: 20.0,
      );

      // Update the custom offer document
      await FirebaseFirestore.instance
          .collection('custom_offers')
          .doc(customOfferId)
          .update({
        'isCompleted': true,
        'isPaid': true,
        'completedAt': FieldValue.serverTimestamp(),
        'paymentAmount': developerEarnings,
        'platformFee': price * 0.2,
      });

      // Update the message with completed status
      await FirebaseFirestore.instance
          .collection('conversations')
          .doc(conversationId)
          .collection('messages')
          .doc(messageId)
          .update({
        'offerStatus': 'completed',
        'completedAt': FieldValue.serverTimestamp(),
      });

      // Add system message about completion
      await _messagingService.sendMessage(
        conversationId: conversationId,
        text: 'Custom offer completed. Payment of \$${developerEarnings.toStringAsFixed(2)} (70% of \$${price.toStringAsFixed(2)}) has been processed to the developer\'s Asatu wallet.',
        receiverId: developerId,
        additionalData: {
          'type': 'system',
          'systemAction': 'offer_completed',
          'offerId': messageId,
          'customOfferId': customOfferId,
          'price': price,
          'developerEarnings': developerEarnings,
          'platformFee': price * 0.3,
        },
      );

      // Send notification to the developer
      await FirebaseFirestore.instance.collection('notifications').add({
        'userId': developerId,
        'title': 'Payment Received',
        'body': 'You have received \$${developerEarnings.toStringAsFixed(2)} for completing a custom offer',
        'type': 'payment_received',
        'read': false,
        'timestamp': FieldValue.serverTimestamp(),
        'data': {
          'conversationId': conversationId,
          'messageId': messageId,
          'customOfferId': customOfferId,
          'amount': developerEarnings,
          'price': price,
          'platformFee': price * 0.3,
        },
      });

      // Reload messages to show updated status
      _loadMessages();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Payment of \$${developerEarnings.toStringAsFixed(2)} has been processed to the developer'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error completing custom offer: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error completing offer: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Decline custom offer
  Future<void> _declineCustomOffer(String messageId) async {
    final TextEditingController reasonController = TextEditingController();

    // Show dialog to get reason for declining
    final String? reason = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Decline Custom Offer',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Please provide a reason for declining this offer:',
              style: GoogleFonts.poppins(),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: reasonController,
              decoration: InputDecoration(
                hintText: 'Enter reason',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, reasonController.text.trim()),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text(
              'Decline',
              style: GoogleFonts.poppins(),
            ),
          ),
        ],
      ),
    );

    // If dialog was dismissed, do nothing
    if (reason == null) return;

    try {
      setState(() {
        _isLoading = true;
      });

      // Get conversation ID
      final String conversationId = widget.conversation['id'];

      // Update the message with declined status
      await FirebaseFirestore.instance
          .collection('conversations')
          .doc(conversationId)
          .collection('messages')
          .doc(messageId)
          .update({
        'offerStatus': 'declined',
        'declinedAt': FieldValue.serverTimestamp(),
        'declineReason': reason,
      });

      // Add system message about declining
      await _messagingService.sendMessage(
        conversationId: conversationId,
        text: 'Custom offer declined${reason.isNotEmpty ? ': $reason' : ''}',
        receiverId: widget.conversation['otherUserId'],
        additionalData: {
          'type': 'system',
          'systemAction': 'offer_declined',
          'offerId': messageId,
        },
      );

      // Send notification to the other user
      final currentUserDoc = await FirebaseFirestore.instance.collection('users').doc(_messagingService.currentUserId).get();
      final currentUserData = currentUserDoc.data() ?? {};
      final currentUserName = currentUserData['displayName'] ?? 'Someone';

      await FirebaseFirestore.instance.collection('notifications').add({
        'userId': widget.conversation['otherUserId'],
        'title': 'Offer Declined',
        'body': '$currentUserName declined your custom offer',
        'type': 'offer_declined',
        'read': false,
        'timestamp': FieldValue.serverTimestamp(),
        'data': {
          'conversationId': conversationId,
          'messageId': messageId,
          'senderId': _messagingService.currentUserId,
          'senderName': currentUserName,
          'reason': reason,
        },
      });

      // Reload messages to show updated status
      _loadMessages();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Offer declined'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error declining custom offer: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error declining offer: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Show attachment options
  void _showAttachmentOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Send Attachment',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Camera option
                _buildAttachmentOption(
                  icon: Icons.camera_alt,
                  label: 'Camera',
                  color: Colors.blue,
                  onTap: () {
                    Navigator.pop(context);
                    _pickImage(ImageSource.camera);
                  },
                ),
                // Gallery option
                _buildAttachmentOption(
                  icon: Icons.photo,
                  label: 'Gallery',
                  color: Colors.purple,
                  onTap: () {
                    Navigator.pop(context);
                    _pickImage(ImageSource.gallery);
                  },
                ),
                // Document option
                _buildAttachmentOption(
                  icon: Icons.insert_drive_file,
                  label: 'Document',
                  color: Colors.orange,
                  onTap: () {
                    Navigator.pop(context);
                    _pickDocument();
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Build attachment option
  Widget _buildAttachmentOption({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircleAvatar(
            radius: 30,
            backgroundColor: color.withAlpha(50),
            child: Icon(
              icon,
              color: color,
              size: 28,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // Pick image from camera or gallery
  Future<void> _pickImage(ImageSource source) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: source,
        imageQuality: 70, // Reduce image quality to save bandwidth
      );

      if (image != null) {
        // Upload and send the image
        _uploadAndSendFile(File(image.path), 'image');
      }
    } catch (e) {
      debugPrint('Error picking image: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error picking image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Pick document
  Future<void> _pickDocument() async {
    try {
      // Define allowed document extensions
      final List<String> allowedExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt'];

      // Create XTypeGroup for file_selector
      final XTypeGroup documentsTypeGroup = XTypeGroup(
        label: 'Documents',
        extensions: allowedExtensions,
      );

      // Show file picker dialog
      final XFile? pickedFile = await openFile(
        acceptedTypeGroups: [documentsTypeGroup],
      );

      if (pickedFile != null) {
        // Show loading indicator
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Processing document...',
                    style: GoogleFonts.poppins(),
                  ),
                ],
              ),
              backgroundColor: Colors.blue,
              duration: const Duration(seconds: 1),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }

        // Create a file from the XFile
        final File file = File(pickedFile.path);

        // Check if file exists
        if (await file.exists()) {
          // Upload and send the document
          _uploadAndSendFile(file, 'document');
        } else {
          throw Exception('Selected file does not exist');
        }
      }
    } catch (e) {
      debugPrint('Error picking document: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error picking document: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }

      // Use image picker as a fallback if document picking fails
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Falling back to image picker...'),
            backgroundColor: Colors.orange,
          ),
        );

        // Wait a moment before showing the image picker
        await Future.delayed(const Duration(seconds: 1));

        // Use image picker as a fallback
        _pickImage(ImageSource.gallery);
      }
    }
  }

  // Upload and send file
  Future<void> _uploadAndSendFile(File file, String type) async {
    if (!mounted) return;

    setState(() {
      _isSending = true;
    });

    try {
      final String conversationId = widget.conversation['id'];
      final String? receiverId = widget.conversation['otherUserId'];
      final String fileName = path.basename(file.path);
      final String fileExtension = path.extension(file.path).toLowerCase();

      // Create a reference to the file location in Firebase Storage
      final storageRef = FirebaseStorage.instance
          .ref()
          .child('conversations')
          .child(conversationId)
          .child('${DateTime.now().millisecondsSinceEpoch}_$fileName');

      // Create a temporary message for optimistic UI update
      final tempMessage = {
        'id': 'temp_${DateTime.now().millisecondsSinceEpoch}',
        'sender': _messagingService.currentUserId,
        'text': 'Sending $fileName...',
        'time': 'Uploading...',
        'timestamp': Timestamp.now(),
        'isRead': false,
        'isDelivered': false,
        'type': type,
        'fileName': fileName,
        'fileType': fileExtension,
        'uploading': true,
      };

      // Add the temporary message to the UI
      setState(() {
        _messages.add(tempMessage);
      });

      // Scroll to bottom after adding the temporary message
      _scrollToBottom();

      // Upload the file to Firebase Storage
      final uploadTask = storageRef.putFile(file);

      // Show upload progress
      uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
        final progress = snapshot.bytesTransferred / snapshot.totalBytes;
        debugPrint('Upload progress: ${(progress * 100).toStringAsFixed(2)}%');
      });

      // Wait for the upload to complete
      await uploadTask.whenComplete(() => null);

      // Get the download URL
      final downloadUrl = await storageRef.getDownloadURL();

      // Remove the temporary message
      setState(() {
        _messages.removeWhere((msg) => msg['id'] == tempMessage['id']);
      });

      // Send the message with the file URL
      final result = await _messagingService.sendMessage(
        conversationId: conversationId,
        text: type == 'image' ? 'Sent an image' : 'Sent a file: $fileName',
        receiverId: receiverId,
        additionalData: {
          'fileUrl': downloadUrl,
          'fileName': fileName,
          'fileType': fileExtension,
          'type': type,
        },
      );

      if (!result['success']) {
        throw Exception(result['error'] ?? 'Failed to send file message');
      }

      // Update the conversation's last message in the widget
      widget.conversation['lastMessage'] = type == 'image' ? 'Sent an image' : 'Sent a file';
      widget.conversation['time'] = 'Just now';

    } catch (e) {
      debugPrint('Error uploading and sending file: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sending file: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSending = false;
        });
      }
    }
  }

  // Navigate to project progress page
  void _navigateToProjectProgress(BuildContext contextArg) {
    // Show loading indicator
    setState(() {
      _isLoading = true;
    });

    // Find the hire request ID
    _findHireRequestId().then((String? hireRequestId) {
      if (!mounted) return;

      // Hide loading indicator
      setState(() {
        _isLoading = false;
      });

      // Check if we found a hire request ID
      if (hireRequestId != null) {
        // Store the hire request ID in the conversation object for future use
        widget.conversation['hireRequestId'] = hireRequestId;

        // Navigate to project progress page using a synchronous approach
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            Navigator.push(
              contextArg,
              MaterialPageRoute(
                builder: (context) => ProjectProgressPage(
                  conversation: widget.conversation,
                  hireRequestId: hireRequestId,
                ),
              ),
            );
          }
        });
      } else {
        // Show error message
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            ScaffoldMessenger.of(contextArg).showSnackBar(
              const SnackBar(
                content: Text('Could not find hire request details'),
                backgroundColor: Colors.red,
              ),
            );
          }
        });
      }
    }).catchError((e) {
      if (!mounted) return;

      debugPrint('Error navigating to project progress: $e');

      // Hide loading indicator
      setState(() {
        _isLoading = false;
      });

      // Show error message
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          ScaffoldMessenger.of(contextArg).showSnackBar(
            SnackBar(
              content: Text('Error: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      });
    });
  }

  // Helper method to find the hire request ID
  Future<String?> _findHireRequestId() async {
    String? hireRequestId;

    // First check if the conversation has a hireRequestId field
    if (widget.conversation.containsKey('hireRequestId') &&
        widget.conversation['hireRequestId'] != null) {
      hireRequestId = widget.conversation['hireRequestId'];
    } else {
      // Try to find it in the messages
      final hireRequestMessage = _messages.firstWhere(
        (message) => message['isHireRequest'] == true,
        orElse: () => <String, dynamic>{},
      );

      if (hireRequestMessage.isNotEmpty && hireRequestMessage['hireRequestId'] != null) {
        hireRequestId = hireRequestMessage['hireRequestId'];
      } else if (hireRequestMessage.isNotEmpty && hireRequestMessage['id'] != null) {
        // As a fallback, use the message ID
        hireRequestId = hireRequestMessage['id'];
      }

      // If still null, use the hire service to find the hire request by conversation ID
      if (hireRequestId == null) {
        final hireRequest = await _hireService.findHireRequestByConversationId(widget.conversation['id']);

        if (hireRequest != null) {
          hireRequestId = hireRequest['id'];

          // Update the conversation with the found hire request ID
          await FirebaseFirestore.instance
              .collection('conversations')
              .doc(widget.conversation['id'])
              .update({
                'hireRequestId': hireRequestId,
              });

          // Also update the local conversation object
          widget.conversation['hireRequestId'] = hireRequestId;
        }
      }
    }

    return hireRequestId;
  }

  // Get message suggestions based on conversation context
  List<String> _getMessageSuggestions() {
    // Default suggestions
    final List<String> suggestions = [
      'Hello! I would like to connect with you.',
    ];

    // Add more context-specific suggestions
    if (widget.conversation['type'] == 'hire') {
      final String status = widget.conversation['status'] ?? 'pending';

      if (status == 'pending') {
        suggestions.add('I\'m interested in discussing the project details.');
        suggestions.add('When would be a good time to discuss the requirements?');
      } else if (status == 'accepted') {
        suggestions.add('How is the project coming along?');
        suggestions.add('Do you need any clarification on the requirements?');
      }
    } else {
      // Regular conversation suggestions
      suggestions.add('How are you doing today?');
      suggestions.add('I saw your profile and I\'m impressed with your work.');
    }

    // Add custom offer suggestion
    suggestions.add('I would like to discuss a custom project with you.');

    return suggestions;
  }

  Widget _buildMessageInput() {
    // Get message suggestions
    final List<String> suggestions = _getMessageSuggestions();

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Reply to message UI
        if (_replyToMessage != null)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            decoration: BoxDecoration(
              color: Colors.grey[200],
              border: Border(
                bottom: BorderSide(
                  color: Colors.grey[300]!,
                  width: 1.5,
                ),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(10),
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.reply, size: 16, color: Color(0xFF6A11CB)),
                          const SizedBox(width: 4),
                          Text(
                            'Reply to',
                            style: GoogleFonts.poppins(
                              fontWeight: FontWeight.w600,
                              fontSize: 12,
                              color: const Color(0xFF6A11CB),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 6),
                      Container(
                        padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.grey[300]!,
                            width: 1,
                          ),
                        ),
                        child: Text(
                          _replyToMessage!['text'] ?? '',
                          style: GoogleFonts.poppins(
                            fontSize: 13,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.close, color: Colors.grey[600]),
                  onPressed: () {
                    setState(() {
                      _replyToMessage = null;
                    });
                  },
                ),
              ],
            ),
          ),

        // Message suggestions
        if (_messages.isEmpty || (_messages.length == 1 && _messages[0]['type'] == 'system'))
          Container(
            height: 50,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: suggestions.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: InkWell(
                    onTap: () {
                      // Set the suggestion as the message text
                      _messageController.text = suggestions[index];
                      setState(() {
                        _isTyping = true;
                      });
                      // Focus on the text field
                      FocusScope.of(context).requestFocus(FocusNode());
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: Text(
                        suggestions[index],
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),

        // Message input field
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(13),
                blurRadius: 10,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: Row(
            children: [
              // Custom offer button
              IconButton(
                icon: const Icon(Icons.local_offer, color: Color(0xFF6A11CB)),
                tooltip: 'Send Custom Offer',
                onPressed: _showCustomOfferDialog,
              ),
              // Attachment button
              IconButton(
                icon: const Icon(Icons.attach_file, color: Colors.grey),
                onPressed: _showAttachmentOptions,
              ),
              Expanded(
                child: TextField(
                  controller: _messageController,
                  decoration: InputDecoration(
                    hintText: 'Type a message...',
                    hintStyle: GoogleFonts.poppins(
                      color: Colors.grey[400],
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(24),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Colors.grey[100],
                    contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _isTyping = value.trim().isNotEmpty;
                    });
                  },
                ),
              ),
              const SizedBox(width: 8),
              CircleAvatar(
                backgroundColor: _isTyping ? const Color(0xFF6A11CB) : Colors.grey[300],
                child: IconButton(
                  icon: const Icon(Icons.send, color: Colors.white),
                  onPressed: _isTyping ? _sendMessage : null,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
