import 'dart:async';
import 'package:flutter/foundation.dart';
import 'account_deletion_service.dart';

/// Service to handle background tasks like scheduled account deletions
/// In production, this would be replaced by Cloud Functions or similar serverless functions
class BackgroundTaskService {
  static final BackgroundTaskService _instance = BackgroundTaskService._internal();
  factory BackgroundTaskService() => _instance;
  BackgroundTaskService._internal();

  final AccountDeletionService _deletionService = AccountDeletionService();
  Timer? _deletionCheckTimer;
  bool _isRunning = false;

  /// Start the background service for account deletion checks
  /// This should be called when the app starts (for admin/server instances)
  void startDeletionCheckService() {
    if (_isRunning) {
      debugPrint('Background deletion service is already running');
      return;
    }

    _isRunning = true;
    debugPrint('Starting background deletion service...');

    // Run immediately on start
    _checkDeletions();

    // Schedule to run every 24 hours
    _deletionCheckTimer = Timer.periodic(
      const Duration(hours: 24),
      (_) => _checkDeletions(),
    );

    debugPrint('Background deletion service started - will check every 24 hours');
  }

  /// Stop the background service
  void stopDeletionCheckService() {
    if (!_isRunning) {
      debugPrint('Background deletion service is not running');
      return;
    }

    _deletionCheckTimer?.cancel();
    _deletionCheckTimer = null;
    _isRunning = false;
    debugPrint('Background deletion service stopped');
  }

  /// Manually trigger a deletion check (for admin use)
  Future<void> triggerDeletionCheck() async {
    debugPrint('Manually triggering deletion check...');
    await _checkDeletions();
  }

  /// Internal method to check and process deletions
  Future<void> _checkDeletions() async {
    try {
      debugPrint('Running scheduled account deletion check...');
      await _deletionService.checkAndProcessDeletions();
      debugPrint('Scheduled account deletion check completed successfully');
    } catch (e) {
      debugPrint('Error in scheduled deletion check: $e');
    }
  }

  /// Get deletion statistics for monitoring
  Future<Map<String, dynamic>> getDeletionStats() async {
    return await _deletionService.getDeletionStatistics();
  }

  /// Check if the service is running
  bool get isRunning => _isRunning;

  /// Dispose of resources
  void dispose() {
    stopDeletionCheckService();
  }
}

/// Utility class to manage background tasks in the app
class BackgroundTaskManager {
  static bool _initialized = false;

  /// Initialize background tasks (call this in main.dart or app startup)
  static void initialize() {
    if (_initialized) return;

    // Only start deletion service in debug mode or for admin users
    // In production, this would be handled by Cloud Functions
    if (kDebugMode) {
      BackgroundTaskService().startDeletionCheckService();
    }

    _initialized = true;
    debugPrint('Background task manager initialized');
  }

  /// Cleanup background tasks (call this when app is disposed)
  static void dispose() {
    if (!_initialized) return;

    BackgroundTaskService().dispose();
    _initialized = false;
    debugPrint('Background task manager disposed');
  }

  /// Manually trigger account deletion check (for admin panel)
  static Future<void> triggerAccountDeletionCheck() async {
    await BackgroundTaskService().triggerDeletionCheck();
  }

  /// Get account deletion statistics (for admin dashboard)
  static Future<Map<String, dynamic>> getAccountDeletionStats() async {
    return await BackgroundTaskService().getDeletionStats();
  }
}
