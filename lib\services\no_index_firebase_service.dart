import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// Service to handle Firebase queries without requiring indexes
/// All queries are designed to work without compound indexes
class NoIndexFirebaseService {
  static final NoIndexFirebaseService _instance = NoIndexFirebaseService._internal();
  factory NoIndexFirebaseService() => _instance;
  NoIndexFirebaseService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Get posts without orderBy (sorts in memory)
  Future<List<Map<String, dynamic>>> getPosts({int limit = 20}) async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection('posts')
          .limit(limit)
          .get();

      final List<Map<String, dynamic>> posts = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          ...data,
          'createdAt': (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
        };
      }).toList();

      // Sort by createdAt in memory
      posts.sort((a, b) {
        final aTime = a['createdAt'] as DateTime;
        final bTime = b['createdAt'] as DateTime;
        return bTime.compareTo(aTime); // Descending order
      });

      return posts;
    } catch (e) {
      debugPrint('Error getting posts: $e');
      return [];
    }
  }

  /// Get user's assets without orderBy (sorts in memory)
  Future<List<Map<String, dynamic>>> getUserAssets(String userId, {int limit = 50}) async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection('assets')
          .where('authorId', isEqualTo: userId)
          .limit(limit)
          .get();

      final List<Map<String, dynamic>> assets = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          ...data,
          'createdAt': (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
        };
      }).toList();

      // Sort by createdAt in memory
      assets.sort((a, b) {
        final aTime = a['createdAt'] as DateTime;
        final bTime = b['createdAt'] as DateTime;
        return bTime.compareTo(aTime); // Descending order
      });

      return assets;
    } catch (e) {
      debugPrint('Error getting user assets: $e');
      return [];
    }
  }

  /// Get user's posts without orderBy (sorts in memory)
  Future<List<Map<String, dynamic>>> getUserPosts(String userId, {int limit = 50}) async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection('posts')
          .where('authorId', isEqualTo: userId)
          .limit(limit)
          .get();

      final List<Map<String, dynamic>> posts = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          ...data,
          'createdAt': (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
        };
      }).toList();

      // Sort by createdAt in memory
      posts.sort((a, b) {
        final aTime = a['createdAt'] as DateTime;
        final bTime = b['createdAt'] as DateTime;
        return bTime.compareTo(aTime); // Descending order
      });

      return posts;
    } catch (e) {
      debugPrint('Error getting user posts: $e');
      return [];
    }
  }

  /// Get user's hire plans without orderBy (sorts in memory)
  Future<List<Map<String, dynamic>>> getUserHirePlans(String userId) async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection('hire_plans')
          .where('userId', isEqualTo: userId)
          .get();

      final List<Map<String, dynamic>> plans = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          ...data,
        };
      }).toList();

      // Sort by price in memory
      plans.sort((a, b) {
        final aPrice = (a['price'] as num?)?.toDouble() ?? 0.0;
        final bPrice = (b['price'] as num?)?.toDouble() ?? 0.0;
        return aPrice.compareTo(bPrice); // Ascending order
      });

      return plans;
    } catch (e) {
      debugPrint('Error getting user hire plans: $e');
      return [];
    }
  }

  /// Get user's transactions without orderBy (sorts in memory)
  Future<List<Map<String, dynamic>>> getUserTransactions(String userId, {int limit = 50}) async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection('transactions')
          .where('userId', isEqualTo: userId)
          .limit(limit)
          .get();

      final List<Map<String, dynamic>> transactions = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          ...data,
          'createdAt': (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
        };
      }).toList();

      // Sort by createdAt in memory
      transactions.sort((a, b) {
        final aTime = a['createdAt'] as DateTime;
        final bTime = b['createdAt'] as DateTime;
        return bTime.compareTo(aTime); // Descending order
      });

      return transactions;
    } catch (e) {
      debugPrint('Error getting user transactions: $e');
      return [];
    }
  }

  /// Get challenges by status without orderBy (sorts in memory)
  Future<List<Map<String, dynamic>>> getChallengesByStatus(String status, {int limit = 50}) async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection('challenges')
          .where('status', isEqualTo: status)
          .limit(limit)
          .get();

      final List<Map<String, dynamic>> challenges = snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          ...data,
          'createdAt': (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
        };
      }).toList();

      // Sort by createdAt in memory
      challenges.sort((a, b) {
        final aTime = a['createdAt'] as DateTime;
        final bTime = b['createdAt'] as DateTime;
        return bTime.compareTo(aTime); // Descending order
      });

      return challenges;
    } catch (e) {
      debugPrint('Error getting challenges by status: $e');
      return [];
    }
  }

  /// Get challenge submissions by participant without orderBy
  Future<List<Map<String, dynamic>>> getChallengeSubmissionsByParticipant(String participantId) async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection('challenge_submissions')
          .where('participantId', isEqualTo: participantId)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          ...data,
        };
      }).toList();
    } catch (e) {
      debugPrint('Error getting challenge submissions: $e');
      return [];
    }
  }

  /// Get bank details by user without orderBy
  Future<List<Map<String, dynamic>>> getBankDetailsByUser(String userId) async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection('bank_details')
          .where('userId', isEqualTo: userId)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          ...data,
        };
      }).toList();
    } catch (e) {
      debugPrint('Error getting bank details: $e');
      return [];
    }
  }

  /// Get payment methods by user without orderBy
  Future<List<Map<String, dynamic>>> getPaymentMethodsByUser(String userId) async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection('payment_methods')
          .where('userId', isEqualTo: userId)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          ...data,
        };
      }).toList();
    } catch (e) {
      debugPrint('Error getting payment methods: $e');
      return [];
    }
  }

  /// Get transactions by type without orderBy
  Future<List<Map<String, dynamic>>> getTransactionsByType(String type, {int limit = 50}) async {
    try {
      final QuerySnapshot snapshot = await _firestore
          .collection('transactions')
          .where('type', isEqualTo: type)
          .limit(limit)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          ...data,
        };
      }).toList();
    } catch (e) {
      debugPrint('Error getting transactions by type: $e');
      return [];
    }
  }
}
