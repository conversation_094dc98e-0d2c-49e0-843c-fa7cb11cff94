import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../public_profile_page.dart';

class MentionText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final int? maxLines;
  final TextOverflow? overflow;

  const MentionText({
    super.key,
    required this.text,
    this.style,
    this.maxLines,
    this.overflow,
  });

  @override
  Widget build(BuildContext context) {
    return RichText(
      text: _buildTextSpan(context),
      maxLines: maxLines,
      overflow: overflow ?? TextOverflow.clip,
    );
  }

  TextSpan _buildTextSpan(BuildContext context) {
    final List<TextSpan> spans = [];
    // Updated regex to capture usernames that can contain letters, numbers, underscores, and dots
    // This will match @username until it hits a space or end of string
    final RegExp mentionRegex = RegExp(r'@([a-zA-Z0-9_.]+)');

    int lastMatchEnd = 0;

    for (final Match match in mentionRegex.allMatches(text)) {
      // Add text before the mention
      if (match.start > lastMatchEnd) {
        spans.add(TextSpan(
          text: text.substring(lastMatchEnd, match.start),
          style: style,
        ));
      }

      // Add the clickable mention
      final String username = match.group(1)!;
      spans.add(TextSpan(
        text: '@$username',
        style: (style ?? const TextStyle()).copyWith(
          color: const Color(0xFF4F46E5),
          fontWeight: FontWeight.w600,
          decoration: TextDecoration.none,
        ),
        recognizer: TapGestureRecognizer()
          ..onTap = () => _onMentionTap(context, username),
      ));

      lastMatchEnd = match.end;
    }

    // Add remaining text after the last mention
    if (lastMatchEnd < text.length) {
      spans.add(TextSpan(
        text: text.substring(lastMatchEnd),
        style: style,
      ));
    }

    // If no mentions found, return the original text
    if (spans.isEmpty) {
      return TextSpan(text: text, style: style);
    }

    return TextSpan(children: spans);
  }

  void _onMentionTap(BuildContext context, String username) {
    debugPrint('Mention tapped: @$username');

    // Show loading indicator and navigate to user profile
    _navigateToUserProfile(context, username);
  }

  // Look up user by username and navigate to their profile
  Future<void> _navigateToUserProfile(BuildContext context, String username) async {
    try {
      // Show loading indicator
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Looking up @$username...',
            style: GoogleFonts.poppins(),
          ),
          backgroundColor: const Color(0xFF4F46E5),
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 1),
        ),
      );

      // Query Firebase for user by username
      final userQuery = await FirebaseFirestore.instance
          .collection('users')
          .where('username', isEqualTo: username)
          .limit(1)
          .get();

      if (userQuery.docs.isNotEmpty) {
        final userId = userQuery.docs.first.id;

        // Navigate to public profile page
        if (context.mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => PublicProfilePage(userId: userId),
            ),
          );
        }
      } else {
        // User not found
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'User @$username not found',
                style: GoogleFonts.poppins(),
              ),
              backgroundColor: Colors.orange,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Error looking up user: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error opening profile for @$username',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }
}
