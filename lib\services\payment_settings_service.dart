import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import '../models/bank_details_model.dart';
import '../models/payment_method_model.dart';

/// Service to manage user payment settings (bank details and payment methods)
class PaymentSettingsService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Get current user ID
  String? get _currentUserId => _auth.currentUser?.uid;

  // Get user's bank details
  Stream<List<BankDetailsModel>> getUserBankDetails() {
    final userId = _currentUserId;
    if (userId == null) {
      return Stream.value([]);
    }

    return _firestore
        .collection('bank_details')
        .where('userId', isEqualTo: userId)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => BankDetailsModel.fromFirestore(doc))
            .toList());
  }

  // Add bank details
  Future<BankDetailsModel> addBankDetails(BankDetailsModel bankDetails) async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Check if this is the first bank account (make it default)
      final existingAccounts = await _firestore
          .collection('bank_details')
          .where('userId', isEqualTo: userId)
          .get();

      final isFirstAccount = existingAccounts.docs.isEmpty;
      
      // If this is set as default, update all other accounts to not be default
      if (bankDetails.isDefault) {
        await _updateDefaultBankAccount(userId);
      }

      // Create bank details document
      final docRef = await _firestore.collection('bank_details').add(
        bankDetails
            .copyWith(
              userId: userId,
              isDefault: bankDetails.isDefault || isFirstAccount,
              isVerified: false, // New accounts start as unverified
            )
            .toMap(),
      );

      // Get the created document
      final doc = await docRef.get();
      return BankDetailsModel.fromFirestore(doc);
    } catch (e) {
      debugPrint('Error adding bank details: $e');
      rethrow;
    }
  }

  // Update bank details
  Future<BankDetailsModel> updateBankDetails(BankDetailsModel bankDetails) async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Verify ownership
      final doc = await _firestore.collection('bank_details').doc(bankDetails.id).get();
      if (!doc.exists) {
        throw Exception('Bank details not found');
      }

      final existingDetails = BankDetailsModel.fromFirestore(doc);
      if (existingDetails.userId != userId) {
        throw Exception('You do not have permission to update these bank details');
      }

      // If this is set as default, update all other accounts to not be default
      if (bankDetails.isDefault && !existingDetails.isDefault) {
        await _updateDefaultBankAccount(userId);
      }

      // Update bank details
      await _firestore.collection('bank_details').doc(bankDetails.id).update(
        bankDetails
            .copyWith(
              userId: userId,
              // Don't allow changing verification status directly
              isVerified: existingDetails.isVerified,
            )
            .toMap(),
      );

      // Get the updated document
      final updatedDoc = await _firestore.collection('bank_details').doc(bankDetails.id).get();
      return BankDetailsModel.fromFirestore(updatedDoc);
    } catch (e) {
      debugPrint('Error updating bank details: $e');
      rethrow;
    }
  }

  // Delete bank details
  Future<void> deleteBankDetails(String bankDetailsId) async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Verify ownership
      final doc = await _firestore.collection('bank_details').doc(bankDetailsId).get();
      if (!doc.exists) {
        throw Exception('Bank details not found');
      }

      final existingDetails = BankDetailsModel.fromFirestore(doc);
      if (existingDetails.userId != userId) {
        throw Exception('You do not have permission to delete these bank details');
      }

      // Delete bank details
      await _firestore.collection('bank_details').doc(bankDetailsId).delete();

      // If this was the default account, set another account as default if available
      if (existingDetails.isDefault) {
        final remainingAccounts = await _firestore
            .collection('bank_details')
            .where('userId', isEqualTo: userId)
            .limit(1)
            .get();

        if (remainingAccounts.docs.isNotEmpty) {
          await _firestore.collection('bank_details').doc(remainingAccounts.docs.first.id).update({
            'isDefault': true,
            'updatedAt': FieldValue.serverTimestamp(),
          });
        }
      }
    } catch (e) {
      debugPrint('Error deleting bank details: $e');
      rethrow;
    }
  }

  // Set bank details as default
  Future<void> setDefaultBankAccount(String bankDetailsId) async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Verify ownership
      final doc = await _firestore.collection('bank_details').doc(bankDetailsId).get();
      if (!doc.exists) {
        throw Exception('Bank details not found');
      }

      final existingDetails = BankDetailsModel.fromFirestore(doc);
      if (existingDetails.userId != userId) {
        throw Exception('You do not have permission to update these bank details');
      }

      // Update all other accounts to not be default
      await _updateDefaultBankAccount(userId);

      // Set this account as default
      await _firestore.collection('bank_details').doc(bankDetailsId).update({
        'isDefault': true,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error setting default bank account: $e');
      rethrow;
    }
  }

  // Helper method to update default bank account
  Future<void> _updateDefaultBankAccount(String userId) async {
    final batch = _firestore.batch();
    
    final accounts = await _firestore
        .collection('bank_details')
        .where('userId', isEqualTo: userId)
        .where('isDefault', isEqualTo: true)
        .get();
    
    for (final doc in accounts.docs) {
      batch.update(doc.reference, {
        'isDefault': false,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    }
    
    await batch.commit();
  }

  // Get user's payment methods
  Stream<List<PaymentMethodModel>> getUserPaymentMethods() {
    final userId = _currentUserId;
    if (userId == null) {
      return Stream.value([]);
    }

    return _firestore
        .collection('payment_methods')
        .where('userId', isEqualTo: userId)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => PaymentMethodModel.fromFirestore(doc))
            .toList());
  }

  // Add payment method
  Future<PaymentMethodModel> addPaymentMethod(PaymentMethodModel paymentMethod) async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Check if this is the first payment method (make it default)
      final existingMethods = await _firestore
          .collection('payment_methods')
          .where('userId', isEqualTo: userId)
          .get();

      final isFirstMethod = existingMethods.docs.isEmpty;
      
      // If this is set as default, update all other methods to not be default
      if (paymentMethod.isDefault) {
        await _updateDefaultPaymentMethod(userId);
      }

      // Create payment method document
      final docRef = await _firestore.collection('payment_methods').add(
        paymentMethod
            .copyWith(
              userId: userId,
              isDefault: paymentMethod.isDefault || isFirstMethod,
              isVerified: false, // New methods start as unverified
            )
            .toMap(),
      );

      // Get the created document
      final doc = await docRef.get();
      return PaymentMethodModel.fromFirestore(doc);
    } catch (e) {
      debugPrint('Error adding payment method: $e');
      rethrow;
    }
  }

  // Update payment method
  Future<PaymentMethodModel> updatePaymentMethod(PaymentMethodModel paymentMethod) async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Verify ownership
      final doc = await _firestore.collection('payment_methods').doc(paymentMethod.id).get();
      if (!doc.exists) {
        throw Exception('Payment method not found');
      }

      final existingMethod = PaymentMethodModel.fromFirestore(doc);
      if (existingMethod.userId != userId) {
        throw Exception('You do not have permission to update this payment method');
      }

      // If this is set as default, update all other methods to not be default
      if (paymentMethod.isDefault && !existingMethod.isDefault) {
        await _updateDefaultPaymentMethod(userId);
      }

      // Update payment method
      await _firestore.collection('payment_methods').doc(paymentMethod.id).update(
        paymentMethod
            .copyWith(
              userId: userId,
              // Don't allow changing verification status directly
              isVerified: existingMethod.isVerified,
            )
            .toMap(),
      );

      // Get the updated document
      final updatedDoc = await _firestore.collection('payment_methods').doc(paymentMethod.id).get();
      return PaymentMethodModel.fromFirestore(updatedDoc);
    } catch (e) {
      debugPrint('Error updating payment method: $e');
      rethrow;
    }
  }

  // Delete payment method
  Future<void> deletePaymentMethod(String paymentMethodId) async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Verify ownership
      final doc = await _firestore.collection('payment_methods').doc(paymentMethodId).get();
      if (!doc.exists) {
        throw Exception('Payment method not found');
      }

      final existingMethod = PaymentMethodModel.fromFirestore(doc);
      if (existingMethod.userId != userId) {
        throw Exception('You do not have permission to delete this payment method');
      }

      // Delete payment method
      await _firestore.collection('payment_methods').doc(paymentMethodId).delete();

      // If this was the default method, set another method as default if available
      if (existingMethod.isDefault) {
        final remainingMethods = await _firestore
            .collection('payment_methods')
            .where('userId', isEqualTo: userId)
            .limit(1)
            .get();

        if (remainingMethods.docs.isNotEmpty) {
          await _firestore.collection('payment_methods').doc(remainingMethods.docs.first.id).update({
            'isDefault': true,
            'updatedAt': FieldValue.serverTimestamp(),
          });
        }
      }
    } catch (e) {
      debugPrint('Error deleting payment method: $e');
      rethrow;
    }
  }

  // Set payment method as default
  Future<void> setDefaultPaymentMethod(String paymentMethodId) async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Verify ownership
      final doc = await _firestore.collection('payment_methods').doc(paymentMethodId).get();
      if (!doc.exists) {
        throw Exception('Payment method not found');
      }

      final existingMethod = PaymentMethodModel.fromFirestore(doc);
      if (existingMethod.userId != userId) {
        throw Exception('You do not have permission to update this payment method');
      }

      // Update all other methods to not be default
      await _updateDefaultPaymentMethod(userId);

      // Set this method as default
      await _firestore.collection('payment_methods').doc(paymentMethodId).update({
        'isDefault': true,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error setting default payment method: $e');
      rethrow;
    }
  }

  // Helper method to update default payment method
  Future<void> _updateDefaultPaymentMethod(String userId) async {
    final batch = _firestore.batch();
    
    final methods = await _firestore
        .collection('payment_methods')
        .where('userId', isEqualTo: userId)
        .where('isDefault', isEqualTo: true)
        .get();
    
    for (final doc in methods.docs) {
      batch.update(doc.reference, {
        'isDefault': false,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    }
    
    await batch.commit();
  }
}
