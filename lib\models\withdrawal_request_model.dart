import 'package:cloud_firestore/cloud_firestore.dart';

/// Withdrawal request model class to handle user withdrawal requests
class WithdrawalRequestModel {
  final String id;
  final String userId;
  final String userName;
  final String userEmail;
  final double amount;
  final String status; // 'pending', 'approved', 'rejected'
  final String paymentMethod; // 'bank_account', 'paypal', 'google_pay', etc.
  final String paymentMethodId; // ID of the payment method or bank account
  final String paymentMethodDetails; // Display name of the payment method
  final String? rejectionReason;
  final String? transactionId;
  final String? adminNotes;
  final DateTime createdAt;
  final DateTime updatedAt;

  WithdrawalRequestModel({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userEmail,
    required this.amount,
    required this.status,
    required this.paymentMethod,
    required this.paymentMethodId,
    required this.paymentMethodDetails,
    this.rejectionReason,
    this.transactionId,
    this.adminNotes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : 
    createdAt = createdAt ?? DateTime.now(),
    updatedAt = updatedAt ?? DateTime.now();

  // Create a WithdrawalRequestModel from a Firestore document
  factory WithdrawalRequestModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>? ?? {};
    
    final Timestamp createdAtTimestamp = data['createdAt'] as Timestamp? ?? Timestamp.now();
    final Timestamp updatedAtTimestamp = data['updatedAt'] as Timestamp? ?? Timestamp.now();
    
    return WithdrawalRequestModel(
      id: doc.id,
      userId: data['userId'] as String? ?? '',
      userName: data['userName'] as String? ?? '',
      userEmail: data['userEmail'] as String? ?? '',
      amount: (data['amount'] as num?)?.toDouble() ?? 0.0,
      status: data['status'] as String? ?? 'pending',
      paymentMethod: data['paymentMethod'] as String? ?? '',
      paymentMethodId: data['paymentMethodId'] as String? ?? '',
      paymentMethodDetails: data['paymentMethodDetails'] as String? ?? '',
      rejectionReason: data['rejectionReason'] as String?,
      transactionId: data['transactionId'] as String?,
      adminNotes: data['adminNotes'] as String?,
      createdAt: createdAtTimestamp.toDate(),
      updatedAt: updatedAtTimestamp.toDate(),
    );
  }

  // Convert WithdrawalRequestModel to a Map for Firestore
  Map<String, dynamic> toMap() {
    final Map<String, dynamic> data = {
      'userId': userId,
      'userName': userName,
      'userEmail': userEmail,
      'amount': amount,
      'status': status,
      'paymentMethod': paymentMethod,
      'paymentMethodId': paymentMethodId,
      'paymentMethodDetails': paymentMethodDetails,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(DateTime.now()),
    };

    if (rejectionReason != null) data['rejectionReason'] = rejectionReason;
    if (transactionId != null) data['transactionId'] = transactionId;
    if (adminNotes != null) data['adminNotes'] = adminNotes;

    return data;
  }

  // Create a copy of this WithdrawalRequestModel with updated fields
  WithdrawalRequestModel copyWith({
    String? id,
    String? userId,
    String? userName,
    String? userEmail,
    double? amount,
    String? status,
    String? paymentMethod,
    String? paymentMethodId,
    String? paymentMethodDetails,
    String? rejectionReason,
    String? transactionId,
    String? adminNotes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return WithdrawalRequestModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userEmail: userEmail ?? this.userEmail,
      amount: amount ?? this.amount,
      status: status ?? this.status,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentMethodId: paymentMethodId ?? this.paymentMethodId,
      paymentMethodDetails: paymentMethodDetails ?? this.paymentMethodDetails,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      transactionId: transactionId ?? this.transactionId,
      adminNotes: adminNotes ?? this.adminNotes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
  
  // Get formatted status
  String get statusDisplay {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'approved':
        return 'Approved';
      case 'rejected':
        return 'Rejected';
      default:
        return status.substring(0, 1).toUpperCase() + status.substring(1);
    }
  }
  
  // Get status color
  int get statusColor {
    switch (status) {
      case 'pending':
        return 0xFFFFA000; // Amber
      case 'approved':
        return 0xFF4CAF50; // Green
      case 'rejected':
        return 0xFFF44336; // Red
      default:
        return 0xFF9E9E9E; // Grey
    }
  }
}
