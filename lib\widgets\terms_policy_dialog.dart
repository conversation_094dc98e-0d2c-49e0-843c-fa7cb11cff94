import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class TermsPolicyDialog extends StatefulWidget {
  final Function(bool)? onAccept;

  const TermsPolicyDialog({
    Key? key,
    this.onAccept,
  }) : super(key: key);

  @override
  State<TermsPolicyDialog> createState() => _TermsPolicyDialogState();
}

class _TermsPolicyDialogState extends State<TermsPolicyDialog> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _hasAccepted = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: contentBox(context),
    );
  }

  Widget contentBox(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(top: 16),
      decoration: BoxDecoration(
        shape: BoxShape.rectangle,
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Terms & Policies',
            style: GoogleFonts.poppins(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          TabBar(
            controller: _tabController,
            labelColor: const Color(0xFF6A11CB),
            unselectedLabelColor: Colors.grey,
            indicatorColor: const Color(0xFF6A11CB),
            tabs: [
              Tab(
                text: 'Terms of Service',
                icon: Icon(
                  Icons.description_outlined,
                  color: _tabController.index == 0
                      ? const Color(0xFF6A11CB)
                      : Colors.grey,
                ),
              ),
              Tab(
                text: 'Privacy Policy',
                icon: Icon(
                  Icons.privacy_tip_outlined,
                  color: _tabController.index == 1
                      ? const Color(0xFF6A11CB)
                      : Colors.grey,
                ),
              ),
            ],
          ),
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.5,
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildTermsOfService(),
                _buildPrivacyPolicy(),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(16),
                bottomRight: Radius.circular(16),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 5,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              children: [
                Checkbox(
                  value: _hasAccepted,
                  onChanged: (value) {
                    setState(() {
                      _hasAccepted = value ?? false;
                    });
                  },
                  activeColor: const Color(0xFF6A11CB),
                ),
                Expanded(
                  child: Text(
                    'I have read and agree to the Terms of Service and Privacy Policy',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.black87,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _hasAccepted
                      ? () {
                          if (widget.onAccept != null) {
                            widget.onAccept!(true);
                          }
                          Navigator.pop(context, true);
                        }
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF6A11CB),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    elevation: 0,
                  ),
                  child: Text(
                    'Accept',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTermsOfService() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Text(
        'TERMS OF SERVICE\n\n'
        'Last Updated: ${DateTime.now().toString().substring(0, 10)}\n\n'
        '1. INTRODUCTION\n\n'
        'Welcome to Asatu, a developer marketplace and community platform. These Terms of Service ("Terms") govern your access to and use of the Asatu platform, including any content, functionality, and services offered on or through the platform. By registering with us or by using our platform, you accept and agree to be bound by these Terms. If you do not agree to these Terms, you must not access or use our platform.\n\n'
        'Asatu provides a marketplace for developers to buy, sell, and share digital assets, code, and services. Our platform facilitates connections between creators and users, enabling the exchange of digital assets, participation in contests, hiring for projects, and community engagement.\n\n'
        '2. DEVELOPER MARKETPLACE\n\n'
        '2.1 Revenue Sharing\n'
        '• Sellers receive 70% of the revenue from their asset sales\n'
        '• Asatu retains 30% as a platform fee\n'
        '• This revenue split applies to all digital assets, including but not limited to: UI kits, templates, code snippets, plugins, and complete applications\n'
        '• Payment processing fees may be deducted from the seller\'s share\n\n'
        '2.2 Transaction Processing\n'
        '• All transactions must be processed through the platform\n'
        '• Attempting to bypass platform fees is prohibited\n'
        '• Direct payment arrangements between buyers and sellers outside the platform are strictly forbidden\n'
        '• Violations may result in account suspension or termination\n\n'
        '2.3 Asset Quality Requirements\n'
        '• All assets must be original or properly licensed\n'
        '• Assets must be fully functional and as described\n'
        '• Assets must not contain malicious code or security vulnerabilities\n'
        '• Asatu reserves the right to remove assets that do not meet quality standards\n\n'
        '3. MONETIZATION FEATURES\n\n'
        '3.1 Asset Sales\n'
        '• Users can sell digital assets with the 70/30 revenue split\n'
        '• Pricing is set by the seller within platform guidelines\n'
        '• Assets can be offered as free, one-time purchase, or subscription\n'
        '• Sellers are responsible for providing accurate descriptions and support\n'
        '• Buyers receive a non-exclusive license to use purchased assets according to the terms specified by the seller\n\n'
        '3.2 Contests\n'
        '• Users can create contests with cash prizes\n'
        '• Contest creators set requirements and judging criteria\n'
        '• Minimum prize amount: \$50\n'
        '• Platform fee: 30% of the prize amount\n'
        '• Contest entries become the property of the contest creator unless otherwise specified\n'
        '• Contest creators must provide clear requirements and fair judging criteria\n'
        '• Contest deadlines must be reasonable\n'
        '• Contest prizes must be paid in full before the contest begins\n\n'
        '3.3 Hiring\n'
        '• Users can hire other users for custom projects\n'
        '• Service providers receive 70% of the payment\n'
        '• Platform fee: 30% of the payment\n'
        '• Milestone-based payments are supported\n'
        '• Escrow services are provided for project protection\n'
        '• Project requirements must be clearly defined\n'
        '• Payment terms must be agreed upon before work begins\n'
        '• Disputes will be mediated by Asatu\'s support team\n\n'
        '4. PAYMENTS AND WALLET\n\n'
        '4.1 Wallet Functionality\n'
        '• Add funds via credit/debit cards, PayPal, PhonePe, Paytm, QR code, Google Pay, and Apple Pay\n'
        '• Store balance for future purchases\n'
        '• Track transaction history\n'
        '• Receive earnings from sales\n\n'
        '4.2 Adding Funds\n'
        '• Minimum deposit amount: \$5 or equivalent in local currency\n'
        '• Payment processing fees may apply depending on the payment method\n'
        '• Funds are typically available immediately, but may be subject to verification\n\n'
        '4.3 Withdrawals\n'
        '• Minimum withdrawal amount: \$20 or equivalent in local currency\n'
        '• Withdrawals are subject to identity verification\n'
        '• Processing time: 3-7 business days\n'
        '• Withdrawal fees may apply depending on the withdrawal method\n\n'
        '5. USER CONTENT\n\n'
        '5.1 Ownership and License\n'
        '• Users retain ownership of their content\n'
        '• By uploading content, users grant Asatu a worldwide, non-exclusive, royalty-free license to use, reproduce, modify, adapt, publish, translate, distribute, and display such content on the platform\n'
        '• This license is solely for the purpose of operating and improving the platform\n\n'
        '5.2 Responsibility\n'
        '• Users are responsible for ensuring they have the right to share any content they upload\n'
        '• Users must not upload content that infringes on third-party rights\n'
        '• Asatu is not responsible for the content posted by users\n\n'
        '6. COMMUNITY GUIDELINES\n\n'
        '6.1 Conduct\n'
        '• Treat others with respect and professionalism\n'
        '• Engage in constructive discussions and feedback\n'
        '• Respect intellectual property rights\n'
        '• Report violations of these guidelines\n\n'
        '6.2 Prohibited Content\n'
        '• Offensive, harmful, or illegal content\n'
        '• Content that promotes discrimination, hatred, or violence\n'
        '• Sexually explicit or pornographic material\n'
        '• Content that infringes on intellectual property rights\n'
        '• Malware, viruses, or other harmful code\n\n'
        '7. TERMINATION\n\n'
        '7.1 Account Termination by Asatu\n'
        '• Asatu reserves the right to terminate or suspend accounts that violate these terms\n'
        '• Accounts engaged in fraudulent activity will be terminated immediately\n'
        '• Terminated accounts may not be eligible for refunds\n\n'
        '7.2 Account Termination by User\n'
        '• Users may terminate their accounts at any time\n'
        '• Termination does not relieve users of obligations incurred prior to termination\n'
        '• Content and assets may remain on the platform after account termination\n\n'
        '8. LIMITATION OF LIABILITY\n\n'
        'To the maximum extent permitted by law, Asatu and its affiliates shall not be liable for any indirect, incidental, special, consequential, or punitive damages, including but not limited to loss of profits, data, use, goodwill, or other intangible losses, resulting from your access to or use of or inability to access or use the platform.\n\n'
        '9. CHANGES TO TERMS\n\n'
        'We may update these terms from time to time. Significant changes will be notified to users via email or platform notification. Continued use of the platform after changes constitutes acceptance of the new terms.',
        style: GoogleFonts.poppins(
          fontSize: 14,
          height: 1.6,
          color: Colors.black87,
        ),
      ),
    );
  }

  Widget _buildPrivacyPolicy() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Text(
        'PRIVACY POLICY\n\n'
        'Last Updated: ${DateTime.now().toString().substring(0, 10)}\n\n'
        '1. INFORMATION WE COLLECT\n\n'
        '1.1 Information You Provide\n'
        'We collect information you provide directly, including:\n\n'
        '• Account information (name, email, username, password)\n'
        '• Profile information (bio, skills, interests, location, profile picture)\n'
        '• Payment information (processed securely by our payment partners)\n'
        '• Content you upload or create (assets, posts, comments, reviews)\n'
        '• Communications with other users (messages, feedback)\n'
        '• Survey responses and contest entries\n\n'
        '1.2 Information Collected Automatically\n'
        'When you use our platform, we automatically collect certain information, including:\n\n'
        '• Device information (device type, operating system, browser type)\n'
        '• Log data (IP address, access times, pages viewed)\n'
        '• Location information (based on IP address or GPS with your consent)\n'
        '• Usage information (features used, interactions, time spent)\n'
        '• Cookies and similar technologies (for functionality and analytics)\n\n'
        '1.3 Information from Third Parties\n'
        'We may receive information about you from third parties, including:\n\n'
        '• Social media platforms (when you connect your accounts)\n'
        '• Payment processors (transaction information)\n'
        '• Other users (reviews, recommendations, referrals)\n\n'
        '2. HOW WE USE YOUR INFORMATION\n\n'
        '2.1 Core Service Functionality\n'
        'We use your information to:\n\n'
        '• Create and manage your account\n'
        '• Provide and improve our services\n'
        '• Process transactions and manage payments\n'
        '• Facilitate communication between users\n'
        '• Display your profile to other users\n\n'
        '2.2 Communication and Support\n'
        '• Communicate with you about your account and transactions\n'
        '• Respond to your inquiries and support requests\n'
        '• Send service announcements and updates\n'
        '• Provide personalized recommendations\n\n'
        '2.3 Platform Improvement and Analytics\n'
        '• Analyze usage patterns and trends\n'
        '• Develop new features and services\n'
        '• Test and troubleshoot platform functionality\n'
        '• Measure the effectiveness of our marketing\n\n'
        '3. INFORMATION SHARING\n\n'
        '3.1 Public Information\n'
        'The following information is visible to other users:\n\n'
        '• Your profile information (name, username, bio, skills, interests)\n'
        '• Public content you create or upload\n'
        '• Your ratings and reviews\n'
        '• Your activity on public discussions\n\n'
        '3.2 Sharing with Other Users\n'
        'We share information with other users when you:\n\n'
        '• Engage in transactions (limited to necessary transaction details)\n'
        '• Participate in contests or hiring arrangements\n'
        '• Send messages or communicate through the platform\n'
        '• Collaborate on projects\n\n'
        '3.3 Service Providers\n'
        'We may share your information with third-party service providers who help us:\n\n'
        '• Process payments (payment processors)\n'
        '• Host our platform (cloud services)\n'
        '• Analyze platform usage (analytics providers)\n'
        '• Send communications (email service providers)\n'
        '• Prevent fraud and abuse (security services)\n\n'
        '4. DATA SECURITY\n\n'
        '4.1 Security Measures\n'
        'We implement appropriate technical and organizational security measures to protect your information, including:\n\n'
        '• Encryption of sensitive data in transit and at rest\n'
        '• Regular security assessments and penetration testing\n'
        '• Access controls and authentication requirements\n'
        '• Monitoring for suspicious activity\n'
        '• Employee training on security practices\n\n'
        '4.2 Data Retention\n'
        'We retain your information for as long as necessary to:\n\n'
        '• Provide our services to you\n'
        '• Comply with legal obligations\n'
        '• Resolve disputes\n'
        '• Enforce our agreements\n\n'
        '5. YOUR RIGHTS AND CHOICES\n\n'
        '5.1 Account Information\n'
        'You can access and update your account information through your profile settings. You may also contact us to request changes.\n\n'
        '5.2 Communication Preferences\n'
        'You can manage your communication preferences by:\n\n'
        '• Updating your notification settings in your account\n'
        '• Using the unsubscribe link in our emails\n'
        '• Contacting our support team\n\n'
        '5.3 Privacy Rights\n'
        'Depending on your location, you may have rights to:\n\n'
        '• Access your personal information\n'
        '• Correct inaccurate information\n'
        '• Delete your information (subject to certain exceptions)\n'
        '• Object to certain processing of your information\n'
        '• Restrict processing of your information\n'
        '• Export your data in a portable format\n\n'
        '6. CHANGES TO PRIVACY POLICY\n\n'
        'We may update this privacy policy from time to time to reflect changes in our practices or for other operational, legal, or regulatory reasons. We will notify you of significant changes by posting a notice on our platform, sending an email to the address associated with your account, or providing a notification when you log in. We encourage you to review our privacy policy periodically.\n\n'
        '7. CONTACT US\n\n'
        'If you have questions, concerns, or requests regarding this privacy policy or our privacy practices, please contact <NAME_EMAIL>.',
        style: GoogleFonts.poppins(
          fontSize: 14,
          height: 1.6,
          color: Colors.black87,
        ),
      ),
    );
  }
}
