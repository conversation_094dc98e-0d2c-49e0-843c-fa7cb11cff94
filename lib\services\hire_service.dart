import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'notification_service.dart';
import 'admin_financial_service.dart';

/// Service to handle all hiring-related functionality
class HireService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final NotificationService _notificationService = NotificationService();

  // Get current user ID
  String? get currentUserId => _auth.currentUser?.uid;

  // Create a new hire request
  Future<Map<String, dynamic>> createHireRequest({
    required String developerId,
    required String planId,
    required String projectDetails,
    required double budget,
    required String timeframe,
    String? conversationId,
  }) async {
    try {
      if (currentUserId == null) {
        return {'success': false, 'error': 'User not authenticated'};
      }

      // Get the hire plan details
      final DocumentSnapshot planDoc = await _firestore.collection('hire_plans').doc(planId).get();
      if (!planDoc.exists) {
        return {'success': false, 'error': 'Hire plan not found'};
      }

      final planData = planDoc.data() as Map<String, dynamic>;

      // Create a new hire request document
      final hireRequestRef = await _firestore.collection('hire_requests').add({
        'clientId': currentUserId,
        'developerId': developerId,
        'planId': planId,
        'planName': planData['name'] ?? 'Custom Plan',
        'projectDetails': projectDetails,
        'budget': budget,
        'timeframe': timeframe,
        'status': 'pending',
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'conversationId': conversationId,
        'milestones': [],
        'files': [],
        'isCompleted': false,
        'isRated': false,
      });

      // If a conversation ID was provided, update it to link to this hire request
      if (conversationId != null && conversationId.isNotEmpty) {
        await _firestore.collection('conversations').doc(conversationId).update({
          'type': 'hire',
          'hireRequestId': hireRequestRef.id,
          'status': 'pending',
          'updatedAt': FieldValue.serverTimestamp(),
        });
      }

      // Send notification to the developer
      await _notificationService.sendNotification(
        userId: developerId,
        title: 'New Hire Request',
        body: 'You have received a new hire request for ${planData['name']}',
        type: 'hire_request',
        data: {
          'hireRequestId': hireRequestRef.id,
          'conversationId': conversationId,
        },
      );

      return {
        'success': true,
        'hireRequestId': hireRequestRef.id,
      };
    } catch (e) {
      debugPrint('Error creating hire request: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  // Accept a hire request
  Future<Map<String, dynamic>> acceptHireRequest(String hireRequestId) async {
    try {
      if (currentUserId == null) {
        return {'success': false, 'error': 'User not authenticated'};
      }

      // Get the hire request
      final DocumentSnapshot hireRequestDoc = await _firestore.collection('hire_requests').doc(hireRequestId).get();
      if (!hireRequestDoc.exists) {
        return {'success': false, 'error': 'Hire request not found'};
      }

      final hireRequestData = hireRequestDoc.data() as Map<String, dynamic>;

      // Verify that the current user is the developer
      if (hireRequestData['developerId'] != currentUserId) {
        return {'success': false, 'error': 'Only the developer can accept this request'};
      }

      // Calculate deadline based on deliveryDays or timeframe
      DateTime deadline;

      // First check if deliveryDays is explicitly set
      if (hireRequestData.containsKey('deliveryDays') && hireRequestData['deliveryDays'] is num) {
        final int deliveryDays = (hireRequestData['deliveryDays'] as num).toInt();
        deadline = DateTime.now().add(Duration(days: deliveryDays));
        debugPrint('HireService - Using explicit deliveryDays: $deliveryDays');
      } else {
        // Fall back to parsing timeframe
        final String timeframe = hireRequestData['timeframe'] ?? '';
        debugPrint('HireService - Parsing timeframe: $timeframe');

        // Try to extract duration information from timeframe
        final RegExp durationRegex = RegExp(r'(\d+)\s*(day|week|month|year)s?', caseSensitive: false);
        final match = durationRegex.firstMatch(timeframe);

        if (match != null) {
          final int count = int.parse(match.group(1)!);
          final String unit = match.group(2)!.toLowerCase();
          debugPrint('HireService - Extracted duration: $count $unit');

          // Calculate deadline based on current time
          final DateTime now = DateTime.now();

          switch (unit) {
            case 'day':
              deadline = now.add(Duration(days: count));
              break;
            case 'week':
              deadline = now.add(Duration(days: count * 7));
              break;
            case 'month':
              // Approximate a month as 30 days
              deadline = now.add(Duration(days: count * 30));
              break;
            case 'year':
              // Approximate a year as 365 days
              deadline = now.add(Duration(days: count * 365));
              break;
            default:
              // Default to 7 days
              deadline = now.add(const Duration(days: 7));
          }
        } else {
          // If we couldn't extract a duration, default to 7 days
          deadline = DateTime.now().add(const Duration(days: 7));
          debugPrint('HireService - Could not parse timeframe, using default 7 days');
        }
      }

      // Update the hire request status with deadline
      await _firestore.collection('hire_requests').doc(hireRequestId).update({
        'status': 'accepted',
        'updatedAt': FieldValue.serverTimestamp(),
        'acceptedAt': FieldValue.serverTimestamp(),
        'deadline': Timestamp.fromDate(deadline),
      });

      // Update the conversation if it exists
      final String? conversationId = hireRequestData['conversationId'];
      if (conversationId != null && conversationId.isNotEmpty) {
        await _firestore.collection('conversations').doc(conversationId).update({
          'status': 'accepted',
          'updatedAt': FieldValue.serverTimestamp(),
          'deadline': Timestamp.fromDate(deadline),
        });
      }

      // Send notification to the client
      await _notificationService.sendNotification(
        userId: hireRequestData['clientId'],
        title: 'Hire Request Accepted',
        body: 'Your hire request has been accepted',
        type: 'hire_request_accepted',
        data: {
          'hireRequestId': hireRequestId,
          'conversationId': conversationId,
          'deadline': deadline.millisecondsSinceEpoch,
        },
      );

      return {'success': true};
    } catch (e) {
      debugPrint('Error accepting hire request: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  // Decline a hire request
  Future<Map<String, dynamic>> declineHireRequest(String hireRequestId, String reason) async {
    try {
      if (currentUserId == null) {
        return {'success': false, 'error': 'User not authenticated'};
      }

      // Get the hire request
      final DocumentSnapshot hireRequestDoc = await _firestore.collection('hire_requests').doc(hireRequestId).get();
      if (!hireRequestDoc.exists) {
        return {'success': false, 'error': 'Hire request not found'};
      }

      final hireRequestData = hireRequestDoc.data() as Map<String, dynamic>;

      // Verify that the current user is the developer
      if (hireRequestData['developerId'] != currentUserId) {
        return {'success': false, 'error': 'Only the developer can decline this request'};
      }

      // Get the client ID and budget for refund processing
      final String clientId = hireRequestData['clientId'] ?? '';
      final double budget = (hireRequestData['budget'] is num)
          ? (hireRequestData['budget'] as num).toDouble()
          : 0.0;

      debugPrint('HireService - declineHireRequest: Processing refund of $budget to client $clientId');

      // Process refund to client's wallet if budget is greater than 0
      if (budget > 0 && clientId.isNotEmpty) {
        try {
          // Use AdminFinancialService to process the refund
          final adminFinancialService = AdminFinancialService();

          // Process the refund with 0% platform fee (full refund)
          await adminFinancialService.processHireRequestRefund(
            hireRequestId: hireRequestId,
            clientId: clientId,
            refundAmount: budget,
            reason: 'Developer declined hire request: $reason',
          );

          debugPrint('HireService - declineHireRequest: Successfully processed refund of $budget to client $clientId');
        } catch (refundError) {
          debugPrint('HireService - declineHireRequest: Error processing refund: $refundError');
          // Continue with declining the request even if refund fails
          // We'll still mark it as refunded in the hire request document
        }
      }

      // Update the hire request status
      await _firestore.collection('hire_requests').doc(hireRequestId).update({
        'status': 'declined',
        'declineReason': reason,
        'updatedAt': FieldValue.serverTimestamp(),
        'declinedAt': FieldValue.serverTimestamp(),
        'isRefunded': budget > 0, // Mark as refunded if there was a budget
        'refundAmount': budget,
        'refundedAt': FieldValue.serverTimestamp(),
      });

      // Update the conversation if it exists
      final String? conversationId = hireRequestData['conversationId'];
      if (conversationId != null && conversationId.isNotEmpty) {
        await _firestore.collection('conversations').doc(conversationId).update({
          'status': 'declined',
          'updatedAt': FieldValue.serverTimestamp(),
          'isRefunded': budget > 0,
          'refundAmount': budget,
        });
      }

      // Send notification to the client about the declined request and refund
      String notificationBody = 'Your hire request has been declined';
      if (budget > 0) {
        notificationBody += '. A full refund of \$${budget.toStringAsFixed(2)} has been processed to your wallet.';
      }

      await _notificationService.sendNotification(
        userId: clientId,
        title: 'Hire Request Declined',
        body: notificationBody,
        type: 'hire_request_declined',
        data: {
          'hireRequestId': hireRequestId,
          'conversationId': conversationId,
          'reason': reason,
          'isRefunded': budget > 0,
          'refundAmount': budget,
        },
      );

      return {
        'success': true,
        'refunded': budget > 0,
        'refundAmount': budget,
      };
    } catch (e) {
      debugPrint('Error declining hire request: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  // Add a milestone to a hire request
  Future<Map<String, dynamic>> addMilestone({
    required String hireRequestId,
    required String title,
    required String description,
    required DateTime dueDate,
  }) async {
    try {
      if (currentUserId == null) {
        return {'success': false, 'error': 'User not authenticated'};
      }

      // Get the hire request
      final DocumentSnapshot hireRequestDoc = await _firestore.collection('hire_requests').doc(hireRequestId).get();
      if (!hireRequestDoc.exists) {
        return {'success': false, 'error': 'Hire request not found'};
      }

      final hireRequestData = hireRequestDoc.data() as Map<String, dynamic>;

      // Verify that the current user is involved in this hire request
      final String clientId = hireRequestData['clientId'];
      final String developerId = hireRequestData['developerId'];

      if (currentUserId != clientId && currentUserId != developerId) {
        return {'success': false, 'error': 'You are not authorized to modify this hire request'};
      }

      // Create a new milestone
      final String milestoneId = FirebaseFirestore.instance.collection('temp').doc().id; // Generate a unique ID
      final Map<String, dynamic> milestone = {
        'id': milestoneId,
        'title': title,
        'description': description,
        'dueDate': Timestamp.fromDate(dueDate),
        'status': 'pending',
        'createdAt': FieldValue.serverTimestamp(),
        'createdBy': currentUserId,
        'completedAt': null,
      };

      // Get existing milestones
      List<dynamic> milestones = List.from(hireRequestData['milestones'] ?? []);
      milestones.add(milestone);

      // Update the hire request with the new milestone
      await _firestore.collection('hire_requests').doc(hireRequestId).update({
        'milestones': milestones,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Send notification to the other party
      final String recipientId = currentUserId == clientId ? developerId : clientId;
      await _notificationService.sendNotification(
        userId: recipientId,
        title: 'New Milestone Added',
        body: 'A new milestone has been added to your project',
        type: 'milestone_added',
        data: {
          'hireRequestId': hireRequestId,
          'milestoneId': milestoneId,
        },
      );

      return {
        'success': true,
        'milestoneId': milestoneId,
      };
    } catch (e) {
      debugPrint('Error adding milestone: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  // Complete a milestone
  Future<Map<String, dynamic>> completeMilestone({
    required String hireRequestId,
    required String milestoneId,
  }) async {
    try {
      if (currentUserId == null) {
        return {'success': false, 'error': 'User not authenticated'};
      }

      // Get the hire request
      final DocumentSnapshot hireRequestDoc = await _firestore.collection('hire_requests').doc(hireRequestId).get();
      if (!hireRequestDoc.exists) {
        return {'success': false, 'error': 'Hire request not found'};
      }

      final hireRequestData = hireRequestDoc.data() as Map<String, dynamic>;

      // Verify that the current user is the developer
      if (hireRequestData['developerId'] != currentUserId) {
        return {'success': false, 'error': 'Only the developer can complete milestones'};
      }

      // Get existing milestones
      List<dynamic> milestones = List.from(hireRequestData['milestones'] ?? []);

      // Find the milestone to update
      int milestoneIndex = milestones.indexWhere((m) => m['id'] == milestoneId);
      if (milestoneIndex == -1) {
        return {'success': false, 'error': 'Milestone not found'};
      }

      // Update the milestone status
      milestones[milestoneIndex]['status'] = 'completed';
      milestones[milestoneIndex]['completedAt'] = FieldValue.serverTimestamp();

      // Update the hire request with the updated milestone
      await _firestore.collection('hire_requests').doc(hireRequestId).update({
        'milestones': milestones,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Send notification to the client
      await _notificationService.sendNotification(
        userId: hireRequestData['clientId'],
        title: 'Milestone Completed',
        body: 'A milestone has been completed in your project',
        type: 'milestone_completed',
        data: {
          'hireRequestId': hireRequestId,
          'milestoneId': milestoneId,
        },
      );

      return {'success': true};
    } catch (e) {
      debugPrint('Error completing milestone: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  // Get a specific hire request by ID
  Future<Map<String, dynamic>?> getHireRequest(String hireRequestId) async {
    try {
      final DocumentSnapshot doc = await _firestore.collection('hire_requests').doc(hireRequestId).get();
      if (!doc.exists) return null;

      final data = doc.data() as Map<String, dynamic>;
      return {
        'id': doc.id,
        ...data,
      };
    } catch (e) {
      debugPrint('Error getting hire request: $e');
      return null;
    }
  }

  // Find a hire request by conversation ID
  Future<Map<String, dynamic>?> findHireRequestByConversationId(String conversationId) async {
    try {
      final QuerySnapshot querySnapshot = await _firestore
          .collection('hire_requests')
          .where('conversationId', isEqualTo: conversationId)
          .limit(1)
          .get();

      if (querySnapshot.docs.isEmpty) return null;

      final doc = querySnapshot.docs.first;
      final data = doc.data() as Map<String, dynamic>;

      return {
        'id': doc.id,
        ...data,
      };
    } catch (e) {
      debugPrint('Error finding hire request by conversation ID: $e');
      return null;
    }
  }

  // Request a deadline extension
  Future<Map<String, dynamic>> requestDeadlineExtension({
    required String hireRequestId,
    required String milestoneId,
    required DateTime newDueDate,
    required String reason,
  }) async {
    try {
      if (currentUserId == null) {
        return {'success': false, 'error': 'User not authenticated'};
      }

      // Get the hire request
      final DocumentSnapshot hireRequestDoc = await _firestore.collection('hire_requests').doc(hireRequestId).get();
      if (!hireRequestDoc.exists) {
        return {'success': false, 'error': 'Hire request not found'};
      }

      final hireRequestData = hireRequestDoc.data() as Map<String, dynamic>;

      // Verify that the current user is the developer
      if (hireRequestData['developerId'] != currentUserId) {
        return {'success': false, 'error': 'Only the developer can request deadline extensions'};
      }

      // Get existing milestones
      List<dynamic> milestones = List.from(hireRequestData['milestones'] ?? []);

      // Find the milestone to update
      int milestoneIndex = milestones.indexWhere((m) => m['id'] == milestoneId);
      if (milestoneIndex == -1) {
        return {'success': false, 'error': 'Milestone not found'};
      }

      // Create extension request
      final String extensionId = FirebaseFirestore.instance.collection('temp').doc().id;
      final Map<String, dynamic> extensionRequest = {
        'id': extensionId,
        'milestoneId': milestoneId,
        'currentDueDate': milestones[milestoneIndex]['dueDate'],
        'requestedDueDate': Timestamp.fromDate(newDueDate),
        'reason': reason,
        'status': 'pending',
        'requestedAt': FieldValue.serverTimestamp(),
        'requestedBy': currentUserId,
      };

      // Add extension request to hire request
      List<dynamic> extensionRequests = List.from(hireRequestData['extensionRequests'] ?? []);
      extensionRequests.add(extensionRequest);

      // Update the hire request with the extension request
      await _firestore.collection('hire_requests').doc(hireRequestId).update({
        'extensionRequests': extensionRequests,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Send notification to the client
      await _notificationService.sendNotification(
        userId: hireRequestData['clientId'],
        title: 'Deadline Extension Requested',
        body: 'A deadline extension has been requested for your project',
        type: 'extension_requested',
        data: {
          'hireRequestId': hireRequestId,
          'milestoneId': milestoneId,
          'extensionId': extensionId,
        },
      );

      return {
        'success': true,
        'extensionId': extensionId,
      };
    } catch (e) {
      debugPrint('Error requesting deadline extension: $e');
      return {'success': false, 'error': e.toString()};
    }
  }
}
