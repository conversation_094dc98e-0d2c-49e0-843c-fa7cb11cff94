import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:async';
import 'notification_service.dart';
import 'ai_moderation_service.dart';

/// A service class to handle flagged conversations for admin review
class FlaggedConversationService {
  // Singleton pattern
  static final FlaggedConversationService _instance = FlaggedConversationService._internal();
  factory FlaggedConversationService() => _instance;
  FlaggedConversationService._internal();

  // Firebase instances
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  
  // Services
  final NotificationService _notificationService = NotificationService();
  final AIModerationService _moderationService = AIModerationService();
  
  // Get current user ID
  String? get currentUserId => _auth.currentUser?.uid;
  
  // Get all flagged conversations
  Stream<List<Map<String, dynamic>>> getFlaggedConversations() {
    return _firestore
        .collection('flagged_conversations')
        .orderBy('timestamp', descending: true)
        .snapshots()
        .map((snapshot) {
          final List<Map<String, dynamic>> flaggedConversations = [];
          
          for (final doc in snapshot.docs) {
            final data = doc.data();
            
            final Map<String, dynamic> flaggedConversation = {
              'id': doc.id,
              'conversationId': data['conversationId'] ?? '',
              'messageText': data['messageText'] ?? '',
              'flagReason': data['flagReason'] ?? '',
              'flagSeverity': data['flagSeverity'] ?? 'minor',
              'timestamp': data['timestamp'],
              'status': data['status'] ?? 'pending',
              'sender': data['sender'] ?? {},
              'receiver': data['receiver'] ?? {},
              'adminFeedback': data['adminFeedback'],
              'adminActionTimestamp': data['adminActionTimestamp'],
              'adminId': data['adminId'],
            };
            
            flaggedConversations.add(flaggedConversation);
          }
          
          return flaggedConversations;
        });
  }
  
  // Get conversation context (messages before and after the flagged message)
  Future<List<Map<String, dynamic>>> getConversationContext(
    String conversationId, 
    Timestamp flaggedTimestamp,
  ) async {
    try {
      // Get messages before the flagged message
      final QuerySnapshot beforeSnapshot = await _firestore
          .collection('conversations')
          .doc(conversationId)
          .collection('messages')
          .orderBy('timestamp', descending: true)
          .where('timestamp', isLessThan: flaggedTimestamp)
          .limit(5)
          .get();
      
      // Get the flagged message
      final QuerySnapshot flaggedSnapshot = await _firestore
          .collection('conversations')
          .doc(conversationId)
          .collection('messages')
          .where('timestamp', isEqualTo: flaggedTimestamp)
          .get();
      
      // Get messages after the flagged message
      final QuerySnapshot afterSnapshot = await _firestore
          .collection('conversations')
          .doc(conversationId)
          .collection('messages')
          .orderBy('timestamp')
          .where('timestamp', isGreaterThan: flaggedTimestamp)
          .limit(5)
          .get();
      
      // Combine all messages
      final List<Map<String, dynamic>> context = [];
      
      // Add messages before
      for (final doc in beforeSnapshot.docs.reversed) {
        final data = doc.data() as Map<String, dynamic>;
        context.add({
          'id': doc.id,
          'senderId': data['senderId'] ?? '',
          'text': data['text'] ?? '',
          'timestamp': data['timestamp'],
          'isFlagged': false,
        });
      }
      
      // Add flagged message
      for (final doc in flaggedSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        context.add({
          'id': doc.id,
          'senderId': data['senderId'] ?? '',
          'text': data['text'] ?? '',
          'timestamp': data['timestamp'],
          'isFlagged': true,
          'flagReason': data['flagReason'] ?? '',
          'flagSeverity': data['flagSeverity'] ?? 'minor',
        });
      }
      
      // Add messages after
      for (final doc in afterSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        context.add({
          'id': doc.id,
          'senderId': data['senderId'] ?? '',
          'text': data['text'] ?? '',
          'timestamp': data['timestamp'],
          'isFlagged': false,
        });
      }
      
      // Sort by timestamp
      context.sort((a, b) {
        final Timestamp aTimestamp = a['timestamp'] as Timestamp;
        final Timestamp bTimestamp = b['timestamp'] as Timestamp;
        return aTimestamp.compareTo(bTimestamp);
      });
      
      return context;
    } catch (e) {
      debugPrint('Error getting conversation context: $e');
      return [];
    }
  }
  
  // Dismiss a flagged conversation (false positive)
  Future<bool> dismissFlaggedConversation(String flaggedId, String moderationLogId) async {
    try {
      if (currentUserId == null) return false;
      
      // Update flagged conversation status
      await _firestore.collection('flagged_conversations').doc(flaggedId).update({
        'status': 'dismissed',
        'adminFeedback': 'false_positive',
        'adminActionTimestamp': FieldValue.serverTimestamp(),
        'adminId': currentUserId,
      });
      
      // Update moderation log with feedback
      if (moderationLogId.isNotEmpty) {
        await _moderationService.updateWithAdminFeedback(
          moderationLogId, 
          true, // false positive
          null,
        );
      }
      
      return true;
    } catch (e) {
      debugPrint('Error dismissing flagged conversation: $e');
      return false;
    }
  }
  
  // Send warning to users
  Future<bool> sendWarningToUsers(
    String flaggedId, 
    String moderationLogId,
    String senderId, 
    String receiverId,
    String warningMessage,
  ) async {
    try {
      if (currentUserId == null) return false;
      
      // Update flagged conversation status
      await _firestore.collection('flagged_conversations').doc(flaggedId).update({
        'status': 'warned',
        'adminFeedback': 'warning_sent',
        'adminActionTimestamp': FieldValue.serverTimestamp(),
        'adminId': currentUserId,
        'warningMessage': warningMessage,
      });
      
      // Send notification to sender
      await _notificationService.sendNotification(
        userId: senderId,
        title: 'Warning from Admin',
        body: warningMessage,
        type: 'admin_warning',
        data: {
          'flaggedId': flaggedId,
        },
      );
      
      // Send notification to receiver if available
      if (receiverId.isNotEmpty) {
        await _notificationService.sendNotification(
          userId: receiverId,
          title: 'Warning from Admin',
          body: warningMessage,
          type: 'admin_warning',
          data: {
            'flaggedId': flaggedId,
          },
        );
      }
      
      // Update moderation log with feedback
      if (moderationLogId.isNotEmpty) {
        await _moderationService.updateWithAdminFeedback(
          moderationLogId, 
          false, // true positive
          'moderate', // correct severity
        );
      }
      
      return true;
    } catch (e) {
      debugPrint('Error sending warning to users: $e');
      return false;
    }
  }
  
  // Temporarily suspend user accounts
  Future<bool> suspendUsers(
    String flaggedId, 
    String moderationLogId,
    String senderId, 
    String receiverId,
    int suspensionDays,
    String suspensionReason,
  ) async {
    try {
      if (currentUserId == null) return false;
      
      // Calculate suspension end date
      final DateTime now = DateTime.now();
      final DateTime suspensionEndDate = now.add(Duration(days: suspensionDays));
      
      // Update flagged conversation status
      await _firestore.collection('flagged_conversations').doc(flaggedId).update({
        'status': 'suspended',
        'adminFeedback': 'accounts_suspended',
        'adminActionTimestamp': FieldValue.serverTimestamp(),
        'adminId': currentUserId,
        'suspensionDays': suspensionDays,
        'suspensionReason': suspensionReason,
        'suspensionEndDate': Timestamp.fromDate(suspensionEndDate),
      });
      
      // Suspend sender
      await _firestore.collection('users').doc(senderId).update({
        'isSuspended': true,
        'suspensionEndDate': Timestamp.fromDate(suspensionEndDate),
        'suspensionReason': suspensionReason,
      });
      
      // Suspend receiver if available
      if (receiverId.isNotEmpty) {
        await _firestore.collection('users').doc(receiverId).update({
          'isSuspended': true,
          'suspensionEndDate': Timestamp.fromDate(suspensionEndDate),
          'suspensionReason': suspensionReason,
        });
      }
      
      // Send notification to sender
      await _notificationService.sendNotification(
        userId: senderId,
        title: 'Account Suspended',
        body: 'Your account has been suspended for $suspensionDays days: $suspensionReason',
        type: 'account_suspended',
        data: {
          'flaggedId': flaggedId,
          'suspensionDays': suspensionDays,
          'suspensionEndDate': suspensionEndDate.toIso8601String(),
        },
      );
      
      // Send notification to receiver if available
      if (receiverId.isNotEmpty) {
        await _notificationService.sendNotification(
          userId: receiverId,
          title: 'Account Suspended',
          body: 'Your account has been suspended for $suspensionDays days: $suspensionReason',
          type: 'account_suspended',
          data: {
            'flaggedId': flaggedId,
            'suspensionDays': suspensionDays,
            'suspensionEndDate': suspensionEndDate.toIso8601String(),
          },
        );
      }
      
      // Update moderation log with feedback
      if (moderationLogId.isNotEmpty) {
        await _moderationService.updateWithAdminFeedback(
          moderationLogId, 
          false, // true positive
          'severe', // correct severity
        );
      }
      
      return true;
    } catch (e) {
      debugPrint('Error suspending users: $e');
      return false;
    }
  }
  
  // Permanently ban users
  Future<bool> banUsers(
    String flaggedId, 
    String moderationLogId,
    String senderId, 
    String receiverId,
    String banReason,
  ) async {
    try {
      if (currentUserId == null) return false;
      
      // Update flagged conversation status
      await _firestore.collection('flagged_conversations').doc(flaggedId).update({
        'status': 'banned',
        'adminFeedback': 'accounts_banned',
        'adminActionTimestamp': FieldValue.serverTimestamp(),
        'adminId': currentUserId,
        'banReason': banReason,
      });
      
      // Ban sender
      await _firestore.collection('users').doc(senderId).update({
        'isBanned': true,
        'banReason': banReason,
        'bannedAt': FieldValue.serverTimestamp(),
        'bannedBy': currentUserId,
      });
      
      // Ban receiver if available
      if (receiverId.isNotEmpty) {
        await _firestore.collection('users').doc(receiverId).update({
          'isBanned': true,
          'banReason': banReason,
          'bannedAt': FieldValue.serverTimestamp(),
          'bannedBy': currentUserId,
        });
      }
      
      // Update moderation log with feedback
      if (moderationLogId.isNotEmpty) {
        await _moderationService.updateWithAdminFeedback(
          moderationLogId, 
          false, // true positive
          'severe', // correct severity
        );
      }
      
      return true;
    } catch (e) {
      debugPrint('Error banning users: $e');
      return false;
    }
  }
  
  // Get flagged conversation statistics
  Future<Map<String, dynamic>> getFlaggedConversationStats() async {
    try {
      // Get total flagged conversations
      final totalSnapshot = await _firestore
          .collection('flagged_conversations')
          .count()
          .get();
      
      // Get pending flagged conversations
      final pendingSnapshot = await _firestore
          .collection('flagged_conversations')
          .where('status', isEqualTo: 'pending')
          .count()
          .get();
      
      // Get dismissed flagged conversations
      final dismissedSnapshot = await _firestore
          .collection('flagged_conversations')
          .where('status', isEqualTo: 'dismissed')
          .count()
          .get();
      
      // Get warned flagged conversations
      final warnedSnapshot = await _firestore
          .collection('flagged_conversations')
          .where('status', isEqualTo: 'warned')
          .count()
          .get();
      
      // Get suspended flagged conversations
      final suspendedSnapshot = await _firestore
          .collection('flagged_conversations')
          .where('status', isEqualTo: 'suspended')
          .count()
          .get();
      
      // Get banned flagged conversations
      final bannedSnapshot = await _firestore
          .collection('flagged_conversations')
          .where('status', isEqualTo: 'banned')
          .count()
          .get();
      
      // Get moderation stats
      final moderationStats = await _moderationService.getModerationStats();
      
      return {
        'totalFlagged': totalSnapshot.count,
        'pending': pendingSnapshot.count,
        'dismissed': dismissedSnapshot.count,
        'warned': warnedSnapshot.count,
        'suspended': suspendedSnapshot.count,
        'banned': bannedSnapshot.count,
        'moderationStats': moderationStats,
      };
    } catch (e) {
      debugPrint('Error getting flagged conversation stats: $e');
      return {
        'totalFlagged': 0,
        'pending': 0,
        'dismissed': 0,
        'warned': 0,
        'suspended': 0,
        'banned': 0,
        'moderationStats': {
          'totalMessages': 0,
          'flaggedMessages': 0,
          'reviewedMessages': 0,
          'falsePositives': 0,
          'accuracy': 0.0,
        },
      };
    }
  }
}
