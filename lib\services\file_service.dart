import 'dart:io';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:file_selector/file_selector.dart';
import '../utils/file_compressor.dart';
import '../utils/image_compressor.dart';

/// A service class to handle file picking and processing
class FileService {
  /// Pick files with the given allowed extensions
  /// Returns a list of File objects
  static Future<List<File>?> pickFiles({
    required BuildContext context,
    required List<String> allowedExtensions,
    bool allowMultiple = true,
  }) async {
    try {

      List<File> selectedFiles = [];

      // Get temporary directory for file operations
      final tempDir = await getTemporaryDirectory();

      // Create XTypeGroups for file_selector
      List<XTypeGroup> acceptedTypeGroups = [];

      if (allowedExtensions.isNotEmpty) {
        // Create a type group for the allowed extensions
        acceptedTypeGroups.add(XTypeGroup(
          label: 'Allowed Files',
          extensions: allowedExtensions.map((ext) =>
            ext.startsWith('.') ? ext.substring(1) : ext
          ).toList(),
        ));
      } else {
        // If no extensions are specified, accept all files
        acceptedTypeGroups.add(const XTypeGroup(
          label: 'All Files',
          extensions: ['*'],
        ));
      }

      // Add a message to remind the user about allowed file types
      if (context.mounted) {
        final String allowedTypesMessage = allowedExtensions.isEmpty
            ? 'all files'
            : allowedExtensions.join(', ');

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Please select only files of type: $allowedTypesMessage',
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.bold,
              ),
            ),
            backgroundColor: Colors.blue,
            duration: const Duration(seconds: 5),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }

      // Use file_selector to pick files
      if (allowMultiple) {
        // Pick multiple files
        final List<XFile> pickedFiles = await openFiles(
          acceptedTypeGroups: acceptedTypeGroups,
        );

        // Validate file extensions after selection
        if (pickedFiles.isNotEmpty && allowedExtensions.isNotEmpty) {
          final List<XFile> validFiles = [];
          final List<String> invalidFiles = [];

          for (final xFile in pickedFiles) {
            final String extension = path.extension(xFile.name).toLowerCase();
            final bool isValid = allowedExtensions.any((ext) =>
              (ext.startsWith('.') ? ext.toLowerCase() : '.$ext'.toLowerCase()) == extension);

            if (isValid) {
              validFiles.add(xFile);
            } else {
              invalidFiles.add(xFile.name);
            }
          }

          // Show warning if invalid files were selected
          if (invalidFiles.isNotEmpty && context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Invalid file type(s): ${invalidFiles.join(', ')}. Only ${allowedExtensions.join(', ')} files are allowed.',
                  style: GoogleFonts.poppins(),
                ),
                backgroundColor: Colors.red,
                behavior: SnackBarBehavior.floating,
              ),
            );
          }

          // Continue only with valid files
          if (validFiles.isEmpty) {
            return null;
          }

          // Replace the original list with only valid files
          pickedFiles.clear();
          pickedFiles.addAll(validFiles);
        }

        if (pickedFiles.isNotEmpty) {
          for (final xFile in pickedFiles) {
            try {
              // Get the original file name and extension from the XFile
              final String originalName = xFile.name;
              final String originalPath = xFile.path;

              debugPrint('Original file name: $originalName');
              debugPrint('Original file path: $originalPath');

              // Create a file from the XFile path
              final File file = File(originalPath);

              // Check if the file exists
              if (await file.exists()) {
                // Check if the file has the correct extension
                final String fileExtension = path.extension(file.path).toLowerCase();
                final String originalExtension = path.extension(originalName).toLowerCase();

                debugPrint('File extension: $fileExtension');
                debugPrint('Original extension: $originalExtension');

                // Special handling for Sketchware Projects (.swb files)
                if (allowedExtensions.any((ext) => ext.toLowerCase() == '.swb' || ext.toLowerCase() == 'swb')) {
                  // Get the file content
                  final List<int> fileBytes = await file.readAsBytes();

                  // Always treat as .swb file when selecting a Sketchware Project
                  final String baseName = path.basenameWithoutExtension(file.path);
                  final String tempFilePath = '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}_$baseName.swb';
                  final File tempFile = File(tempFilePath);
                  await tempFile.writeAsBytes(fileBytes);

                  debugPrint('Created .swb file: ${tempFile.path}');
                  selectedFiles.add(tempFile);
                } else if (fileExtension == '.bin' && originalExtension != '.bin' && originalExtension.isNotEmpty) {
                  // For other files that got converted to .bin, restore the original extension
                  final List<int> fileBytes = await file.readAsBytes();

                  // Create a new file with the correct extension
                  final String baseName = path.basenameWithoutExtension(file.path);
                  final String newPath = '${tempDir.path}/$baseName$originalExtension';
                  final File newFile = File(newPath);
                  await newFile.writeAsBytes(fileBytes);

                  debugPrint('Created file with correct extension: ${newFile.path}');
                  selectedFiles.add(newFile);
                } else {
                  // Use the original file
                  debugPrint('Using original file: ${file.path}');
                  selectedFiles.add(file);
                }
              }
            } catch (e) {
              debugPrint('Error processing file: $e');
            }
          }
        }
      } else {
        // Pick a single file
        final XFile? pickedFile = await openFile(
          acceptedTypeGroups: acceptedTypeGroups,
        );

        // Validate file extension after selection
        if (pickedFile != null && allowedExtensions.isNotEmpty) {
          final String extension = path.extension(pickedFile.name).toLowerCase();
          final bool isValid = allowedExtensions.any((ext) =>
            (ext.startsWith('.') ? ext.toLowerCase() : '.$ext'.toLowerCase()) == extension);

          if (!isValid && context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Invalid file type: ${pickedFile.name}. Only ${allowedExtensions.join(', ')} files are allowed.',
                  style: GoogleFonts.poppins(),
                ),
                backgroundColor: Colors.red,
                behavior: SnackBarBehavior.floating,
              ),
            );
            return null;
          }
        }

        if (pickedFile != null) {
          try {
            // Get the original file name and extension from the XFile
            final String originalName = pickedFile.name;
            final String originalPath = pickedFile.path;

            debugPrint('Original file name: $originalName');
            debugPrint('Original file path: $originalPath');

            // Create a file from the XFile path
            final File file = File(originalPath);

            // Check if the file exists
            if (await file.exists()) {
              // Check if the file has the correct extension
              final String fileExtension = path.extension(file.path).toLowerCase();
              final String originalExtension = path.extension(originalName).toLowerCase();

              debugPrint('File extension: $fileExtension');
              debugPrint('Original extension: $originalExtension');

              // Get the file content
              final List<int> fileBytes = await file.readAsBytes();

              // Special handling for Sketchware Projects (.swb files)
              // If allowedExtensions is empty and we're in single file mode, assume it's a Sketchware Project
              if ((allowedExtensions.isEmpty && !allowMultiple) ||
                  allowedExtensions.any((ext) => ext.toLowerCase() == '.swb' || ext.toLowerCase() == 'swb')) {
                // Always treat as .swb file when selecting a Sketchware Project
                final String baseName = path.basenameWithoutExtension(file.path);
                final String tempFilePath = '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}_$baseName.swb';
                final File tempFile = File(tempFilePath);
                await tempFile.writeAsBytes(fileBytes);

                debugPrint('Created .swb file: ${tempFile.path}');
                selectedFiles.add(tempFile);
              } else if (fileExtension == '.bin') {
                // For other .bin files, try to determine the correct extension from allowed extensions
                String correctExtension = '.bin'; // Default to .bin if we can't determine

                if (allowedExtensions.isNotEmpty) {
                  // Use the first allowed extension
                  correctExtension = allowedExtensions.first.startsWith('.')
                      ? allowedExtensions.first
                      : '.${allowedExtensions.first}';
                }

                // Create a new file with the determined extension
                final String baseName = path.basenameWithoutExtension(file.path);
                final String tempFilePath = '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}_$baseName$correctExtension';
                final File tempFile = File(tempFilePath);
                await tempFile.writeAsBytes(fileBytes);

                debugPrint('Created file with extension $correctExtension: ${tempFile.path}');
                selectedFiles.add(tempFile);
              } else {
                // For other files, create a copy with the original name to preserve the extension
                final String tempFilePath = '${tempDir.path}/${DateTime.now().millisecondsSinceEpoch}_$originalName';
                final File tempFile = File(tempFilePath);
                await tempFile.writeAsBytes(fileBytes);

                debugPrint('Created file with original name: ${tempFile.path}');
                selectedFiles.add(tempFile);
              }
            }
          } catch (e) {
            debugPrint('Error processing file: $e');
          }
        }
      }

      // Return null if no files were selected
      if (selectedFiles.isEmpty) {
        return null;
      }

      // Log the selected files
      for (final file in selectedFiles) {
        debugPrint('Selected file path: ${file.path}');
        debugPrint('Selected file extension: ${path.extension(file.path)}');
      }

      // Show success message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Selected ${selectedFiles.length} ${selectedFiles.length == 1 ? 'file' : 'files'}',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }

      return selectedFiles;
    } catch (e) {
      debugPrint('Error picking files: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error picking files: $e',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
      return null;
    }
  }





  /// Process the selected files
  /// All files will be compressed to reduce database usage
  /// Returns the final file and its formatted size
  static Future<Map<String, dynamic>?> processFiles({
    required BuildContext context,
    required List<File> files,
  }) async {
    try {
      // Create a progress dialog
      double progressValue = 0.0;
      String progressMessage = 'Starting compression...';

      if (context.mounted) {
        // Show a progress dialog
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return StatefulBuilder(
              builder: (context, setState) {
                return AlertDialog(
                  title: Text(
                    'Compressing Files',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      LinearProgressIndicator(
                        value: progressValue,
                        backgroundColor: Colors.grey[200],
                        valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFF6A11CB)),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        progressMessage,
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${(progressValue * 100).toInt()}%',
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                );
              },
            );
          },
        );
      }

      // Always compress files to reduce database usage
      File finalFile;

      // Progress callback to update the dialog
      void updateProgress(double progress, String message) {
        if (context.mounted) {
          // Update the progress dialog
          (context as Element).markNeedsBuild();
          progressValue = progress;
          progressMessage = message;
        }
      }

      if (files.length > 1) {
        // Compress multiple files into a single zip with maximum compression
        finalFile = await FileCompressor.compressMultipleFiles(
          files,
          customFileName: 'asset_${DateTime.now().millisecondsSinceEpoch}.zip',
          progressCallback: updateProgress,
          compressionLevel: 9, // Maximum compression
        );
      } else {
        // Compress single file as well
        final String extension = path.extension(files.first.path).toLowerCase();
        // Don't compress if it's already a compressed format
        if (extension == '.zip' || extension == '.rar' ||
            extension == '.gz' || extension == '.7z') {
          finalFile = files.first;
          updateProgress(1.0, 'File already compressed, no further compression needed.');
        } else {
          // Compress the single file with maximum compression
          updateProgress(0.2, 'Preparing to compress file...');

          // For images, compress them first before adding to zip
          if (extension == '.jpg' || extension == '.jpeg' || extension == '.png') {
            updateProgress(0.3, 'Pre-compressing image before zipping...');
            final compressedImage = await ImageCompressor.compressImage(
              files.first,
              quality: 70,
              aggressive: true,
            );
            files = [compressedImage];
            updateProgress(0.5, 'Image pre-compressed, now creating zip archive...');
          }

          // Compress to zip with maximum compression
          finalFile = await FileCompressor.compressMultipleFiles(
            files,
            customFileName: 'asset_${DateTime.now().millisecondsSinceEpoch}.zip',
            progressCallback: (progress, message) {
              // Adjust progress to account for pre-compression step
              updateProgress(0.5 + (progress * 0.5), message);
            },
            compressionLevel: 9, // Maximum compression
          );
        }
      }

      // Close the progress dialog
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // Get file size
      final int fileSize = await finalFile.length();
      final String formattedSize = FileCompressor.formatFileSize(fileSize);

      // Show success message
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              files.length > 1
                  ? '${files.length} files compressed into ${path.basename(finalFile.path)} ($formattedSize)'
                  : 'File ready to upload: ${path.basename(finalFile.path)} ($formattedSize)',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }

      return {
        'file': finalFile,
        'size': formattedSize,
      };
    } catch (e) {
      debugPrint('Error processing files: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error processing files: $e',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
      return null;
    }
  }
}
