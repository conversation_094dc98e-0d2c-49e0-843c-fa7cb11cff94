import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'dart:math' as math;
import 'dart:async';
import 'package:firebase_auth/firebase_auth.dart';
import 'widgets/app_logo.dart';
import 'public_profile_page.dart';
import 'asset_detail_page.dart';
import 'messaging_page.dart';
import 'notifications_page.dart';
import 'saved_posts_page.dart';
import 'services/notification_service.dart';
import 'services/feedback_service.dart';
import 'services/responsive_layout_service.dart';
import 'services/search_service.dart';
import 'services/messaging_service.dart';
import 'utils/performance_optimizations.dart';
import 'utils/responsive_layout.dart';

class HomePageContent extends StatefulWidget {
  const HomePageContent({Key? key}) : super(key: key);

  @override
  State<HomePageContent> createState() => _HomePageContentState();
}

class _HomePageContentState extends State<HomePageContent> {
  final TextEditingController _searchController = TextEditingController();
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FeedbackService _feedbackService = FeedbackService();
  final SearchService _searchService = SearchService();
  String _searchQuery = '';
  bool _isSearching = false;
  List<String> _searchSuggestions = [];
  bool _showSuggestions = false;
  final bool _showFeedbackDialog = false;

  // Filter state variables
  String _selectedCategory = 'All';
  String _selectedPriceRange = 'All';
  final List<String> _categories = ['All', 'UI Kits', 'Templates', 'Blocks', 'Libraries', 'Icons', 'Code Snippets'];
  final List<String> _priceRanges = ['All', 'Free', 'Paid', 'Under ₹500', '₹500 - ₹1000', 'Above ₹1000'];

  // Scroll controllers for infinite scrolling
  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false;
  bool _hasMoreItems = true;

  // Mixed content list for the main feed
  final List<Map<String, dynamic>> _mixedContent = [];

  // SEO keywords for search suggestions
  final List<String> _seoKeywords = [
    'Mobile App', 'UI Kit', 'Material Design', 'iOS Design', 'Android',
    'Flutter', 'React Native', 'Sketchware', 'Kodular', 'Game',
    'Education', 'Social Media', 'E-commerce', 'Dashboard', 'Admin Panel',
    'Login Screen', 'Profile UI', 'Navigation Drawer', 'Bottom Navigation',
    'Dark Mode', 'Light Mode', 'Minimalist', 'Modern', 'Colorful',
    'Responsive', 'Clean Code', 'Well Documented', 'Easy to Customize',
    'Ready to Use', 'Premium', 'Free', 'Beginner Friendly', 'Advanced',
  ];

  // Sample trending assets data
  final List<Map<String, dynamic>> _trendingAssets = [
    {
      'title': 'Modern UI Kit',
      'author': 'David Miller',
      'image': 'assets/images/ui_kit.jpg',
      'type': 'UI Kits',
      'platform': 'Flutter',
      'price': 19.99,
      'downloads': 1250,
      'rating': 4.8,
    },
    {
      'title': 'E-commerce Template',
      'author': 'Sarah Johnson',
      'image': 'assets/images/ecommerce.jpg',
      'type': 'Templates',
      'platform': 'Sketchware',
      'price': 0.0,
      'downloads': 3420,
      'rating': 4.5,
    },
    {
      'title': 'Navigation Drawer Blocks',
      'author': 'Michael Chen',
      'image': 'assets/images/drawer.jpg',
      'type': 'Blocks',
      'platform': 'Sketchware',
      'price': 4.99,
      'downloads': 2150,
      'rating': 4.7,
    },
    {
      'title': 'Authentication Library',
      'author': 'Emma Wilson',
      'image': 'assets/images/auth.jpg',
      'type': 'Libraries',
      'platform': 'Kodular',
      'price': 9.99,
      'downloads': 1870,
      'rating': 4.6,
    },
  ];

  // Sample popular assets data
  final List<Map<String, dynamic>> _popularAssets = [
    {
      'title': 'Material Design Icons',
      'author': 'Alex Turner',
      'image': 'assets/images/icons.jpg',
      'type': 'Icons',
      'platform': 'All',
      'price': 0.0,
      'downloads': 5240,
      'rating': 4.9,
    },
    {
      'title': 'Social Media App Template',
      'author': 'Jessica Lee',
      'image': 'assets/images/social.jpg',
      'type': 'Templates',
      'platform': 'Android Studio',
      'price': 24.99,
      'downloads': 1980,
      'rating': 4.7,
    },
    {
      'title': 'Database Helper Library',
      'author': 'Ryan Garcia',
      'image': 'assets/images/database.jpg',
      'type': 'Libraries',
      'platform': 'VS Code',
      'price': 14.99,
      'downloads': 2340,
      'rating': 4.8,
    },
    {
      'title': 'Custom Button Collection',
      'author': 'Olivia Smith',
      'image': 'assets/images/buttons.jpg',
      'type': 'UI Kits',
      'platform': 'Flutter',
      'price': 7.99,
      'downloads': 3120,
      'rating': 4.6,
    },
  ];

  // Sample recommended users
  final List<Map<String, dynamic>> _recommendedUsers = [
    {
      'name': 'David Miller',
      'username': '@davidmiller',
      'avatar': 'assets/images/user1.jpg',
      'isVerified': true,
      'isElite': true,
      'followers': 1250,
    },
    {
      'name': 'Sarah Johnson',
      'username': '@sarahj',
      'avatar': 'assets/images/user2.jpg',
      'isVerified': true,
      'isElite': false,
      'followers': 980,
    },
    {
      'name': 'Michael Chen',
      'username': '@mikechen',
      'avatar': 'assets/images/user3.jpg',
      'isVerified': false,
      'isElite': false,
      'followers': 540,
    },
    {
      'name': 'Emma Wilson',
      'username': '@emmaw',
      'avatar': 'assets/images/user4.jpg',
      'isVerified': true,
      'isElite': false,
      'followers': 1120,
    },
  ];



  // Sample code snippets
  final List<Map<String, dynamic>> _codeSnippets = [
    {
      'author': 'David Miller',
      'title': 'Custom Button with Gradient Background',
      'language': 'Flutter/Dart',
      'time': '3 hours ago',
      'code': '''ElevatedButton(
  onPressed: () {},
  style: ElevatedButton.styleFrom(
    padding: EdgeInsets.zero,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(10),
    ),
  ),
  child: Ink(
    decoration: BoxDecoration(
      gradient: LinearGradient(
        colors: [Colors.purple, Colors.blue],
      ),
      borderRadius: BorderRadius.circular(10),
    ),
    child: Container(
      padding: EdgeInsets.all(10),
      child: Text('Gradient Button'),
    ),
  ),
)''',
      'likes': 128,
      'comments': 24,
      'liked': true,
      'saved': false,
    },
    {
      'author': 'Sarah Johnson',
      'title': 'Animated List Item',
      'language': 'Flutter/Dart',
      'time': '1 day ago',
      'code': '''AnimatedContainer(
  duration: Duration(milliseconds: 300),
  curve: Curves.easeInOut,
  height: _expanded ? 120 : 60,
  child: Card(
    child: Column(
      children: [
        ListTile(
          title: Text('Tap to expand'),
          trailing: Icon(_expanded
            ? Icons.arrow_drop_up
            : Icons.arrow_drop_down),
          onTap: () {
            setState(() {
              _expanded = !_expanded;
            });
          },
        ),
        if (_expanded)
          Padding(
            padding: EdgeInsets.all(8.0),
            child: Text('Additional content here'),
          ),
      ],
    ),
  ),
)''',
      'likes': 95,
      'comments': 12,
      'liked': false,
      'saved': true,
    },
    {
      'author': 'Michael Chen',
      'title': 'Firebase Authentication Helper',
      'language': 'Dart',
      'time': '2 days ago',
      'code': '''class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Sign in with email & password
  Future<UserCredential?> signInWithEmailAndPassword(
      String email, String password) async {
    try {
      return await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
    } on FirebaseAuthException catch (error) {
      print('Error: \${error.code}');
      return null;
    }
  }

  // Register with email & password
  Future<UserCredential?> registerWithEmailAndPassword(
      String email, String password) async {
    try {
      return await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
    } on FirebaseAuthException catch (error) {
      print('Error: \${error.code}');
      return null;
    }
  }
}''',
      'likes': 210,
      'comments': 35,
      'liked': false,
      'saved': false,
    },
  ];

  @override
  void initState() {
    super.initState();

    // Initialize mixed content with assets, projects, and code snippets
    _initializeMixedContent();

    // Set up scroll controller for infinite scrolling
    _scrollController.addListener(() {
      if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent * 0.8 &&
          !_isLoadingMore &&
          _hasMoreItems) {
        _loadMoreItems();
      }
    });

    // Set up search controller listener for suggestions
    _searchController.addListener(_updateSearchSuggestions);

    // Refresh notification count
    if (_auth.currentUser != null) {
      NotificationService().refreshUnreadNotificationCount(_auth.currentUser!.uid);
    }
  }

  // Update search suggestions based on current input
  Timer? _suggestionsDebounceTimer;

  void _updateSearchSuggestions() {
    final query = _searchController.text.toLowerCase();
    if (query.isEmpty) {
      setState(() {
        _searchSuggestions = [];
        _showSuggestions = false;
        _searchQuery = '';
      });
      return;
    }

    // Cancel any previous timer
    _suggestionsDebounceTimer?.cancel();

    // Debounce to avoid too many requests while typing
    _suggestionsDebounceTimer = Timer(const Duration(milliseconds: 300), () async {
      try {
        // Get suggestions from search service based on user's previous searches
        final serviceSuggestions = await _searchService.getSearchSuggestions(query, 'asset');

        if (!mounted) return;

        // If we have enough service suggestions, use them
        if (serviceSuggestions.length >= 3) {
          setState(() {
            _searchSuggestions = serviceSuggestions;
            _showSuggestions = serviceSuggestions.isNotEmpty;
            _searchQuery = query;
          });
          return;
        }

        // Otherwise, supplement with SEO keywords
        final keywordSuggestions = _seoKeywords
            .where((keyword) => keyword.toLowerCase().contains(query))
            .take(5 - serviceSuggestions.length)
            .toList();

        final combinedSuggestions = [...serviceSuggestions, ...keywordSuggestions];

        if (!mounted) return;

        setState(() {
          _searchSuggestions = combinedSuggestions;
          _showSuggestions = combinedSuggestions.isNotEmpty;
          _searchQuery = query; // Set search query even if no suggestions
        });
      } catch (e) {
        // Use a logging framework in production
        debugPrint('Error getting search suggestions: $e');

        if (!mounted) return;

        // Fallback to local suggestions
        final suggestions = _seoKeywords
            .where((keyword) => keyword.toLowerCase().contains(query))
            .take(5)
            .toList();

        setState(() {
          _searchSuggestions = suggestions;
          _showSuggestions = suggestions.isNotEmpty;
          _searchQuery = query;
        });
      }
    });
  }

  // Perform search with Firebase and user interaction tracking
  Future<List<Map<String, dynamic>>> _performSearch(String query) async {
    if (query.isEmpty) return [];

    try {
      // Use the search service to get assets based on the query
      final assets = await _searchService.searchAssets(query);

      // Convert AssetModel objects to Map format for display
      final results = assets.map((asset) => {
        'id': asset.id ?? '',
        'title': asset.title ?? 'Untitled Asset',
        'author': asset.authorName ?? 'Unknown Author',
        'image': asset.thumbnailUrl ?? '',
        'type': asset.category ?? 'Uncategorized',
        'platform': asset.platform ?? 'All Platforms',
        'price': asset.price ?? 0.0,
        'downloads': asset.downloadCount ?? 0,
        'rating': asset.rating ?? 0.0,
        'contentType': 'asset',
        'isWide': false,
        'heightFactor': 1,
      }).toList();

      // If we don't have enough results from Firebase, supplement with local data
      if (results.length < 5) {
        // Convert query to lowercase for case-insensitive search
        final lowerQuery = query.toLowerCase();

        // Local search function
        List<Map<String, dynamic>> performLocalSearch() {
          // Extract keywords from the query
          final queryKeywords = lowerQuery.split(' ')
              .where((word) => word.length > 2) // Only consider words with 3+ characters
              .toList();

          // Score function for ranking results
          int getSearchScore(Map<String, dynamic> item) {
            int score = 0;

            // Check title
            final title = (item['title'] ?? '').toLowerCase();
            if (title.contains(lowerQuery)) {
              score += 10; // Exact match in title
            }

            // Check for individual keywords in title
            for (final keyword in queryKeywords) {
              if (title.contains(keyword)) {
                score += 5;
              }
            }

            // Check type and platform
            final type = (item['type'] ?? '').toLowerCase();
            final platform = (item['platform'] ?? '').toLowerCase();

            if (type.contains(lowerQuery) || lowerQuery.contains(type)) {
              score += 8; // Type match
            }

            if (platform.contains(lowerQuery) || lowerQuery.contains(platform)) {
              score += 8; // Platform match
            }

            // Check author
            final author = (item['author'] ?? '').toLowerCase();
            if (author.contains(lowerQuery)) {
              score += 6; // Author match
            }

            // Boost for popular items
            final downloads = item['downloads'] ?? 0;
            if (downloads > 1000) {
              score += 3;
            }

            final rating = item['rating'] ?? 0.0;
            if (rating > 4.5) {
              score += 2;
            }

            return score;
          }

          // Filter and score items
          final localResults = [..._trendingAssets, ..._popularAssets, ..._codeSnippets]
              .where((item) {
                // Basic filtering
                final title = (item['title'] ?? '').toLowerCase();
                final type = (item['type'] ?? '').toLowerCase();
                final platform = (item['platform'] ?? '').toLowerCase();
                final author = (item['author'] ?? '').toLowerCase();

                // Check if any field contains the query
                return title.contains(lowerQuery) ||
                       type.contains(lowerQuery) ||
                       platform.contains(lowerQuery) ||
                       author.contains(lowerQuery);
              })
              .toList();

          // Sort by score (highest first)
          localResults.sort((a, b) => getSearchScore(b).compareTo(getSearchScore(a)));

          return localResults;
        }

        // Get local search results
        final localResults = performLocalSearch();

        // Add local results that aren't already in the results
        for (final localResult in localResults) {
          if (results.length >= 20) break; // Limit to 20 results

          // Check if this result is already in the list
          final isDuplicate = results.any((result) =>
              result['title'] == localResult['title'] &&
              result['author'] == localResult['author']);

          if (!isDuplicate) {
            // Convert to Map<String, Object> to satisfy type requirements
            Map<String, Object> convertedResult = {};
            localResult.forEach((key, value) {
              if (value != null) {
                convertedResult[key] = value;
              } else {
                // Handle null values by providing defaults based on key type
                if (key == 'id' || key == 'title' || key == 'author' || key == 'type') {
                  convertedResult[key] = '';
                } else if (key == 'price' || key == 'rating') {
                  convertedResult[key] = 0.0;
                } else if (key == 'downloads') {
                  convertedResult[key] = 0;
                } else if (key == 'isWide') {
                  convertedResult[key] = false;
                } else {
                  convertedResult[key] = '';
                }
              }
            });
            results.add(convertedResult);
          }
        }
      }

      return results;
    } catch (e) {
      // Fallback to local search if Firebase search fails
      // Use a logging framework in production
      debugPrint('Error performing search: $e');

      // Convert query to lowercase for case-insensitive search
      final lowerQuery = query.toLowerCase();

      // Extract keywords from the query
      final queryKeywords = lowerQuery.split(' ')
          .where((word) => word.length > 2) // Only consider words with 3+ characters
          .toList();

      // Score function for ranking results
      int getSearchScore(Map<String, dynamic> item) {
        int score = 0;

        // Check title
        final title = (item['title'] ?? '').toLowerCase();
        if (title.contains(lowerQuery)) {
          score += 10; // Exact match in title
        }

        // Check for individual keywords in title
        for (final keyword in queryKeywords) {
          if (title.contains(keyword)) {
            score += 5;
          }
        }

        // Check type and platform
        final type = (item['type'] ?? '').toLowerCase();
        final platform = (item['platform'] ?? '').toLowerCase();

        if (type.contains(lowerQuery) || lowerQuery.contains(type)) {
          score += 8; // Type match
        }

        if (platform.contains(lowerQuery) || lowerQuery.contains(platform)) {
          score += 8; // Platform match
        }

        // Check author
        final author = (item['author'] ?? '').toLowerCase();
        if (author.contains(lowerQuery)) {
          score += 6; // Author match
        }

        // Boost for popular items
        final downloads = item['downloads'] ?? 0;
        if (downloads > 1000) {
          score += 3;
        }

        final rating = item['rating'] ?? 0.0;
        if (rating > 4.5) {
          score += 2;
        }

        return score;
      }

      // Filter and score items
      final results = [..._trendingAssets, ..._popularAssets, ..._codeSnippets]
          .where((item) {
            // Basic filtering
            final title = (item['title'] ?? '').toLowerCase();
            final type = (item['type'] ?? '').toLowerCase();
            final platform = (item['platform'] ?? '').toLowerCase();
            final author = (item['author'] ?? '').toLowerCase();

            // Check if any field contains the query
            return title.contains(lowerQuery) ||
                   type.contains(lowerQuery) ||
                   platform.contains(lowerQuery) ||
                   author.contains(lowerQuery);
          })
          .toList();

      // Sort by score (highest first)
      results.sort((a, b) => getSearchScore(b).compareTo(getSearchScore(a)));

      return results;
    }
  }




  // Track user interaction with search results
  void _trackSearchInteraction(String query, String resultId) {
    _searchService.trackSearchInteraction(
      query: query,
      resultId: resultId,
      resultType: 'asset',
    );
  }

  // Initialize mixed content
  void _initializeMixedContent() {
    // Add assets with varying sizes
    for (int i = 0; i < _trendingAssets.length; i++) {
      final asset = _trendingAssets[i];

      // Determine if this should be a featured (wide) item
      final bool isWide = i % 5 == 0; // Every 5th item is wide
      final int heightFactor = 1 + math.Random().nextInt(2); // Random height (1 or 2)

      _mixedContent.add({
        ...asset,
        'contentType': 'asset',
        'isWide': isWide,
        'heightFactor': heightFactor,
      });
    }

    // Add popular assets with varying sizes
    for (int i = 0; i < _popularAssets.length; i++) {
      final asset = _popularAssets[i];

      // Determine if this should be a featured (wide) item
      final bool isWide = i % 4 == 0; // Every 4th item is wide
      final int heightFactor = 1 + math.Random().nextInt(2); // Random height (1 or 2)

      _mixedContent.add({
        ...asset,
        'contentType': 'asset',
        'isWide': isWide,
        'heightFactor': heightFactor,
      });
    }

    // Add code snippets with varying sizes
    for (int i = 0; i < _codeSnippets.length; i++) {
      final snippet = _codeSnippets[i];

      // Alternate between wide and narrow, and between tall and short
      final bool isWide = i % 2 == 0; // Even indices are wide
      final int heightFactor = (i % 3 == 0) ? 1 : 2; // Every third is shorter

      _mixedContent.add({
        ...snippet,
        'contentType': 'code',
        'isWide': isWide,
        'heightFactor': heightFactor,
      });
    }

    // Add suggested users to follow (every 5-7 items)
    final userIndices = [5, 12, 20, 28, 35, 42]; // Positions to insert suggested users
    for (final index in userIndices) {
      if (index < _mixedContent.length) {
        // Get a random user to suggest
        final user = _recommendedUsers[math.Random().nextInt(_recommendedUsers.length)];
        // Alternate between wide and narrow user suggestions
        final bool isWide = index % 2 == 0;
        _mixedContent.insert(index, {
          ...user,
          'contentType': 'user',
          'isWide': isWide,
          'heightFactor': 1, // Standard height
        });
      }
    }

    // Shuffle the content for a mixed feed
    _mixedContent.shuffle();
  }

  // Apply filters to the content
  void _applyFilters() {
    setState(() {
      // Clear the current content
      _mixedContent.clear();

      // Reset loading state
      _isLoadingMore = false;
      _hasMoreItems = true;

      // Load filtered content
      _loadMoreItems(applyFilters: true);
    });
  }

  // Load more items for infinite scrolling
  void _loadMoreItems({bool applyFilters = false}) {
    if (_isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
    });

    // Simulate loading more items
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          // In a real app, you would fetch more items from an API
          List<Map<String, dynamic>> itemsToAdd = [];

          if (applyFilters) {
            // Apply category filter
            List<Map<String, dynamic>> filteredItems = [];

            // Get all available items (in a real app, this would be from an API)
            List<Map<String, dynamic>> allItems = [];
            allItems.addAll(_trendingAssets);
            allItems.addAll(_popularAssets);
            allItems.addAll(_codeSnippets);

            // Apply category filter
            if (_selectedCategory != 'All') {
              filteredItems = allItems.where((item) {
                // Check if item matches the selected category
                if (item.containsKey('type')) {
                  return item['type'] == _selectedCategory;
                }
                return false;
              }).toList();
            } else {
              filteredItems = allItems;
            }

            // Apply price filter
            if (_selectedPriceRange != 'All') {
              filteredItems = filteredItems.where((item) {
                if (!item.containsKey('price')) return false;

                final price = item['price'] is int ? item['price'] : 0;

                switch (_selectedPriceRange) {
                  case 'Free':
                    return price == 0;
                  case 'Paid':
                    return price > 0;
                  case 'Under ₹500':
                    return price > 0 && price < 500;
                  case '₹500 - ₹1000':
                    return price >= 500 && price <= 1000;
                  case 'Above ₹1000':
                    return price > 1000;
                  default:
                    return true;
                }
              }).toList();
            }

            // Format items for display
            for (int i = 0; i < filteredItems.length; i++) {
              final item = filteredItems[i];

              // Determine if this should be a featured (wide) item
              final bool isWide = i % 5 == 0; // Every 5th item is wide
              final int heightFactor = 1 + math.Random().nextInt(2); // Random height (1 or 2)

              final contentType = item.containsKey('code') ? 'code' : 'asset';

              itemsToAdd.add({
                ...item,
                'contentType': contentType,
                'isWide': isWide,
                'heightFactor': heightFactor,
              });
            }

            // Shuffle the filtered items
            itemsToAdd.shuffle();

            // Take only a subset for this page
            itemsToAdd = itemsToAdd.take(10).toList();
          } else {
            // For normal loading, just add duplicates with modified titles
            itemsToAdd = List.of(_mixedContent.take(6)).map((item) {
              final newItem = Map<String, dynamic>.from(item);
              if (newItem.containsKey('title')) {
                newItem['title'] = '${newItem['title']} (New)';
              }
              return newItem;
            }).toList();
          }

          _mixedContent.addAll(itemsToAdd);
          _isLoadingMore = false;

          // For demo purposes, limit the number of items to load
          if (_mixedContent.length > 50) {
            _hasMoreItems = false;
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  // Build the app bar with search functionality and app logo
  Widget _buildAppBar() {
    // Use responsive height based on screen size - increased to prevent overflow
    final expandedHeight = ResponsiveLayout.getValueForScreenType<double>(
      context: context,
      mobile: _isSearching ? 90 : 120, // Increased heights to prevent overflow
      tablet: _isSearching ? 110 : 140,
      desktop: _isSearching ? 130 : 160,
    );

    return SliverAppBar(
      floating: true,
      pinned: true,
      backgroundColor: Colors.white,
      elevation: 0,
      expandedHeight: expandedHeight,
      toolbarHeight: 56, // Explicitly set toolbar height
      title: _isSearching
          ? Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    autofocus: true,
                    decoration: InputDecoration(
                      hintText: 'Search...',
                      border: InputBorder.none,
                      hintStyle: GoogleFonts.poppins(
                        color: Colors.grey[400],
                        fontSize: 16,
                      ),
                      prefixIcon: const Icon(Icons.search, color: Colors.grey),
                      suffixIcon: IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          setState(() {
                            _searchController.clear();
                            _searchQuery = '';
                          });
                        },
                      ),
                    ),
                    onChanged: (value) {
                      setState(() {
                        _searchQuery = value;
                        _showSuggestions = value.isNotEmpty;
                      });

                      // Perform search as user types
                      if (value.length > 2) { // Only search if at least 3 characters
                        setState(() {
                          _searchQuery = value;
                        });
                      }
                    },
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                    ),
                  ),
                ),
                // Add filter button to search bar when searching
                IconButton(
                  icon: const Icon(Icons.filter_list, color: Color(0xFF6A11CB)),
                  onPressed: () {
                    _showFilterOptions(context);
                  },
                  tooltip: 'Filter',
                ),
              ],
            )
          : Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // App logo
                const AppLogo(
                  size: 28,
                  isAnimated: false,
                ),
                const SizedBox(width: 8),
                Text(
                  'Asatu',
                  style: GoogleFonts.poppins(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
      actions: [
        if (_isSearching)
          IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black87),
            onPressed: () {
              setState(() {
                _isSearching = false;
                _searchController.clear();
                _searchQuery = '';
              });
            },
          )
        else ...[
          IconButton(
            icon: const Icon(Icons.search, color: Colors.black87),
            onPressed: () {
              setState(() {
                _isSearching = true;
              });
            },
          ),
          // Message icon added to top bar with dynamic unread count
          StreamBuilder<int>(
            stream: MessagingService().getTotalUnreadMessageCount(),
            builder: (context, snapshot) {
              final int unreadCount = snapshot.data ?? 0;

              return IconButton(
                icon: Stack(
                  children: [
                    const Icon(Icons.message_rounded, color: Colors.black87),
                    if (unreadCount > 0)
                      Positioned(
                        right: 0,
                        top: 0,
                        child: Container(
                          padding: const EdgeInsets.all(1),
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          constraints: const BoxConstraints(
                            minWidth: 12,
                            minHeight: 12,
                          ),
                          child: Text(
                            unreadCount.toString(),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 8,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                  ],
                ),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const MessagingPage(),
                    ),
                  );
                },
              );
            },
          ),
          // Only show notifications and bookmark on larger screens to prevent overflow
          if (MediaQuery.of(context).size.width > 400) ...[
            StreamBuilder<int>(
              stream: NotificationService().getUnreadNotificationCount(_auth.currentUser?.uid ?? ''),
              builder: (context, snapshot) {
                final int unreadCount = snapshot.data ?? 0;

                return IconButton(
                  icon: Stack(
                    children: [
                      const Icon(Icons.notifications_outlined, color: Colors.black87),
                      if (unreadCount > 0)
                        Positioned(
                          right: 0,
                          top: 0,
                          child: Container(
                            padding: const EdgeInsets.all(1),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(6),
                            ),
                            constraints: const BoxConstraints(
                              minWidth: 12,
                              minHeight: 12,
                            ),
                            child: Text(
                              unreadCount.toString(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 8,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                    ],
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const NotificationsPage(),
                      ),
                    );
                  },
                );
              },
            ),
            IconButton(
              icon: const Icon(Icons.bookmark_border, color: Colors.black87),
              onPressed: () {
                // Navigate to saved posts page
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SavedPostsPage(),
                  ),
                );
              },
            ),
          ] else
            // On smaller screens, show a menu button for additional options
            PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert, color: Colors.black87),
              onSelected: (value) {
                switch (value) {
                  case 'notifications':
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const NotificationsPage(),
                      ),
                    );
                    break;
                  case 'saved':
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const SavedPostsPage(),
                      ),
                    );
                    break;
                }
              },
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'notifications',
                  child: Row(
                    children: [
                      const Icon(Icons.notifications_outlined),
                      const SizedBox(width: 8),
                      Text('Notifications', style: GoogleFonts.poppins()),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'saved',
                  child: Row(
                    children: [
                      const Icon(Icons.bookmark_border),
                      const SizedBox(width: 8),
                      Text('Saved Posts', style: GoogleFonts.poppins()),
                    ],
                  ),
                ),
              ],
            ),
          const SizedBox(width: 4), // Reduced width to save space
        ],
      ],
      bottom: _isSearching
          ? null
          : PreferredSize(
              preferredSize: const Size.fromHeight(56), // Increased height to prevent overflow
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6), // Increased vertical padding
                child: SizedBox(
                  height: 44, // Fixed height to prevent overflow
                  child: TextField(
                    decoration: InputDecoration(
                      hintText: 'Search assets, creators, etc.',
                      prefixIcon: const Icon(Icons.search, color: Colors.grey, size: 20),
                      filled: true,
                      fillColor: Colors.grey[100],
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none,
                      ),
                      contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16), // Fixed padding
                      isDense: true,
                      hintStyle: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.grey[400],
                      ),
                    ),
                    onTap: () {
                      setState(() {
                        _isSearching = true;
                      });
                    },
                    readOnly: true,
                  ),
                ),
              ),
            ),
    );
  }

  // Build quick action buttons
  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Quick Actions',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            // Feedback badge positioned in quick actions section - using responsive layout service
            GestureDetector(
              onTap: _showFeedbackForm,
              child: Container(
                padding: context.responsivePadding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  smallScreenPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFF6A11CB),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(4),
                    topRight: Radius.circular(18),
                    bottomLeft: Radius.circular(18),
                    bottomRight: Radius.circular(4),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(50),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: ResponsiveLayoutService().getSafeRow(
                  children: [
                    Icon(
                      Icons.feedback_outlined,
                      color: Colors.white,
                      size: context.responsiveValue(
                        extraSmall: 14.0,
                        small: 16.0,
                        medium: 18.0,
                      ),
                    ),
                    SizedBox(width: context.isSmallScreen ? 4 : 6),
                    Text(
                      'Feedback',
                      style: GoogleFonts.poppins(
                        color: Colors.white,
                        fontSize: context.responsiveFontSize(
                          size: 14,
                          smallScreenSize: 12,
                        ),
                        fontWeight: FontWeight.bold,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: [
            _buildActionButton('Trending', Icons.trending_up, () {
              // Show trending content
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Showing trending content...', 
                    style: GoogleFonts.poppins(),
                  ),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            }),
            _buildActionButton('New', Icons.fiber_new, () {
              // Show new content
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Showing newest content...', 
                    style: GoogleFonts.poppins(),
                  ),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            }),
            _buildActionButton('Popular', Icons.star, () {
              // Show popular content
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Showing popular content...', 
                    style: GoogleFonts.poppins(),
                  ),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            }),
            _buildActionButton('Free', Icons.money_off, () {
              // Show free content
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Showing free content...', 
                    style: GoogleFonts.poppins(),
                  ),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            }),
          ],
        ),
      ],
    );
  }

  // Build action button
  Widget _buildActionButton(String label, IconData icon, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(10),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: const Color(0xFF6A11CB),
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.w600,
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build trending assets section
  Widget _buildTrendingSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Trending Now',
              style: GoogleFonts.poppins(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            TextButton(
              onPressed: () {
                // Navigate to trending page
              },
              child: Text(
                'See All',
                style: GoogleFonts.poppins(
                  color: const Color(0xFF6A11CB),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 280,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _trendingAssets.length,
            itemBuilder: (context, index) {
              final asset = _trendingAssets[index];
              return _buildAssetCard(asset, featured: true);
            },
          ),
        ),
      ],
    );
  }

  // Build main content with brick grid layout
  Widget _buildMainContent() {
    // Get responsive column count based on screen size
    final gridColumns = ResponsiveLayout.getGridColumnsForScreenType(
      context: context,
      mobile: 4, // 4 columns on mobile (2 items per row)
      tablet: 6, // 6 columns on tablet (3 items per row)
      desktop: 8, // 8 columns on desktop (4 items per row)
    );

    // Get responsive padding based on screen size
    final horizontalPadding = ResponsiveLayout.getValueForScreenType<double>(
      context: context,
      mobile: 16,
      tablet: 24,
      desktop: 32,
    );

    return SliverList(
      delegate: SliverChildListDelegate([
        Padding(
          padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Top creators section
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Top Creators',
                    style: GoogleFonts.poppins(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      // Navigate to creators page
                    },
                    child: Text(
                      'See All',
                      style: GoogleFonts.poppins(
                        color: const Color(0xFF6A11CB),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // User cards
              SizedBox(
                height: 220,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: _recommendedUsers.length,
                  itemBuilder: (context, index) {
                    final user = _recommendedUsers[index];
                    return _buildUserCard(user);
                  },
                ),
              ),

              const SizedBox(height: 32),

              // Mixed content feed title
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'For You',
                    style: GoogleFonts.poppins(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      // Refresh content
                      setState(() {
                        _mixedContent.shuffle();
                      });
                    },
                    child: Row(
                      children: [
                        const Icon(Icons.refresh, size: 16, color: Color(0xFF6A11CB)),
                        const SizedBox(width: 4),
                        Text(
                          'Refresh',
                          style: GoogleFonts.poppins(
                            color: const Color(0xFF6A11CB),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),

        // Staggered grid layout for mixed content
        Padding(
          padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
          child: Container(
            child: StaggeredGrid.count(
              crossAxisCount: gridColumns, // Using responsive column count
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              children: List.generate(_mixedContent.length, (index) {
                final item = _mixedContent[index];
                final bool isWide = item['isWide'] ?? false;

                // Calculate the cross axis cell count (width)
                final int crossAxisCellCount = isWide ? gridColumns : (gridColumns ~/ 2);

                // We're using fit-content approach so we don't need heightFactor anymore
                // This allows content to determine its own height

                return StaggeredGridTile.fit(
                  crossAxisCellCount: crossAxisCellCount,
                  child: Container(
                    child: _buildContentItem(item),
                  ),
                );
              }),
            ),
          ),
        ),
      ]),
    );
  }

  // Build content item
  Widget _buildContentItem(Map<String, dynamic> item) {
    // Handle different content types
    if (item['contentType'] == 'user') {
      return _buildSuggestedUserCard(item);
    }

    final bool isWide = item['isWide'] ?? false;

    // For assets and code snippets, add share and save buttons
    return GestureDetector(
      onTap: () {
        // Navigate to asset detail page if it's an asset
        if (item['contentType'] != 'code') {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AssetDetailPage(asset: item),
            ),
          );
        } else {
          // For code snippets, show code dialog
          _showCodeDialog(item);
        }
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(10),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: isWide
            ? _buildWideContentItem(item)
            : _buildNormalContentItem(item),
      ),
    );
  }

  // Build normal (half-width) content item
  Widget _buildNormalContentItem(Map<String, dynamic> item) {
    return SingleChildScrollView(
      physics: const NeverScrollableScrollPhysics(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Content
          ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(12),
              topRight: Radius.circular(12),
            ),
            child: item['contentType'] == 'code'
                ? _buildCodeContent(item)
                : _buildAssetContent(item),
          ),

          // Title and action buttons
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title
                Text(
                  item['title'] ?? 'Untitled',
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 4),

                // Author (clickable)
                GestureDetector(
                  onTap: () {
                    // Navigate to publisher profile
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const PublicProfilePage(),
                      ),
                    );
                  },
                  child: Text(
                    'by ${item['author']}',
                    style: GoogleFonts.poppins(
                      color: const Color(0xFF6A11CB),
                      fontSize: 12,
                    ),
                  ),
                ),

                const SizedBox(height: 8),

                // Action buttons
                Row(
                  children: [
                    // Price or free tag
                    if (item.containsKey('price'))
                      Text(
                        item['price'] > 0 ? '\$${item['price']}' : 'Free',
                        style: GoogleFonts.poppins(
                          color: item['price'] > 0 ? Colors.black87 : Colors.green[600],
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),

                    const Spacer(),

                    // Share button
                    InkWell(
                      onTap: () {
                        // Share logic
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'Sharing ${item['title']}...', 
                              style: GoogleFonts.poppins(),
                            ),
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                      },
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(Icons.share, size: 16, color: Colors.black87),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Save button
                    InkWell(
                      onTap: () {
                        // Save to wishlist logic
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'Added ${item['title']} to wishlist', 
                              style: GoogleFonts.poppins(),
                            ),
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                      },
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(Icons.bookmark_border, size: 16, color: Colors.black87),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Build wide (full-width) content item
  Widget _buildWideContentItem(Map<String, dynamic> item) {
    if (item['contentType'] == 'code') {
      // For code snippets, use a similar layout but with more space for code
      return SingleChildScrollView(
        physics: const NeverScrollableScrollPhysics(),
        child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and author
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: Colors.grey[200],
                  child: Text(
                    item['author'].substring(0, 1),
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF6A11CB),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item['title'] ?? 'Untitled',
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      GestureDetector(
                        onTap: () {
                          // Navigate to publisher profile
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const PublicProfilePage(),
                            ),
                          );
                        },
                        child: Text(
                          'by ${item['author']}',
                          style: GoogleFonts.poppins(
                            color: const Color(0xFF6A11CB),
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Row(
                  children: [
                    // Share button
                    InkWell(
                      onTap: () {
                        // Share logic
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'Sharing ${item['title']}...', 
                              style: GoogleFonts.poppins(),
                            ),
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                      },
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(Icons.share, size: 18, color: Colors.black87),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Save button
                    InkWell(
                      onTap: () {
                        // Save to wishlist logic
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'Added ${item['title']} to wishlist', 
                              style: GoogleFonts.poppins(),
                            ),
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                      },
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(Icons.bookmark_border, size: 18, color: Colors.black87),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Code content
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: _buildCodeContent(item, isWide: true),
          ),

          // Actions
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Like button
                InkWell(
                  onTap: () {
                    // Toggle like status
                    setState(() {
                      final Map<String, dynamic> updatedItem = Map<String, dynamic>.from(item);
                      updatedItem['liked'] = !(item['liked'] == true);

                      // Update like count
                      if (updatedItem['liked']) {
                        updatedItem['likes'] = (item['likes'] ?? 0) + 1;
                      } else {
                        updatedItem['likes'] = (item['likes'] ?? 1) - 1;
                      }

                      // Find and update the item in the mixed content list
                      final index = _mixedContent.indexWhere((element) =>
                        element['contentType'] == 'code' &&
                        element['title'] == item['title'] &&
                        element['author'] == item['author']);

                      if (index != -1) {
                        _mixedContent[index] = updatedItem;
                      }
                    });

                    // Show feedback
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          item['liked'] == true ? 'Unliked ${item['title']}' : 'Liked ${item['title']}', 
                          style: GoogleFonts.poppins(),
                        ),
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  },
                  child: Row(
                    children: [
                      Icon(
                        Icons.favorite,
                        color: item['liked'] == true ? Colors.red : Colors.grey[400],
                        size: 20,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _formatNumber(item['likes'] ?? 0),
                        style: GoogleFonts.poppins(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 16),

                // Comment button
                InkWell(
                  onTap: () {
                    // Show comment dialog
                    _showCommentDialog(item);
                  },
                  child: Row(
                    children: [
                      Icon(
                        Icons.comment,
                        color: Colors.grey[400],
                        size: 20,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _formatNumber(item['comments'] ?? 0),
                        style: GoogleFonts.poppins(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      );
    } else {
      // For assets, use a horizontal layout
      return SingleChildScrollView(
        physics: const NeverScrollableScrollPhysics(),
        child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Asset image
          ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(12),
              bottomLeft: Radius.circular(12),
            ),
            child: SizedBox(
              width: 140,
              height: 140,
              child: _buildAssetContent(item),
            ),
          ),

          // Content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Type and platform tags
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: const Color(0xFF6A11CB).withAlpha(30),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              item['type'] ?? 'Asset',
                              style: GoogleFonts.poppins(
                                color: const Color(0xFF6A11CB),
                                fontSize: 10,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          if (item.containsKey('platform')) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.blue.withAlpha(30),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                item['platform'],
                                style: GoogleFonts.poppins(
                                  color: Colors.blue[700],
                                  fontSize: 10,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),

                      const SizedBox(height: 8),

                      // Title
                      Text(
                        item['title'] ?? 'Untitled',
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: 4),

                      // Author
                      Text(
                        'by ${item['author']}',
                        style: GoogleFonts.poppins(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // Action buttons
                  Row(
                    children: [
                      // Price or free tag
                      if (item.containsKey('price'))
                        Text(
                          item['price'] > 0 ? '\$${item['price']}' : 'Free',
                          style: GoogleFonts.poppins(
                            color: item['price'] > 0 ? Colors.black87 : Colors.green[600],
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),

                      const Spacer(),

                      // Share button
                      InkWell(
                        onTap: () {
                          // Share logic
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'Sharing ${item['title']}...', 
                                style: GoogleFonts.poppins(),
                              ),
                              behavior: SnackBarBehavior.floating,
                            ),
                          );
                        },
                        child: Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(Icons.share, size: 16, color: Colors.black87),
                        ),
                      ),

                      const SizedBox(width: 8),

                      // Save button
                      InkWell(
                        onTap: () {
                          // Save to wishlist logic
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'Added ${item['title']} to wishlist', 
                                style: GoogleFonts.poppins(),
                              ),
                              behavior: SnackBarBehavior.floating,
                            ),
                          );
                        },
                        child: Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(Icons.bookmark_border, size: 16, color: Colors.black87),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      );
    }
  }

  // Build suggested user card
  Widget _buildSuggestedUserCard(Map<String, dynamic> user) {
    final bool isWide = user['isWide'] ?? true;

    if (isWide) {
      // Wide format (horizontal layout)
      return GestureDetector(
        onTap: () {
          // Navigate to public profile page
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const PublicProfilePage(),
            ),
          );
        },
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(10),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
        child: Column(
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFF6A11CB).withAlpha(15),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Text(
                'Suggested to Follow',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                  color: const Color(0xFF6A11CB),
                ),
                textAlign: TextAlign.center,
              ),
            ),

            // User info
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // Avatar
                  CircleAvatar(
                    radius: 24,
                    backgroundColor: Colors.grey[200],
                    child: Text(
                      user['name'].substring(0, 1),
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF6A11CB),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),

                  // User details
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          user['name'],
                          style: GoogleFonts.poppins(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                        Text(
                          user['username'],
                          style: GoogleFonts.poppins(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                        Text(
                          '${_formatNumber(user['followers'])} followers',
                          style: GoogleFonts.poppins(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Follow button
                  ElevatedButton(
                    onPressed: () {
                      // Follow user logic
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            'Following ${user['name']}',
                            style: GoogleFonts.poppins(),
                          ),
                          behavior: SnackBarBehavior.floating,
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF6A11CB),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      minimumSize: const Size(80, 32),
                    ),
                    child: Text(
                      'Follow',
                      style: GoogleFonts.poppins(
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      );
    } else {
      // Narrow format (vertical layout)
      return GestureDetector(
        onTap: () {
          // Navigate to public profile page
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const PublicProfilePage(),
            ),
          );
        },
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(10),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
        child: Column(
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              decoration: BoxDecoration(
                color: const Color(0xFF6A11CB).withAlpha(15),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Text(
                'Follow',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                  color: const Color(0xFF6A11CB),
                ),
                textAlign: TextAlign.center,
              ),
            ),

            // User info
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Avatar
                  CircleAvatar(
                    radius: 32,
                    backgroundColor: Colors.grey[200],
                    child: Text(
                      user['name'].substring(0, 1),
                      style: GoogleFonts.poppins(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF6A11CB),
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),

                  // User details
                  Text(
                    user['name'],
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    user['username'],
                    style: GoogleFonts.poppins(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${_formatNumber(user['followers'])} followers',
                    style: GoogleFonts.poppins(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 12),

                  // Follow button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        // Follow user logic
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'Following ${user['name']}',
                              style: GoogleFonts.poppins(),
                            ),
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF6A11CB),
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                      child: Text(
                        'Follow',
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.w600,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      );
    }
  }

  // Build asset content (just the image/preview part)
  Widget _buildAssetContent(Map<String, dynamic> asset) {
    return Container(
      height: 140,
      width: double.infinity,
      color: Colors.grey[200],
      child: Center(
        child: Icon(
          _getIconForType(asset['type']),
          size: 40,
          color: Colors.grey[400],
        ),
      ),
    );
  }

  // Build code content (just the code part)
  Widget _buildCodeContent(Map<String, dynamic> snippet, {bool isWide = false}) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[900],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                snippet['language'],
                style: GoogleFonts.poppins(
                  color: Colors.grey[400],
                  fontSize: 12,
                ),
              ),
              InkWell(
                onTap: () {
                  // Copy code logic
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'Code copied to clipboard',
                        style: GoogleFonts.poppins(),
                      ),
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                },
                child: const Icon(Icons.copy, color: Colors.white, size: 16),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            snippet['code'].toString().split('\n').take(isWide ? 10 : 6).join('\n'),
            style: GoogleFonts.firaCode(
              color: Colors.white,
              fontSize: isWide ? 14 : 12,
            ),
            maxLines: isWide ? 10 : 6,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }



  // Build asset card
  Widget _buildAssetCard(Map<String, dynamic> asset, {bool featured = false}) {
    // Use responsive width based on screen size
    final cardWidth = ResponsiveLayout.getValueForScreenType<double>(
      context: context,
      mobile: featured ? 220 : 180,
      tablet: featured ? 260 : 200,
      desktop: featured ? 300 : 220,
    );

    return OptimizedListItem(
      child: GestureDetector(
        onTap: () {
          // Navigate to asset detail page
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => AssetDetailPage(asset: asset),
            ),
          );
        },
        child: Container(
          width: cardWidth,
          margin: const EdgeInsets.only(right: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(10),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Asset image
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
              child: Stack(
                children: [
                  Hero(
                    tag: 'asset-image-${asset['title']}',
                    child: OptimizedAnimatedWidget(
                      child: Container(
                        height: 140,
                        width: double.infinity,
                        color: Colors.grey[200],
                        child: Center(
                          child: Icon(
                            _getIconForType(asset['type']),
                            size: 40,
                            color: Colors.grey[400],
                          ),
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.black.withAlpha(150),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.star,
                            color: Colors.amber,
                            size: 14,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            asset['rating'].toString(),
                            style: GoogleFonts.poppins(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

          // Asset info
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Asset type and platform
                Wrap(
                  spacing: 6,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: const Color(0xFF6A11CB).withAlpha(30),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        asset['type'],
                        style: GoogleFonts.poppins(
                          color: const Color(0xFF6A11CB),
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.blue.withAlpha(30),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        asset['platform'],
                        style: GoogleFonts.poppins(
                          color: Colors.blue[700],
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                // Asset title
                Text(
                  asset['title'],
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),

                // Asset author
                Text(
                  'by ${asset['author']}',
                  style: GoogleFonts.poppins(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 8),

                // Price and downloads
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      asset['price'] > 0 ? '\$${asset['price']}' : 'Free',
                      style: GoogleFonts.poppins(
                        color: asset['price'] > 0 ? Colors.black87 : Colors.green[600],
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                    Row(
                      children: [
                        Icon(
                          Icons.download,
                          size: 14,
                          color: Colors.grey[600],
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _formatNumber(asset['downloads']),
                          style: GoogleFonts.poppins(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
      ),
    ));
  }



  // Build user card
  Widget _buildUserCard(Map<String, dynamic> user) {
    // Use responsive width based on screen size
    final cardWidth = ResponsiveLayout.getValueForScreenType<double>(
      context: context,
      mobile: 160,
      tablet: 180,
      desktop: 200,
    );

    return Container(
      child: GestureDetector(
        onTap: () {
          // Navigate to public profile page
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const PublicProfilePage(),
            ),
          );
        },
        child: Container(
          width: cardWidth,
          height: 220,
          margin: const EdgeInsets.only(right: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(10),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
        child: SingleChildScrollView(
          physics: const NeverScrollableScrollPhysics(),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 12),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
              // User avatar
              Stack(
                alignment: Alignment.center,
                children: [
                  Hero(
                    tag: 'user-avatar-${user['username']}',
                    child: CircleAvatar(
                      radius: 35,
                      backgroundColor: Colors.grey[200],
                      child: Text(
                        user['name'].substring(0, 1),
                        style: GoogleFonts.poppins(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF6A11CB),
                        ),
                      ),
                    ),
                  ),
                  if (user['isVerified'])
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 2),
                        ),
                        child: Icon(
                          Icons.verified,
                          size: 16,
                          color: user['isElite'] ? Colors.amber : Colors.blue,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 8),

              // User name
              Text(
                user['name'],
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 2),

              // Username
              Text(
                user['username'],
                style: GoogleFonts.poppins(
                  color: Colors.grey[600],
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),

              // Followers
              Text(
                '${_formatNumber(user['followers'])} followers',
                style: GoogleFonts.poppins(
                  color: Colors.grey[600],
                  fontSize: 11,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),

              // Follow button
              SizedBox(
                height: 32,
                child: ElevatedButton(
                  onPressed: () {
                    // Follow user logic
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF6A11CB),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
                    minimumSize: const Size(80, 32),
                  ),
                  child: Text(
                    'Follow',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      ),
    ));
  }

  // Build search results
  Widget _buildSearchResults() {
    // We've moved search suggestions to the main content area
    // This method now only handles actual search results

    if (_searchQuery.isEmpty) {
      return const SliverToBoxAdapter(
        child: Center(
          child: Padding(
            padding: EdgeInsets.all(32),
            child: Text('Type to search...'),
          ),
        ),
      );
    }

    // Store the search query to prevent rebuilding during scrolling
    final String currentQuery = _searchQuery;

    // Use async search function with Firebase
    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _performSearch(currentQuery),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const SliverToBoxAdapter(
            child: Center(
              child: Padding(
                padding: EdgeInsets.all(32),
                child: CircularProgressIndicator(),
              ),
            ),
          );
        }

        if (snapshot.hasError) {
          return SliverToBoxAdapter(
            child: Center(
              child: Padding(
                padding: const EdgeInsets.all(32),
                child: Text('Error: ${snapshot.error}'),
              ),
            ),
          );
        }

        final searchResults = snapshot.data ?? [];

        // Separate results by type
        final filteredAssets = searchResults.where((item) =>
          item.containsKey('type') && item.containsKey('price')).toList();

        // Filter code snippets
        final filteredSnippets = searchResults.where((item) =>
          item.containsKey('code') && item.containsKey('language')).toList();

        // Filter users based on search query
        final filteredUsers = _recommendedUsers.where((user) {
          final name = user['name'].toString().toLowerCase();
          final username = user['username'].toString().toLowerCase();
          final query = _searchQuery.toLowerCase();
          return name.contains(query) || username.contains(query);
        }).toList();

        if (filteredAssets.isEmpty && filteredUsers.isEmpty && filteredSnippets.isEmpty) {
          return const SliverToBoxAdapter(
            child: Center(
              child: Padding(
                padding: EdgeInsets.all(32),
                child: Text('No results found'),
              ),
            ),
          );
        }

        // When we have results, track the interaction with the first result
        if (filteredAssets.isNotEmpty && filteredAssets[0].containsKey('id')) {
          _trackSearchInteraction(_searchQuery, filteredAssets[0]['id'].toString());
        }

        return SliverList(
          delegate: SliverChildListDelegate([
            // Assets section
        if (filteredAssets.isNotEmpty) ...[
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: Text(
              'Assets',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: StaggeredGrid.count(
              crossAxisCount: 4,
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              children: filteredAssets.map((asset) {
                // Alternate between wide and narrow items
                final bool isWide = filteredAssets.indexOf(asset) % 3 == 0;
                final int crossAxisCellCount = isWide ? 4 : 2;

                return StaggeredGridTile.fit(
                  crossAxisCellCount: crossAxisCellCount,
                  child: GestureDetector(
                    onTap: () {
                      // Track interaction when user clicks on an asset
                      if (asset.containsKey('id')) {
                        _trackSearchInteraction(_searchQuery, asset['id'].toString());
                      }
                      // Navigate to asset details
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => AssetDetailPage(asset: asset),
                        ),
                      );
                    },
                    child: _buildAssetCard(asset, featured: isWide),
                  ),
                );
              }).toList(),
            ),
          ),
        ],

        // Code snippets section
        if (filteredSnippets.isNotEmpty) ...[
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
            child: Text(
              'Code Snippets',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: StaggeredGrid.count(
              crossAxisCount: 4,
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              children: filteredSnippets.map((snippet) {
                // Add content type for proper rendering
                snippet['contentType'] = 'code';
                // Alternate between wide and narrow items
                final bool isWide = filteredSnippets.indexOf(snippet) % 3 == 0;
                snippet['isWide'] = isWide;

                return StaggeredGridTile.fit(
                  crossAxisCellCount: isWide ? 4 : 2,
                  child: _buildContentItem(snippet),
                );
              }).toList(),
            ),
          ),
        ],

        // Users section
        if (filteredUsers.isNotEmpty) ...[
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
            child: Text(
              'Users',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ),
          SizedBox(
            height: 220,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: filteredUsers.length,
              itemBuilder: (context, index) {
                final user = filteredUsers[index];
                return _buildUserCard(user);
              },
            ),
          ),
        ],
      ]),
    );
      },
    );
  }

  // Show filter options
  void _showFilterOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // Allow the sheet to be larger than half the screen
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        // Get the bottom padding to avoid the keyboard
        final bottomPadding = MediaQuery.of(context).viewInsets.bottom;

        return Padding(
          // Add padding to avoid the keyboard
          padding: EdgeInsets.only(bottom: bottomPadding),
          child: SingleChildScrollView( // Make the content scrollable to avoid overflow
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Filter Results',
                        style: GoogleFonts.poppins(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Categories',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: _categories.map((category) {
                      return _buildFilterChip(
                        category,
                        _selectedCategory == category,
                        isCategory: true
                      );
                    }).toList(),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Price Range',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: _priceRanges.map((priceRange) {
                      return _buildFilterChip(
                        priceRange,
                        _selectedPriceRange == priceRange,
                        isCategory: false
                      );
                    }).toList(),
                  ),
                  const SizedBox(height: 24),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        // Apply the filters to the content
                        _applyFilters();
                        Navigator.pop(context);

                        // Show a snackbar to confirm filters were applied
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'Filters applied: $_selectedCategory • $_selectedPriceRange',
                              style: GoogleFonts.poppins(),
                            ),
                            behavior: SnackBarBehavior.floating,
                            backgroundColor: const Color(0xFF6A11CB),
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF6A11CB),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        'Apply Filters',
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                          color: Colors.white, // Explicitly set text color to white
                        ),
                      ),
                    ),
                  ),
                  // Add extra padding at the bottom to ensure content is visible
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Build filter chip
  Widget _buildFilterChip(String label, bool isSelected, {bool isCategory = true}) {
    return FilterChip(
      label: Text(
        label,
        style: GoogleFonts.poppins(
          color: isSelected ? Colors.white : Colors.black87,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
          fontSize: 14,
        ),
      ),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          if (isCategory) {
            _selectedCategory = selected ? label : 'All';
          } else {
            _selectedPriceRange = selected ? label : 'All';
          }
        });
      },
      backgroundColor: Colors.grey[100],
      selectedColor: const Color(0xFF6A11CB),
      checkmarkColor: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
        side: BorderSide(
          color: isSelected ? Colors.transparent : Colors.grey[300]!,
          width: 1,
        ),
      ),
    );
  }



  // Helper method to get icon for asset type
  IconData _getIconForType(String type) {
    switch (type) {
      case 'UI Kits':
        return Icons.palette;
      case 'Templates':
        return Icons.web;
      case 'Blocks':
        return Icons.view_module;
      case 'Libraries':
        return Icons.code;
      case 'Icons':
        return Icons.image;
      case 'Code Snippets':
        return Icons.code;
      default:
        return Icons.extension;
    }
  }



  // Helper method to format numbers
  String _formatNumber(int number) {
    if (number >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}k';
    }
    return number.toString();
  }

  // Show feedback form
  void _showFeedbackForm() {
    final TextEditingController feedbackController = TextEditingController();
    String selectedCategory = 'General';
    int selectedRating = 0; // Start with no stars selected
    final List<String> categories = ['General', 'UI/UX', 'Performance', 'Feature Request', 'Bug Report'];

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Container(
            padding: const EdgeInsets.all(20),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Simple header with just text and icon - using responsive layout service
                  Padding(
                    padding: context.responsivePadding(
                      padding: const EdgeInsets.only(bottom: 24),
                      smallScreenPadding: const EdgeInsets.only(bottom: 16),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Row(
                            children: [
                              const Icon(
                                Icons.feedback_outlined,
                                color: Color(0xFF6A11CB),
                                size: 24,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Send Feedback',
                                style: GoogleFonts.poppins(
                                  fontSize: context.responsiveFontSize(
                                    size: 22,
                                    smallScreenSize: 18,
                                  ),
                                  fontWeight: FontWeight.bold,
                                  color: const Color(0xFF6A11CB),
                                ),
                                overflow: TextOverflow.ellipsis,
                              ).safe(),
                            ],
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close, color: Colors.grey),
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                          onPressed: () => Navigator.pop(context),
                        ),
                      ],
                    ),
                  ),

                  // Category dropdown with improved styling - using responsive layout service
                  Text(
                    'Category:',
                    style: GoogleFonts.poppins(
                      fontSize: context.responsiveFontSize(
                        size: 16,
                        smallScreenSize: 14,
                      ),
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  SizedBox(height: context.isSmallScreen ? 6 : 8),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(12),
                      color: Colors.grey[50],
                    ),
                    padding: context.responsivePadding(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                      smallScreenPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        value: selectedCategory,
                        isExpanded: true,
                        icon: Icon(
                          Icons.arrow_drop_down,
                          color: const Color(0xFF6A11CB),
                          size: context.responsiveValue(
                            extraSmall: 24.0,
                            small: 28.0,
                            medium: 30.0,
                          ),
                        ),
                        elevation: 8,
                        style: GoogleFonts.poppins(
                          fontSize: context.responsiveFontSize(
                            size: 16,
                            smallScreenSize: 14,
                          ),
                          color: Colors.black87,
                          fontWeight: FontWeight.w500,
                        ),
                        dropdownColor: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        items: categories.map((String category) {
                          return DropdownMenuItem<String>(
                            value: category,
                            child: Padding(
                              padding: context.responsivePadding(
                                padding: const EdgeInsets.symmetric(vertical: 8.0),
                                smallScreenPadding: const EdgeInsets.symmetric(vertical: 6.0),
                              ),
                              child: Text(
                                category,
                                style: GoogleFonts.poppins(
                                  fontSize: context.responsiveFontSize(
                                    size: 16,
                                    smallScreenSize: 14,
                                  ),
                                  fontWeight: category == selectedCategory ? FontWeight.w600 : FontWeight.normal,
                                ),
                              ),
                            ),
                          );
                        }).toList(),
                        onChanged: (String? newValue) {
                          if (newValue != null) {
                            setState(() {
                              selectedCategory = newValue;
                            });
                          }
                        },
                      ),
                    ),
                  ),

                  const SizedBox(height: 20),

                  // Rating
                  Text(
                    'Rating:',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Center(
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: List.generate(5, (index) {
                        return GestureDetector(
                          onTap: () {
                            setState(() {
                              // Toggle rating if tapping the same star
                              if (selectedRating == index + 1) {
                                selectedRating = 0;
                              } else {
                                selectedRating = index + 1;
                              }
                            });
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 4),
                            child: Icon(
                              index < selectedRating ? Icons.star : Icons.star_border,
                              color: index < selectedRating ? Colors.amber : Colors.grey,
                              size: 32,
                            ),
                          ),
                        );
                      }),
                    ),
                  ),

                  const SizedBox(height: 20),

                  // Feedback text field
                  Text(
                    'Your Feedback:',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: feedbackController,
                    maxLines: 5,
                    decoration: InputDecoration(
                      hintText: 'Enter your feedback here...', 
                      hintStyle: GoogleFonts.poppins(
                        color: Colors.grey[400],
                      ),
                      filled: true,
                      fillColor: Colors.grey[50],
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide(color: Colors.grey[300]!),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: const BorderSide(
                          color: Color(0xFF6A11CB),
                          width: 2,
                        ),
                      ),
                      contentPadding: const EdgeInsets.all(16),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Submit button
                  SizedBox(
                    width: double.infinity,
                    height: 50,
                    child: ElevatedButton(
                      onPressed: () async {
                        final message = feedbackController.text.trim();
                        if (message.isNotEmpty) {
                          try {
                            await _feedbackService.submitFeedback(
                              message: message,
                              category: selectedCategory,
                              rating: selectedRating > 0 ? selectedRating : null, // Only send rating if selected
                            );

                            if (context.mounted) {
                              Navigator.pop(context);
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'Thank you for your feedback!', 
                                    style: GoogleFonts.poppins(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  backgroundColor: Colors.green,
                                  behavior: SnackBarBehavior.floating,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                ),
                              );
                            }
                          } catch (e) {
                            if (context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(
                                    'Error submitting feedback: \$e', 
                                    style: GoogleFonts.poppins(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  backgroundColor: Colors.red,
                                  behavior: SnackBarBehavior.floating,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                ),
                              );
                            }
                          }
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'Please enter your feedback', 
                                style: GoogleFonts.poppins(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              behavior: SnackBarBehavior.floating,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          );
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF6A11CB),
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 2,
                      ),
                      child: Text(
                        'Submit Feedback',
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Show code dialog for viewing full code
  void _showCodeDialog(Map<String, dynamic> item) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.8,
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            item['title'] ?? 'Code Snippet',
                            style: GoogleFonts.poppins(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'by ${item['author']} • ${item['language']}',
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.copy),
                      onPressed: () {
                        // Copy code logic
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(
                              'Code copied to clipboard',
                              style: GoogleFonts.poppins(),
                            ),
                            behavior: SnackBarBehavior.floating,
                          ),
                        );
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Code content
                Container(
                  constraints: BoxConstraints(
                    maxHeight: MediaQuery.of(context).size.height * 0.5,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.grey[900],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Text(
                      item['code'].toString(),
                      style: GoogleFonts.firaCode(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Like and comment counts
                    Row(
                      children: [
                        Icon(
                          Icons.favorite,
                          color: item['liked'] == true ? Colors.red : Colors.grey[400],
                          size: 20,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _formatNumber(item['likes'] ?? 0),
                          style: GoogleFonts.poppins(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Icon(
                          Icons.comment,
                          color: Colors.grey[400],
                          size: 20,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _formatNumber(item['comments'] ?? 0),
                          style: GoogleFonts.poppins(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),

                    // Close button
                    TextButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: const Color(0xFF6A11CB),
                      ),
                      child: Text(
                        'Close',
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Show comment dialog for code snippets
  void _showCommentDialog(Map<String, dynamic> item) {
    final TextEditingController commentController = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Text(
                  'Add Comment',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Commenting on: ${item['title']}',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 16),

                // Comment input
                TextField(
                  controller: commentController,
                  decoration: InputDecoration(
                    hintText: 'Write your comment...', 
                    hintStyle: GoogleFonts.poppins(
                      color: Colors.grey[400],
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: const BorderSide(color: Color(0xFF6A11CB)),
                    ),
                    contentPadding: const EdgeInsets.all(16),
                  ),
                  maxLines: 3,
                  style: GoogleFonts.poppins(),
                ),
                const SizedBox(height: 20),

                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // Cancel button
                    TextButton(
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.grey[700],
                      ),
                      child: Text(
                        'Cancel',
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),

                    // Post button
                    ElevatedButton(
                      onPressed: () {
                        if (commentController.text.trim().isNotEmpty) {
                          // Add comment logic
                          setState(() {
                            final Map<String, dynamic> updatedItem = Map<String, dynamic>.from(item);
                            updatedItem['comments'] = (item['comments'] ?? 0) + 1;

                            // Find and update the item in the mixed content list
                            final index = _mixedContent.indexWhere((element) =>
                              element['contentType'] == 'code' &&
                              element['title'] == item['title'] &&
                              element['author'] == item['author']);

                            if (index != -1) {
                              _mixedContent[index] = updatedItem;
                            }
                          });

                          Navigator.pop(context);

                          // Show feedback
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(
                                'Comment posted on ${item['title']}', 
                                style: GoogleFonts.poppins(),
                              ),
                              behavior: SnackBarBehavior.floating,
                            ),
                          );
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF6A11CB),
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      ),
                      child: Text(
                        'Post Comment',
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // App Bar with Search
          _buildAppBar(),

          // Main Content
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 16),

                  // Search suggestions if available
                  if (_showSuggestions && _searchSuggestions.isNotEmpty)
                    Container(
                      width: double.infinity,
                      margin: const EdgeInsets.only(bottom: 16),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(10),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.auto_awesome, size: 16, color: Colors.grey[600]),
                              const SizedBox(width: 8),
                              Text(
                                'Suggested Keywords',
                                style: GoogleFonts.poppins(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.grey[700],
                                ),
                              ),
                            ],
                          ),
                          const Divider(),
                          Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: _searchSuggestions.map((suggestion) {
                              return InkWell(
                                onTap: () {
                                  setState(() {
                                    _searchController.text = suggestion;
                                    _searchQuery = suggestion;
                                    _showSuggestions = false;
                                  });
                                },
                                child: Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                  decoration: BoxDecoration(
                                    color: Colors.grey[100],
                                    borderRadius: BorderRadius.circular(20),
                                    border: Border.all(color: Colors.grey[300]!),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(Icons.search, size: 14, color: Colors.grey[500]),
                                      const SizedBox(width: 6),
                                      Text(
                                        suggestion,
                                        style: GoogleFonts.poppins(
                                          fontSize: 13,
                                          color: Colors.black87,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            }).toList(),
                          ),
                        ],
                      ),
                    ),

                  // Quick action buttons
                  if (!_isSearching || _searchQuery.isEmpty)
                    _buildQuickActions(),

                  const SizedBox(height: 24),

                  // Trending assets section
                  if (!_isSearching && _searchQuery.isEmpty)
                    _buildTrendingSection(),

                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),

          // Search results or main content
          _isSearching ? _buildSearchResults() : _buildMainContent(),

          // Loading indicator for infinite scroll
          if (_isLoadingMore)
            const SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Center(
                  child: CircularProgressIndicator(),
                ),
              ),
            ),

          // Bottom padding
          const SliverToBoxAdapter(
            child: SizedBox(height: 24),
          ),
        ],
      ),
    );
  }
}