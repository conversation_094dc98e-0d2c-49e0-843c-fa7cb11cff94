import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/scheduler.dart';
import 'package:cached_network_image/cached_network_image.dart';

/// A utility class containing methods and widgets to optimize app performance
class PerformanceOptimizations {
  /// Enables performance monitoring in debug mode
  static void enablePerformanceMonitoring(BuildContext context) {
    if (kDebugMode) {
      debugPrintRebuildDirtyWidgets = true;
      debugProfileBuildsEnabled = true;
    }
  }

  /// Disables performance monitoring
  static void disablePerformanceMonitoring() {
    debugPrintRebuildDirtyWidgets = false;
    debugProfileBuildsEnabled = false;
  }

  /// Enables rendering profiling in debug mode
  static void enableRenderingProfiling() {
    if (kDebugMode) {
      debugProfileBuildsEnabled = true;
      debugProfilePaintsEnabled = true;
    }
  }

  /// Disables rendering profiling
  static void disableRenderingProfiling() {
    debugProfileBuildsEnabled = false;
    debugProfilePaintsEnabled = false;
  }

  /// Optimizes the app for better performance
  static void optimizeApp() {
    // Disable debug flags in release mode
    if (!kDebugMode) {
      debugPrintRebuildDirtyWidgets = false;
    }
  }

  /// Reduces memory usage by clearing image cache
  static void clearImageCache() {
    PaintingBinding.instance.imageCache.clear();
    PaintingBinding.instance.imageCache.clearLiveImages();
  }

  /// Schedules a task for the next frame to avoid jank
  static void scheduleTask(VoidCallback task) {
    SchedulerBinding.instance.scheduleFrameCallback((_) {
      task();
    });
  }
}

/// A widget that prevents unnecessary rebuilds by memoizing its child
class MemoizedWidget extends StatefulWidget {
  final Widget child;
  final Object? dependencies;

  const MemoizedWidget({
    Key? key,
    required this.child,
    this.dependencies,
  }) : super(key: key);

  @override
  State<MemoizedWidget> createState() => _MemoizedWidgetState();
}

class _MemoizedWidgetState extends State<MemoizedWidget> {
  late Widget _child;
  Object? _dependencies;

  @override
  void initState() {
    super.initState();
    _child = widget.child;
    _dependencies = widget.dependencies;
  }

  @override
  void didUpdateWidget(MemoizedWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.dependencies != _dependencies) {
      _child = widget.child;
      _dependencies = widget.dependencies;
    }
  }

  @override
  Widget build(BuildContext context) {
    return _child;
  }
}

/// A widget that optimizes image loading and caching
class OptimizedImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final bool useMemoryCache;
  final Duration fadeInDuration;

  const OptimizedImage({
    Key? key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.useMemoryCache = true,
    this.fadeInDuration = const Duration(milliseconds: 300),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Calculate optimal cache dimensions based on device pixel ratio
    final devicePixelRatio = MediaQuery.of(context).devicePixelRatio;
    final cacheWidth = width != null ? (width! * devicePixelRatio).round() : null;
    final cacheHeight = height != null ? (height! * devicePixelRatio).round() : null;

    // Use cached_network_image for network images
    if (imageUrl.startsWith('http')) {
      return CachedNetworkImage(
        imageUrl: imageUrl,
        width: width,
        height: height,
        fit: fit,
        memCacheWidth: cacheWidth,
        memCacheHeight: cacheHeight,
        useOldImageOnUrlChange: true,
        fadeInDuration: fadeInDuration,
        placeholder: (context, url) => placeholder ?? Center(
          child: SizedBox(
            width: 24,
            height: 24,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).primaryColor),
            ),
          ),
        ),
        errorWidget: (context, url, error) => errorWidget ?? const Icon(Icons.error),
      );
    }

    // For local assets - use RepaintBoundary for better performance
    return RepaintBoundary(
      child: Image.asset(
        imageUrl,
        width: width,
        height: height,
        fit: fit,
        cacheWidth: cacheWidth,
        cacheHeight: cacheHeight,
        errorBuilder: (context, error, stackTrace) {
          return errorWidget ?? const Icon(Icons.error);
        },
      ),
    );
  }
}

/// A widget that optimizes list rendering by using RepaintBoundary
class OptimizedListItem extends StatelessWidget {
  final Widget child;
  final bool useRepaintBoundary;

  const OptimizedListItem({
    Key? key,
    required this.child,
    this.useRepaintBoundary = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return useRepaintBoundary
        ? RepaintBoundary(child: child)
        : child;
  }
}

/// A widget that optimizes animations by using RepaintBoundary
class OptimizedAnimatedWidget extends StatelessWidget {
  final Widget child;

  const OptimizedAnimatedWidget({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(child: child);
  }
}

/// A widget that optimizes scrolling by using KeepAlive
class OptimizedScrollItem extends StatefulWidget {
  final Widget child;
  final bool keepAlive;

  const OptimizedScrollItem({
    Key? key,
    required this.child,
    this.keepAlive = true,
  }) : super(key: key);

  @override
  State<OptimizedScrollItem> createState() => _OptimizedScrollItemState();
}

class _OptimizedScrollItemState extends State<OptimizedScrollItem>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => widget.keepAlive;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }
}
