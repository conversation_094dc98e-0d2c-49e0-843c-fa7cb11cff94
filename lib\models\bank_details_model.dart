import 'package:cloud_firestore/cloud_firestore.dart';

/// Bank details model class to handle user bank accounts
class BankDetailsModel {
  final String id;
  final String userId;
  final String accountHolderName;
  final String bankName;
  final String accountNumber;
  final String ifscCode;
  final String accountType; // 'checking', 'savings', etc.
  final String? swiftCode;
  final String? iban;
  final String? branchName;
  final String? branchCode;
  final String? additionalInfo;
  final bool isDefault;
  final bool isVerified;
  final DateTime createdAt;
  final DateTime updatedAt;

  BankDetailsModel({
    required this.id,
    required this.userId,
    required this.accountHolderName,
    required this.bankName,
    required this.accountNumber,
    required this.ifscCode,
    required this.accountType,
    this.swiftCode,
    this.iban,
    this.branchName,
    this.branchCode,
    this.additionalInfo,
    required this.isDefault,
    required this.isVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) :
    createdAt = createdAt ?? DateTime.now(),
    updatedAt = updatedAt ?? DateTime.now();

  // Create a BankDetailsModel from a Firestore document
  factory BankDetailsModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>? ?? {};

    final Timestamp createdAtTimestamp = data['createdAt'] as Timestamp? ?? Timestamp.now();
    final Timestamp updatedAtTimestamp = data['updatedAt'] as Timestamp? ?? Timestamp.now();

    return BankDetailsModel(
      id: doc.id,
      userId: data['userId'] as String? ?? '',
      accountHolderName: data['accountHolderName'] as String? ?? '',
      bankName: data['bankName'] as String? ?? '',
      accountNumber: data['accountNumber'] as String? ?? '',
      ifscCode: data['routingNumber'] as String? ?? '', // Still using routingNumber field in Firestore for backward compatibility
      accountType: data['accountType'] as String? ?? 'checking',
      swiftCode: data['swiftCode'] as String?,
      iban: data['iban'] as String?,
      branchName: data['branchName'] as String?,
      branchCode: data['branchCode'] as String?,
      additionalInfo: data['additionalInfo'] as String?,
      isDefault: data['isDefault'] as bool? ?? false,
      isVerified: data['isVerified'] as bool? ?? false,
      createdAt: createdAtTimestamp.toDate(),
      updatedAt: updatedAtTimestamp.toDate(),
    );
  }

  // Convert BankDetailsModel to a Map for Firestore
  Map<String, dynamic> toMap() {
    final Map<String, dynamic> data = {
      'userId': userId,
      'accountHolderName': accountHolderName,
      'bankName': bankName,
      'accountNumber': accountNumber,
      'routingNumber': ifscCode, // Still using routingNumber field in Firestore for backward compatibility
      'accountType': accountType,
      'isDefault': isDefault,
      'isVerified': isVerified,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(DateTime.now()),
    };

    if (swiftCode != null) data['swiftCode'] = swiftCode;
    if (iban != null) data['iban'] = iban;
    if (branchName != null) data['branchName'] = branchName;
    if (branchCode != null) data['branchCode'] = branchCode;
    if (additionalInfo != null) data['additionalInfo'] = additionalInfo;

    return data;
  }

  // Create a copy of this BankDetailsModel with updated fields
  BankDetailsModel copyWith({
    String? id,
    String? userId,
    String? accountHolderName,
    String? bankName,
    String? accountNumber,
    String? ifscCode,
    String? accountType,
    String? swiftCode,
    String? iban,
    String? branchName,
    String? branchCode,
    String? additionalInfo,
    bool? isDefault,
    bool? isVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BankDetailsModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      accountHolderName: accountHolderName ?? this.accountHolderName,
      bankName: bankName ?? this.bankName,
      accountNumber: accountNumber ?? this.accountNumber,
      ifscCode: ifscCode ?? this.ifscCode,
      accountType: accountType ?? this.accountType,
      swiftCode: swiftCode ?? this.swiftCode,
      iban: iban ?? this.iban,
      branchName: branchName ?? this.branchName,
      branchCode: branchCode ?? this.branchCode,
      additionalInfo: additionalInfo ?? this.additionalInfo,
      isDefault: isDefault ?? this.isDefault,
      isVerified: isVerified ?? this.isVerified,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
