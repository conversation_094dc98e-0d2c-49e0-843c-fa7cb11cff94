import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class TermsPolicyPage extends StatefulWidget {
  final bool isFromAuth;
  final Function(bool)? onAccept;

  const TermsPolicyPage({
    Key? key,
    this.isFromAuth = false,
    this.onAccept,
  }) : super(key: key);

  @override
  State<TermsPolicyPage> createState() => _TermsPolicyPageState();
}

class _TermsPolicyPageState extends State<TermsPolicyPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _hasAccepted = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Terms & Policies',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: widget.isFromAuth
            ? IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.black87),
                onPressed: () => Navigator.pop(context, false),
              )
            : IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.black87),
                onPressed: () => Navigator.pop(context),
              ),
        bottom: TabBar(
          controller: _tabController,
          labelColor: const Color(0xFF6A11CB),
          unselectedLabelColor: Colors.grey,
          indicatorColor: const Color(0xFF6A11CB),
          tabs: [
            Tab(
              text: 'Terms of Service',
              icon: Icon(
                Icons.description_outlined,
                color: _tabController.index == 0
                    ? const Color(0xFF6A11CB)
                    : Colors.grey,
              ),
            ),
            Tab(
              text: 'Privacy Policy',
              icon: Icon(
                Icons.privacy_tip_outlined,
                color: _tabController.index == 1
                    ? const Color(0xFF6A11CB)
                    : Colors.grey,
              ),
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildTermsOfService(),
          _buildPrivacyPolicy(),
        ],
      ),
      bottomNavigationBar: widget.isFromAuth
          ? Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, -5),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Checkbox(
                    value: _hasAccepted,
                    onChanged: (value) {
                      setState(() {
                        _hasAccepted = value ?? false;
                      });
                    },
                    activeColor: const Color(0xFF6A11CB),
                  ),
                  Expanded(
                    child: Text(
                      'I have read and agree to the Terms of Service and Privacy Policy',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: _hasAccepted
                        ? () {
                            if (widget.onAccept != null) {
                              widget.onAccept!(true);
                            }
                            Navigator.pop(context, true);
                          }
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF6A11CB),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      'Accept',
                      style: GoogleFonts.poppins(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            )
          : null,
    );
  }

  Widget _buildTermsOfService() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Terms of Service',
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Last Updated: ${DateTime.now().toString().substring(0, 10)}',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          _buildTermsSection(
            title: '1. Introduction',
            content:
                'Welcome to Asatu, a developer marketplace and community platform. By using our services, you agree to these Terms of Service. Please read them carefully.\n\n'
                'These Terms of Service ("Terms") govern your access to and use of the Asatu platform, including any content, functionality, and services offered on or through the platform. By registering with us or by using our platform, you accept and agree to be bound by these Terms. If you do not agree to these Terms, you must not access or use our platform.',
          ),
          _buildTermsSection(
            title: '2. Developer Marketplace',
            content:
                'Asatu provides a marketplace for developers to buy, sell, and share digital assets, code, and services. Users can upload assets for sale or download, with the following conditions:\n\n'
                '2.1 Revenue Sharing\n'
                '• Sellers receive 80% of the revenue from their asset sales\n'
                '• Asatu retains 20% as a platform fee\n'
                '• This revenue split applies to all digital assets, including but not limited to: UI kits, templates, code snippets, plugins, and complete applications\n'
                '• Payment processing fees may be deducted from the seller\'s share\n\n'

                '2.2 Transaction Processing\n'
                '• All transactions must be processed through the platform\n'
                '• Attempting to bypass platform fees is prohibited\n'
                '• Direct payment arrangements between buyers and sellers outside the platform are strictly forbidden\n'
                '• Violations may result in account suspension or termination\n\n'

                '2.3 Asset Quality Requirements\n'
                '• All assets must be original or properly licensed\n'
                '• Assets must be fully functional and as described\n'
                '• Assets must not contain malicious code or security vulnerabilities\n'
                '• Asatu reserves the right to remove assets that do not meet quality standards\n\n'

                '2.4 Refund Policy\n'
                '• Buyers may request refunds within 14 days if assets do not function as described\n'
                '• Refund requests will be reviewed by Asatu\'s support team\n'
                '• Approved refunds will be processed within 7 business days',
          ),
          _buildTermsSection(
            title: '3. Community Guidelines',
            content:
                'Our community is built on respect and collaboration. Users must:\n\n'
                '3.1 Conduct\n'
                '• Treat others with respect and professionalism\n'
                '• Engage in constructive discussions and feedback\n'
                '• Respect intellectual property rights\n'
                '• Report violations of these guidelines\n\n'

                '3.2 Prohibited Content\n'
                '• Offensive, harmful, or illegal content\n'
                '• Content that promotes discrimination, hatred, or violence\n'
                '• Sexually explicit or pornographic material\n'
                '• Content that infringes on intellectual property rights\n'
                '• Malware, viruses, or other harmful code\n\n'

                '3.3 Account Integrity\n'
                '• Users must not impersonate others or misrepresent themselves\n'
                '• Multiple accounts for the purpose of abuse or manipulation are prohibited\n'
                '• Account sharing is not permitted\n'
                '• Users are responsible for maintaining the security of their accounts\n\n'

                '3.4 Communication\n'
                '• Spam, excessive self-promotion, and disruptive behavior are prohibited\n'
                '• Harassment, threats, and intimidation are not tolerated\n'
                '• Private information should not be shared without consent',
          ),
          _buildTermsSection(
            title: '4. User Content',
            content:
                '4.1 Ownership and License\n'
                '• Users retain ownership of their content\n'
                '• By uploading content, users grant Asatu a worldwide, non-exclusive, royalty-free license to use, reproduce, modify, adapt, publish, translate, distribute, and display such content on the platform\n'
                '• This license is solely for the purpose of operating and improving the platform\n\n'

                '4.2 Responsibility\n'
                '• Users are responsible for ensuring they have the right to share any content they upload\n'
                '• Users must not upload content that infringes on third-party rights\n'
                '• Asatu is not responsible for the content posted by users\n\n'

                '4.3 Content Removal\n'
                '• Asatu reserves the right to remove any content that violates these Terms\n'
                '• Content may be removed upon receipt of a valid copyright infringement notice\n'
                '• Users may appeal content removal decisions',
          ),
          _buildTermsSection(
            title: '5. Payments and Wallet',
            content:
                'Asatu provides a wallet system for transactions. Users can:\n\n'
                '5.1 Wallet Functionality\n'
                '• Add funds via credit/debit cards, PayPal, PhonePe, Paytm, QR code, Google Pay, and Apple Pay\n'
                '• Store balance for future purchases\n'
                '• Track transaction history\n'
                '• Receive earnings from sales\n\n'

                '5.2 Adding Funds\n'
                '• Minimum deposit amount: \$5 or equivalent in local currency\n'
                '• Payment processing fees may apply depending on the payment method\n'
                '• Funds are typically available immediately, but may be subject to verification\n\n'

                '5.3 Withdrawals\n'
                '• Minimum withdrawal amount: \$20 or equivalent in local currency\n'
                '• Withdrawals are subject to identity verification\n'
                '• Processing time: 3-7 business days\n'
                '• Withdrawal fees may apply depending on the withdrawal method\n\n'

                '5.4 Security\n'
                '• All financial transactions are securely processed through our payment partners\n'
                '• Wallet balances are not insured by government agencies\n'
                '• Users should report unauthorized transactions immediately',
          ),
          _buildTermsSection(
            title: '6. Monetization Features',
            content:
                '6.1 Asset Sales\n'
                '• Users can sell digital assets with the 70/30 revenue split\n'
                '• Pricing is set by the seller within platform guidelines\n'
                '• Assets can be offered as free, one-time purchase, or subscription\n\n'

                '6.2 Contests\n'
                '• Users can create contests with cash prizes\n'
                '• Contest creators set requirements and judging criteria\n'
                '• Minimum prize amount: \$50\n'
                '• Platform fee: 30% of the prize amount\n'
                '• Contest entries become the property of the contest creator unless otherwise specified\n\n'

                '6.3 Hiring\n'
                '• Users can hire other users for custom projects\n'
                '• Service providers receive 70% of the payment\n'
                '• Platform fee: 30% of the payment\n'
                '• Milestone-based payments are supported\n'
                '• Escrow services are provided for project protection\n\n'

                '6.4 Subscriptions\n'
                '• Creators can offer subscription-based content\n'
                '• Subscribers gain access to exclusive content\n'
                '• Subscription fees follow the 70/30 revenue split\n'
                '• Cancellation policy: Subscribers can cancel at any time, effective at the end of the current billing period',
          ),
          _buildTermsSection(
            title: '7. Contests and Hiring',
            content:
                '7.1 Contest Rules\n'
                '• Contest creators must provide clear requirements and fair judging criteria\n'
                '• Contest deadlines must be reasonable\n'
                '• Contest prizes must be paid in full before the contest begins\n'
                '• Contest entries must be original work\n\n'

                '7.2 Hiring Process\n'
                '• Project requirements must be clearly defined\n'
                '• Payment terms must be agreed upon before work begins\n'
                '• Disputes will be mediated by Asatu\'s support team\n'
                '• Both parties must act in good faith\n\n'

                '7.3 Intellectual Property\n'
                '• Unless otherwise specified, hired work becomes the property of the client upon full payment\n'
                '• Contest entries become the property of the contest creator upon selection as winner\n'
                '• Non-winning contest entries remain the property of their creators',
          ),
          _buildTermsSection(
            title: '8. Termination',
            content:
                '8.1 Account Termination by Asatu\n'
                '• Asatu reserves the right to terminate or suspend accounts that violate these terms\n'
                '• Accounts engaged in fraudulent activity will be terminated immediately\n'
                '• Terminated accounts may not be eligible for refunds\n\n'

                '8.2 Account Termination by User\n'
                '• Users may terminate their accounts at any time\n'
                '• Termination does not relieve users of obligations incurred prior to termination\n'
                '• Content and assets may remain on the platform after account termination\n\n'

                '8.3 Effects of Termination\n'
                '• Upon termination, users lose access to their accounts and services\n'
                '• Wallet balances may be forfeited if termination is due to violations\n'
                '• Users may request data deletion in accordance with applicable laws',
          ),
          _buildTermsSection(
            title: '9. Limitation of Liability',
            content:
                'To the maximum extent permitted by law, Asatu and its affiliates shall not be liable for any indirect, incidental, special, consequential, or punitive damages, including but not limited to loss of profits, data, use, goodwill, or other intangible losses, resulting from:\n\n'
                '• Your access to or use of or inability to access or use the platform\n'
                '• Any conduct or content of any third party on the platform\n'
                '• Any content obtained from the platform\n'
                '• Unauthorized access, use, or alteration of your transmissions or content\n\n'
                'In no event shall Asatu\'s total liability to you for all claims exceed the amount you have paid to Asatu in the past six months.',
          ),
          _buildTermsSection(
            title: '10. Changes to Terms',
            content:
                '• We may update these terms from time to time\n'
                '• Significant changes will be notified to users via email or platform notification\n'
                '• Continued use of the platform after changes constitutes acceptance of the new terms\n'
                '• Users who do not agree with the updated terms should cease using the platform',
          ),
          const SizedBox(height: 40),
        ],
      ),
    );
  }

  Widget _buildPrivacyPolicy() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Privacy Policy',
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Last Updated: ${DateTime.now().toString().substring(0, 10)}',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          _buildTermsSection(
            title: '1. Information We Collect',
            content:
                '1.1 Information You Provide\n'
                'We collect information you provide directly, including:\n\n'
                '• Account information (name, email, username, password)\n'
                '• Profile information (bio, skills, interests, location, profile picture)\n'
                '• Payment information (processed securely by our payment partners)\n'
                '• Content you upload or create (assets, posts, comments, reviews)\n'
                '• Communications with other users (messages, feedback)\n'
                '• Survey responses and contest entries\n\n'

                '1.2 Information Collected Automatically\n'
                'When you use our platform, we automatically collect certain information, including:\n\n'
                '• Device information (device type, operating system, browser type)\n'
                '• Log data (IP address, access times, pages viewed)\n'
                '• Location information (based on IP address or GPS with your consent)\n'
                '• Usage information (features used, interactions, time spent)\n'
                '• Cookies and similar technologies (for functionality and analytics)\n\n'

                '1.3 Information from Third Parties\n'
                'We may receive information about you from third parties, including:\n\n'
                '• Social media platforms (when you connect your accounts)\n'
                '• Payment processors (transaction information)\n'
                '• Other users (reviews, recommendations, referrals)',
          ),
          _buildTermsSection(
            title: '2. How We Use Your Information',
            content:
                '2.1 Core Service Functionality\n'
                'We use your information to:\n\n'
                '• Create and manage your account\n'
                '• Provide and improve our services\n'
                '• Process transactions and manage payments\n'
                '• Facilitate communication between users\n'
                '• Display your profile to other users\n\n'

                '2.2 Communication and Support\n'
                '• Communicate with you about your account and transactions\n'
                '• Respond to your inquiries and support requests\n'
                '• Send service announcements and updates\n'
                '• Provide personalized recommendations\n\n'

                '2.3 Platform Improvement and Analytics\n'
                '• Analyze usage patterns and trends\n'
                '• Develop new features and services\n'
                '• Test and troubleshoot platform functionality\n'
                '• Measure the effectiveness of our marketing\n\n'

                '2.4 Security and Compliance\n'
                '• Ensure platform security and prevent fraud\n'
                '• Verify identity and enforce platform policies\n'
                '• Detect and prevent prohibited activities\n'
                '• Comply with legal obligations',
          ),
          _buildTermsSection(
            title: '3. Information Sharing',
            content:
                '3.1 Public Information\n'
                'The following information is visible to other users:\n\n'
                '• Your profile information (name, username, bio, skills, interests)\n'
                '• Public content you create or upload\n'
                '• Your ratings and reviews\n'
                '• Your activity on public discussions\n\n'

                '3.2 Sharing with Other Users\n'
                'We share information with other users when you:\n\n'
                '• Engage in transactions (limited to necessary transaction details)\n'
                '• Participate in contests or hiring arrangements\n'
                '• Send messages or communicate through the platform\n'
                '• Collaborate on projects\n\n'

                '3.3 Service Providers\n'
                'We may share your information with third-party service providers who help us:\n\n'
                '• Process payments (payment processors)\n'
                '• Host our platform (cloud services)\n'
                '• Analyze platform usage (analytics providers)\n'
                '• Send communications (email service providers)\n'
                '• Prevent fraud and abuse (security services)\n\n'

                '3.4 Legal Requirements\n'
                'We may disclose your information if required by law, including:\n\n'
                '• In response to legal process (court orders, subpoenas)\n'
                '• To comply with regulatory requirements\n'
                '• To protect our rights, privacy, safety, or property\n'
                '• To investigate suspected fraud or illegal activity\n\n'

                '3.5 Business Transfers\n'
                'If Asatu is involved in a merger, acquisition, or sale of assets, your information may be transferred as part of that transaction.\n\n'

                '3.6 With Your Consent\n'
                'We may share your information in other ways with your explicit consent.\n\n'

                'We do not sell your personal information to third parties for advertising purposes.',
          ),
          _buildTermsSection(
            title: '4. Data Security',
            content:
                '4.1 Security Measures\n'
                'We implement appropriate technical and organizational security measures to protect your information, including:\n\n'
                '• Encryption of sensitive data in transit and at rest\n'
                '• Regular security assessments and penetration testing\n'
                '• Access controls and authentication requirements\n'
                '• Monitoring for suspicious activity\n'
                '• Employee training on security practices\n\n'

                '4.2 Data Retention\n'
                'We retain your information for as long as necessary to:\n\n'
                '• Provide our services to you\n'
                '• Comply with legal obligations\n'
                '• Resolve disputes\n'
                '• Enforce our agreements\n\n'

                '4.3 Limitations\n'
                'While we strive to protect your information, no method of transmission over the internet or electronic storage is 100% secure. We cannot guarantee absolute security of your data.',
          ),
          _buildTermsSection(
            title: '5. Your Rights and Choices',
            content:
                '5.1 Account Information\n'
                'You can access and update your account information through your profile settings. You may also contact us to request changes.\n\n'

                '5.2 Communication Preferences\n'
                'You can manage your communication preferences by:\n\n'
                '• Updating your notification settings in your account\n'
                '• Using the unsubscribe link in our emails\n'
                '• Contacting our support team\n\n'

                '5.3 Privacy Rights\n'
                'Depending on your location, you may have rights to:\n\n'
                '• Access your personal information\n'
                '• Correct inaccurate information\n'
                '• Delete your information (subject to certain exceptions)\n'
                '• Object to certain processing of your information\n'
                '• Restrict processing of your information\n'
                '• Export your data in a portable format\n'
                '• Withdraw consent (where processing is based on consent)\n\n'

                '5.4 Exercising Your Rights\n'
                'To exercise your privacy rights, please contact <NAME_EMAIL>. We will respond to your request within the timeframe required by applicable law.',
          ),
          _buildTermsSection(
            title: '6. International Data Transfers',
            content:
                'Asatu operates globally, which means your information may be transferred to, stored, and processed in countries other than your country of residence. These countries may have different data protection laws than your country.\n\n'

                'When we transfer your information across borders, we take appropriate safeguards to protect your information and comply with applicable laws.',
          ),
          _buildTermsSection(
            title: '7. Children\'s Privacy',
            content:
                'Our platform is not intended for children under the age of 16. We do not knowingly collect personal information from children under 16. If you believe we have collected information from a child under 16, please contact us immediately.',
          ),
          _buildTermsSection(
            title: '8. Third-Party Links and Services',
            content:
                'Our platform may contain links to third-party websites, services, or content that are not owned or controlled by Asatu. We are not responsible for the privacy practices of these third parties. We encourage you to review the privacy policies of any third-party sites you visit.',
          ),
          _buildTermsSection(
            title: '9. Changes to Privacy Policy',
            content:
                'We may update this privacy policy from time to time to reflect changes in our practices or for other operational, legal, or regulatory reasons.\n\n'

                'We will notify you of significant changes by:\n\n'
                '• Posting a notice on our platform\n'
                '• Sending an email to the address associated with your account\n'
                '• Providing a notification when you log in\n\n'

                'We encourage you to review our privacy policy periodically.',
          ),
          _buildTermsSection(
            title: '10. Contact Us',
            content:
                'If you have questions, concerns, or requests regarding this privacy policy or our privacy practices, please contact us at:\n\n'

                'Email: <EMAIL>\n'
                'Address: Asatu Privacy Team, 123 Tech Street, San Francisco, CA 94107\n\n'

                'We will respond to your inquiry as soon as possible and within the timeframe required by applicable law.',
          ),
          const SizedBox(height: 40),
        ],
      ),
    );
  }

  Widget _buildTermsSection({required String title, required String content}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: GoogleFonts.poppins(
              fontSize: 14,
              height: 1.6,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }
}
