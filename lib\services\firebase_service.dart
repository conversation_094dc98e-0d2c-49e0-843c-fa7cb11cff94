import 'dart:async';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:rxdart/rxdart.dart';
import 'package:uuid/uuid.dart';

/// A secure service class to handle Firebase authentication and user management
class FirebaseService {
  // Singleton pattern
  static final FirebaseService _instance = FirebaseService._internal();
  factory FirebaseService() => _instance;
  FirebaseService._internal();

  // Firebase instances
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: [
      'email',
      'profile',
    ],
    // Using the updated configuration from google-services.json
  );

  // Secure storage for sensitive data
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
  );

  // Stream controllers
  final BehaviorSubject<User?> _userSubject = BehaviorSubject<User?>();

  // Getters
  Stream<User?> get userStream => _userSubject.stream;
  User? get currentUser => _auth.currentUser;
  bool get isUserLoggedIn => _auth.currentUser != null;

  // Initialize the service
  Future<void> initialize() async {
    try {
      // Ensure Firebase is initialized
      if (Firebase.apps.isEmpty) {
        await Firebase.initializeApp();
      }

      // Disable reCAPTCHA verification for testing
      if (!kIsWeb) {
        await FirebaseAuth.instance.setSettings(
          appVerificationDisabledForTesting: true,
        );
        debugPrint('Disabled reCAPTCHA verification for testing');
      } else {
        debugPrint('Web platform detected - reCAPTCHA settings not applicable');
      }

      // Listen to auth state changes
      _auth.authStateChanges().listen((User? user) {
        _userSubject.add(user);
      });

      debugPrint('FirebaseService initialized successfully');
    } catch (e) {
      debugPrint('Error initializing FirebaseService: $e');
      rethrow;
    }
  }

  // Email verification
  Future<void> sendEmailVerification() async {
    try {
      await _auth.currentUser?.sendEmailVerification();
    } catch (e) {
      rethrow;
    }
  }

  // Check if email is verified
  bool isEmailVerified() {
    return _auth.currentUser?.emailVerified ?? false;
  }

  // Sign up with email and password
  Future<UserCredential> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String fullName,
  }) async {
    try {
      debugPrint('Attempting to sign up with email: $email');

      // Disable reCAPTCHA verification for non-web platforms
      if (!kIsWeb) {
        await FirebaseAuth.instance.setSettings(
          appVerificationDisabledForTesting: true,
        );
      }

      // Create user with email and password
      final UserCredential userCredential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      debugPrint('User created successfully: ${userCredential.user?.uid}');

      // Update user profile
      await userCredential.user?.updateDisplayName(fullName);
      debugPrint('User profile updated with name: $fullName');

      // Create user document in Firestore
      await _createUserDocument(userCredential.user!, fullName);
      debugPrint('User document created in Firestore');

      return userCredential;
    } on FirebaseAuthException catch (e) {
      debugPrint('FirebaseAuthException during sign up: ${e.code} - ${e.message}');
      throw _handleAuthException(e);
    } catch (e) {
      debugPrint('Error during sign up: $e');
      throw Exception('Failed to create account: $e');
    }
  }

  // Sign in with email and password
  Future<UserCredential> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      debugPrint('Attempting to sign in with email: $email');

      // Disable reCAPTCHA verification for non-web platforms
      if (!kIsWeb) {
        await FirebaseAuth.instance.setSettings(
          appVerificationDisabledForTesting: true,
        );
      }

      final UserCredential userCredential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      debugPrint('Sign in successful for user: ${userCredential.user?.uid}');

      // Check if user is banned
      if (userCredential.user != null) {
        try {
          final userDoc = await _firestore.collection('users').doc(userCredential.user!.uid).get();
          if (userDoc.exists) {
            final userData = userDoc.data();
            if (userData != null && (userData['isBanned'] == true)) {
              // Sign out the banned user
              await _auth.signOut();
              throw Exception('Your account has been banned. Please contact support.');
            }
          }

          // Update last login timestamp (only if not banned)
          await _firestore.collection('users').doc(userCredential.user!.uid).update({
            'lastLogin': FieldValue.serverTimestamp(),
          });
          debugPrint('Updated last login timestamp');
        } catch (e) {
          // If the error is our custom ban exception, rethrow it
          if (e.toString().contains('Your account has been banned')) {
            rethrow;
          }
          debugPrint('Error updating last login timestamp: $e');
          // Continue even if updating timestamp fails
        }
      }

      return userCredential;
    } on FirebaseAuthException catch (e) {
      debugPrint('FirebaseAuthException during sign in: ${e.code} - ${e.message}');
      throw _handleAuthException(e);
    } catch (e) {
      debugPrint('Error during sign in: $e');
      throw Exception('Failed to sign in: $e');
    }
  }

  // Sign in with Google
  Future<UserCredential> signInWithGoogle() async {
    try {
      debugPrint('Starting Google sign-in process');

      // First, try to sign out from any previous sessions
      try {
        await _googleSignIn.signOut();
        debugPrint('Signed out from previous Google session');
      } catch (e) {
        debugPrint('No previous Google session to sign out from: $e');
      }

      // Begin interactive sign-in process
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        debugPrint('Google sign-in was cancelled by the user');
        throw Exception('Google sign-in was cancelled by the user.');
      }

      debugPrint('Google sign-in successful: ${googleUser.email}');

      // Obtain auth details from the request
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      if (googleAuth.accessToken == null || googleAuth.idToken == null) {
        debugPrint('Failed to get Google authentication tokens');
        throw Exception('Failed to authenticate with Google. Please try again.');
      }

      debugPrint('Got Google authentication tokens');

      // Create a new credential
      final OAuthCredential credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken!,
        idToken: googleAuth.idToken!,
      );

      debugPrint('Created OAuth credential, signing in to Firebase');

      // Sign in to Firebase with the Google credential
      final UserCredential userCredential = await _auth.signInWithCredential(credential);

      debugPrint('Firebase sign-in successful: ${userCredential.user?.uid}');

      // Check if this is a new user
      if (userCredential.additionalUserInfo?.isNewUser ?? false) {
        debugPrint('New user detected, creating user document');
        // Create user document in Firestore
        await _createUserDocument(
          userCredential.user!,
          userCredential.user?.displayName ?? 'Google User',
        );
      }

      return userCredential;
    } on FirebaseAuthException catch (e) {
      debugPrint('FirebaseAuthException during Google sign-in: ${e.code} - ${e.message}');
      throw _handleAuthException(e);
    } on PlatformException catch (e) {
      debugPrint('PlatformException during Google sign-in: ${e.code} - ${e.message}');

      // Handle specific platform exceptions
      if (e.code == 'sign_in_failed' && e.message?.contains('10:') == true) {
        throw Exception('Google Sign-In failed. Please make sure you have Google Play Services installed and updated on your device.');
      }

      throw Exception('Failed to sign in with Google: ${e.message}');
    } catch (e) {
      debugPrint('Exception during Google sign-in: $e');
      throw Exception('Failed to sign in with Google: $e');
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _googleSignIn.signOut();
      await _auth.signOut();
    } catch (e) {
      throw Exception('Failed to sign out: $e');
    }
  }

  // Reset password
  Future<void> resetPassword({required String email}) async {
    try {
      debugPrint('Attempting to send password reset email to: $email');
      await _auth.sendPasswordResetEmail(email: email);
      debugPrint('Password reset email sent successfully');
    } on FirebaseAuthException catch (e) {
      debugPrint('FirebaseAuthException during password reset: ${e.code} - ${e.message}');
      throw _handleAuthException(e);
    } catch (e) {
      debugPrint('Error during password reset: $e');
      throw Exception('Failed to reset password: $e');
    }
  }

  // Create user document in Firestore
  Future<void> _createUserDocument(User user, String fullName) async {
    try {
      // Generate a unique username suggestion based on the full name
      final String usernameSuggestion = _generateUsernameSuggestion(fullName);

      // Create user document
      await _firestore.collection('users').doc(user.uid).set({
        'uid': user.uid,
        'email': user.email,
        'displayName': fullName,
        'photoURL': user.photoURL,
        'usernameSuggestion': usernameSuggestion,
        'emailVerified': user.emailVerified,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'profileSetupCompleted': false,
        'isAdmin': false,
        'isBanned': false,
        'lastLogin': FieldValue.serverTimestamp(),
        // Financial fields
        'balance': 0.0,
        'totalEarnings': 0.0,
        'pendingWithdrawals': 0.0,
      }, SetOptions(merge: true));

      // Create user settings document
      await _firestore.collection('user_settings').doc(user.uid).set({
        'uid': user.uid,
        'emailNotifications': true,
        'pushNotifications': true,
        'darkMode': false,
        'language': 'en',
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

    } catch (e) {
      debugPrint('Error creating user document: $e');
      // We don't throw here to avoid blocking the sign-up process
      // The document can be created later
    }
  }

  // Generate a username suggestion based on the full name
  String _generateUsernameSuggestion(String fullName) {
    // Remove spaces and special characters
    String username = fullName.toLowerCase().replaceAll(RegExp(r'[^\w\s]+'), '');
    username = username.replaceAll(' ', '_');

    // Add a random suffix
    final String randomSuffix = const Uuid().v4().substring(0, 4);
    return '${username}_$randomSuffix';
  }

  // Handle Firebase Auth exceptions
  Exception _handleAuthException(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return Exception('No user found with this email.');
      case 'wrong-password':
        return Exception('Wrong password. Please try again.');
      case 'email-already-in-use':
        return Exception('This email is already registered. Please sign in.');
      case 'weak-password':
        return Exception('The password is too weak. Please use a stronger password.');
      case 'invalid-email':
        return Exception('The email address is invalid.');
      case 'user-disabled':
        return Exception('This account has been disabled. Please contact support.');
      case 'too-many-requests':
        return Exception('Too many unsuccessful login attempts. Please try again later.');
      case 'operation-not-allowed':
        return Exception('This operation is not allowed. Please contact support.');
      default:
        return Exception('Authentication error: ${e.message}');
    }
  }

  // Dispose resources
  void dispose() {
    _userSubject.close();
  }
}
