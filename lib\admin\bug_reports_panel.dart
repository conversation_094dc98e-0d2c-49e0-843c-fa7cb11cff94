import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/bug_report_service.dart';
import '../models/bug_report_model.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:url_launcher/url_launcher.dart';

class BugReportsPanel extends StatefulWidget {
  const BugReportsPanel({Key? key}) : super(key: key);

  @override
  State<BugReportsPanel> createState() => _BugReportsPanelState();
}

class _BugReportsPanelState extends State<BugReportsPanel> {
  final BugReportService _bugReportService = BugReportService();

  BugReport? _selectedReport;
  String _statusFilter = 'all';
  String _searchQuery = '';
  bool _isProcessing = false;

  // Pagination variables
  final int _reportsLimit = 20;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Bug Reports',
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Manage and respond to user-reported issues',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),

          // Filter and search
          Row(
            children: [
              // Status filter
              Expanded(
                flex: 2,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      value: _statusFilter,
                      isExpanded: true,
                      hint: const Text('Filter by status'),
                      items: const [
                        DropdownMenuItem(value: 'all', child: Text('All Reports')),
                        DropdownMenuItem(value: 'pending', child: Text('Pending')),
                        DropdownMenuItem(value: 'in_progress', child: Text('In Progress')),
                        DropdownMenuItem(value: 'resolved', child: Text('Resolved')),
                        DropdownMenuItem(value: 'closed', child: Text('Closed')),
                      ],
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _statusFilter = value;
                          });
                        }
                      },
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),

              // Search
              Expanded(
                flex: 3,
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'Search by title or description',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    contentPadding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Bug reports list and details
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Left panel - List of bug reports
                Expanded(
                  flex: 3,
                  child: _buildBugReportsList(),
                ),

                // Right panel - Selected report details
                Expanded(
                  flex: 4,
                  child: _selectedReport != null
                      ? _buildReportDetails()
                      : Center(
                          child: Text(
                            'Select a report to view details',
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                          ),
                        ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Build bug reports list
  Widget _buildBugReportsList() {
    return StreamBuilder<List<BugReport>>(
      stream: _bugReportService.getLimitedBugReports(_reportsLimit),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Text(
              'Error loading reports: ${snapshot.error}',
              style: GoogleFonts.poppins(color: Colors.red),
            ),
          );
        }

        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return Center(
            child: Text(
              'No bug reports found',
              style: GoogleFonts.poppins(fontSize: 16),
            ),
          );
        }

        final reports = snapshot.data!;

        // Apply filters
        final filteredReports = reports.where((report) {
          // Apply status filter
          if (_statusFilter != 'all' && report.status != _statusFilter) {
            return false;
          }

          // Apply search filter
          if (_searchQuery.isNotEmpty) {
            final title = (report.title).toLowerCase();
            final description = (report.description).toLowerCase();
            final query = _searchQuery.toLowerCase();

            return title.contains(query) || description.contains(query);
          }

          return true;
        }).toList();

        if (filteredReports.isEmpty) {
          return Center(
            child: Text(
              'No matching reports found',
              style: GoogleFonts.poppins(fontSize: 16),
            ),
          );
        }

        return ListView.builder(
          itemCount: filteredReports.length,
          itemBuilder: (context, index) {
            final report = filteredReports[index];

            final title = report.title;
            final category = report.category;
            final status = report.status;
            final timestamp = report.createdAt;
            final hasScreenshot = report.screenshotUrl != null && report.screenshotUrl!.isNotEmpty;

            final isSelected = _selectedReport != null && _selectedReport!.id == report.id;

            return Card(
              elevation: isSelected ? 4 : 1,
              margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(
                  color: isSelected ? Colors.blue : Colors.transparent,
                  width: 2,
                ),
              ),
              child: ListTile(
                onTap: () {
                  setState(() {
                    _selectedReport = report;
                  });
                },
                leading: CircleAvatar(
                  backgroundColor: _getStatusColor(status).withAlpha(50),
                  child: Icon(
                    _getStatusIcon(status),
                    color: _getStatusColor(status),
                  ),
                ),
                title: Text(
                  title,
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.blue.shade100,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            category,
                            style: GoogleFonts.poppins(
                              fontSize: 12,
                              color: Colors.blue.shade800,
                            ),
                          ),
                        ),
                        if (hasScreenshot) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.purple.shade100,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.image,
                                  size: 12,
                                  color: Colors.purple.shade800,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'Screenshot',
                                  style: GoogleFonts.poppins(
                                    fontSize: 12,
                                    color: Colors.purple.shade800,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Status: ${_getStatusText(status)}',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: _getStatusColor(status),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Reported: ${_formatDate(timestamp)}',
                      style: GoogleFonts.poppins(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ],
                ),
                trailing: Icon(
                  Icons.chevron_right,
                  color: isSelected ? Colors.blue : Colors.grey,
                ),
              ),
            );
          },
        );
      },
    );
  }

  // Build report details
  Widget _buildReportDetails() {
    if (_selectedReport == null) {
      return const SizedBox();
    }

    final title = _selectedReport!.title;
    final category = _selectedReport!.category;
    final description = _selectedReport!.description;
    final status = _selectedReport!.status;
    final timestamp = _selectedReport!.createdAt;
    final userName = _selectedReport!.userName ?? 'Unknown User';
    final userEmail = _selectedReport!.userEmail ?? 'No email';
    final userId = _selectedReport!.userId;
    final deviceInfo = _selectedReport!.deviceInfo ?? {};
    final screenshotUrl = _selectedReport!.screenshotUrl;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: _getStatusColor(status).withAlpha(50),
                  radius: 24,
                  child: Icon(
                    _getStatusIcon(status),
                    color: _getStatusColor(status),
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: GoogleFonts.poppins(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Reported by $userName',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Chip(
                  label: Text(
                    _getStatusText(status),
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  backgroundColor: _getStatusColor(status),
                ),
              ],
            ),
            const Divider(height: 32),

            // Report details
            _buildInfoSection('Report Details', [
              _buildInfoRow('Category', category),
              _buildInfoRow('Reported On', _formatDateTime(timestamp)),
              _buildInfoRow('User Email', userEmail),
              _buildInfoRow('User ID', userId),
              if (deviceInfo.isNotEmpty) ...[
                _buildInfoRow('Platform', deviceInfo['platform'] ?? 'Unknown'),
                _buildInfoRow('App Version', deviceInfo['appVersion'] ?? 'Unknown'),
              ],
            ]),

            const SizedBox(height: 24),

            // Description
            Text(
              'Description',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Text(
                description,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                ),
              ),
            ),

            // Screenshot
            if (screenshotUrl != null && screenshotUrl.isNotEmpty) ...[
              const SizedBox(height: 24),
              Text(
                'Screenshot',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
              const SizedBox(height: 12),
              ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: InkWell(
                  onTap: () => _openImageInBrowser(screenshotUrl),
                  child: CachedNetworkImage(
                    imageUrl: screenshotUrl,
                    placeholder: (context, url) => Container(
                      height: 200,
                      color: Colors.grey[200],
                      child: const Center(child: CircularProgressIndicator()),
                    ),
                    errorWidget: (context, url, error) => Container(
                      height: 100,
                      color: Colors.grey[200],
                      child: const Center(child: Icon(Icons.error)),
                    ),
                    fit: BoxFit.contain,
                    height: 300,
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Center(
                child: TextButton.icon(
                  icon: const Icon(Icons.open_in_new),
                  label: const Text('View Full Image'),
                  onPressed: () => _openImageInBrowser(screenshotUrl),
                ),
              ),
            ],

            const SizedBox(height: 24),

            // Action buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  // Build info section
  Widget _buildInfoSection(String title, List<Widget> rows) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
        const SizedBox(height: 12),
        ...rows,
      ],
    );
  }

  // Build info row
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build action buttons
  Widget _buildActionButtons() {
    if (_selectedReport == null) {
      return const SizedBox();
    }

    final status = _selectedReport!.status;

    return Wrap(
      spacing: 16,
      runSpacing: 16,
      alignment: WrapAlignment.end,
      children: [
        if (status == 'pending') ...[
          OutlinedButton.icon(
            icon: const Icon(Icons.play_arrow),
            label: const Text('Mark In Progress'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.blue,
              side: const BorderSide(color: Colors.blue),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            onPressed: _isProcessing ? null : () => _updateReportStatus('in_progress'),
          ),
        ],
        if (status == 'pending' || status == 'in_progress') ...[
          OutlinedButton.icon(
            icon: const Icon(Icons.check_circle),
            label: const Text('Mark Resolved'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.green,
              side: const BorderSide(color: Colors.green),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            onPressed: _isProcessing ? null : () => _updateReportStatus('resolved'),
          ),
        ],
        if (status != 'closed') ...[
          OutlinedButton.icon(
            icon: const Icon(Icons.close),
            label: const Text('Close Report'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.red,
              side: const BorderSide(color: Colors.red),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            onPressed: _isProcessing ? null : () => _updateReportStatus('closed'),
          ),
        ],
        const SizedBox(width: 16),
        ElevatedButton.icon(
          icon: const Icon(Icons.send),
          label: const Text('Send Response'),
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF4F46E5),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
          onPressed: _isProcessing ? null : _showSendResponseDialog,
        ),
      ],
    );
  }

  // Update report status
  Future<void> _updateReportStatus(String newStatus) async {
    if (_selectedReport == null) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      final reportId = _selectedReport!.id!;

      // Update report status using the bug report service
      await _bugReportService.updateBugReportStatus(reportId, newStatus);

      // Update the selected report in the UI
      setState(() {
        // Create a new bug report with updated status
        _selectedReport = _selectedReport!.copyWith(status: newStatus);
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Report status updated to ${_getStatusText(newStatus)}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating report status: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  // Show send response dialog
  void _showSendResponseDialog() {
    if (_selectedReport == null) return;

    final TextEditingController responseController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Send Response to User',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Send a response to ${_selectedReport!.userName ?? 'the user'} regarding their bug report.',
              style: GoogleFonts.poppins(),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: responseController,
              decoration: InputDecoration(
                labelText: 'Your Response',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              maxLines: 5,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              final response = responseController.text.trim();
              if (response.isNotEmpty) {
                Navigator.pop(context);
                _sendResponseToUser(response);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4F46E5),
            ),
            child: Text(
              'Send Response',
              style: GoogleFonts.poppins(
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Send response to user
  Future<void> _sendResponseToUser(String response) async {
    if (_selectedReport == null) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      final reportId = _selectedReport!.id!;

      // Add response to bug report using the bug report service
      await _bugReportService.addResponseToBugReport(reportId, response);

      // Update the selected report in the UI to show it has responses
      setState(() {
        _selectedReport = _selectedReport!.copyWith(hasResponses: true);
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Response sent successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sending response: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  // Open image in browser
  void _openImageInBrowser(String url) async {
    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not open image'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Helper methods
  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'in_progress':
        return Colors.blue;
      case 'resolved':
        return Colors.green;
      case 'closed':
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'pending':
        return Icons.hourglass_empty;
      case 'in_progress':
        return Icons.sync;
      case 'resolved':
        return Icons.check_circle;
      case 'closed':
        return Icons.cancel;
      default:
        return Icons.help_outline;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'in_progress':
        return 'In Progress';
      case 'resolved':
        return 'Resolved';
      case 'closed':
        return 'Closed';
      default:
        return 'Unknown';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateTime(DateTime date) {
    return '${date.day}/${date.month}/${date.year} at ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }
}
