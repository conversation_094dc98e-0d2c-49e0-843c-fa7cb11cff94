import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'firebase_messaging_service.dart';

class NotificationService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseMessagingService _messagingService = FirebaseMessagingService();

  // Firebase Cloud Messaging server key (should be stored securely in a real app)
  // This is a placeholder - you need to get your actual server key from Firebase console
  static const String _fcmServerKey = 'YOUR_FCM_SERVER_KEY';
  static const String _fcmEndpoint = 'https://fcm.googleapis.com/fcm/send';

  // Send a notification to a user
  Future<bool> sendNotification({
    required String userId,
    required String title,
    required String body,
    required String type,
    Map<String, dynamic>? data,
  }) async {
    try {
      // Create notification document in Firestore
      final notificationRef = await _firestore.collection('notifications').add({
        'userId': userId,
        'title': title,
        'body': body,
        'type': type,
        'data': data ?? {},
        'read': false,
        'createdAt': FieldValue.serverTimestamp(),
        'sentBy': _auth.currentUser?.uid,
      });

      // Update user's unread notification count
      await _firestore.collection('users').doc(userId).update({
        'unreadNotifications': FieldValue.increment(1),
      });

      // Send push notification using FCM
      await _sendPushNotification(
        userId: userId,
        title: title,
        body: body,
        type: type,
        data: {
          ...data ?? {},
          'notificationId': notificationRef.id,
        },
      );

      return true;
    } catch (e) {
      debugPrint('Error sending notification: $e');
      return false;
    }
  }

  // Send push notification using FCM
  Future<void> _sendPushNotification({
    required String userId,
    required String title,
    required String body,
    required String type,
    required Map<String, dynamic> data,
  }) async {
    try {
      // Get user's FCM tokens
      final userDoc = await _firestore.collection('fcm_tokens').doc(userId).get();

      if (!userDoc.exists) {
        debugPrint('No FCM tokens found for user: $userId');
        return;
      }

      final userData = userDoc.data();
      if (userData == null) {
        debugPrint('FCM tokens document exists but data is null for user: $userId');
        return;
      }

      final List<dynamic> tokens = userData['tokens'] ?? [];

      if (tokens.isEmpty) {
        debugPrint('No FCM tokens found for user: $userId');
        return;
      }

      // For each token, send a push notification
      for (final token in tokens) {
        await _sendFCMMessage(
          token: token.toString(),
          title: title,
          body: body,
          type: type,
          data: data,
        );
      }
    } catch (e) {
      debugPrint('Error sending push notification: $e');
    }
  }

  // Send FCM message to a specific device token
  Future<void> _sendFCMMessage({
    required String token,
    required String title,
    required String body,
    required String type,
    required Map<String, dynamic> data,
  }) async {
    try {
      // Prepare FCM message payload
      final Map<String, dynamic> message = {
        'to': token,
        'notification': {
          'title': title,
          'body': body,
          'sound': 'default',
        },
        'data': {
          'type': type,
          'click_action': 'FLUTTER_NOTIFICATION_CLICK',
          ...data,
        },
        'priority': 'high',
      };

      // Send HTTP request to FCM
      final response = await http.post(
        Uri.parse(_fcmEndpoint),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'key=$_fcmServerKey',
        },
        body: jsonEncode(message),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        debugPrint('FCM message sent successfully: $responseData');
      } else {
        debugPrint('Failed to send FCM message. Status code: ${response.statusCode}');
        debugPrint('Response body: ${response.body}');
      }
    } catch (e) {
      debugPrint('Error sending FCM message: $e');
    }
  }

  // Mark a notification as read
  Future<bool> markNotificationAsRead(String notificationId) async {
    try {
      await _firestore.collection('notifications').doc(notificationId).update({
        'read': true,
      });

      // Decrement user's unread notification count
      final notification = await _firestore.collection('notifications').doc(notificationId).get();
      if (notification.exists) {
        final userId = notification.data()?['userId'];
        if (userId != null) {
          await _firestore.collection('users').doc(userId).update({
            'unreadNotifications': FieldValue.increment(-1),
          });
        }
      }

      return true;
    } catch (e) {
      debugPrint('Error marking notification as read: $e');
      return false;
    }
  }

  // Get user's notifications
  Stream<QuerySnapshot> getUserNotifications(String userId) {
    try {
      // First try with the complex query that requires an index
      return _firestore
          .collection('notifications')
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .snapshots();
    } catch (e) {
      // If the index doesn't exist, fall back to a simpler query
      debugPrint('Using fallback query for notifications: $e');
      return _firestore
          .collection('notifications')
          .where('userId', isEqualTo: userId)
          .snapshots();
    }
  }

  // Get unread notification count
  Stream<int> getUnreadNotificationCount(String userId) {
    if (userId.isEmpty) {
      return Stream.value(0);
    }

    // First try to get the count from the user document (more efficient)
    return _firestore
        .collection('users')
        .doc(userId)
        .snapshots()
        .map((snapshot) {
          if (!snapshot.exists) {
            return 0;
          }

          final userData = snapshot.data();
          if (userData == null) {
            return 0;
          }

          // If unreadNotifications field exists, use it
          if (userData.containsKey('unreadNotifications') && userData['unreadNotifications'] is num) {
            return (userData['unreadNotifications'] as num).toInt();
          }

          // Otherwise, fallback to query-based count
          return 0;
        })
        .handleError((error) {
          debugPrint('Error getting unread notification count: $error');
          return 0;
        });
  }

  // Ensure unread notification count is accurate by querying notifications
  Future<void> refreshUnreadNotificationCount(String userId) async {
    if (userId.isEmpty) return;

    try {
      // Count unread notifications directly from the collection
      final QuerySnapshot unreadNotifications = await _firestore
          .collection('notifications')
          .where('userId', isEqualTo: userId)
          .where('read', isEqualTo: false)
          .get();

      // Update the user document with the accurate count
      await _firestore.collection('users').doc(userId).update({
        'unreadNotifications': unreadNotifications.docs.length,
      });

      debugPrint('NotificationService - Refreshed unread count: ${unreadNotifications.docs.length}');
    } catch (e) {
      debugPrint('NotificationService - Error refreshing unread count: $e');
    }
  }

  // Get user's notifications with pagination (optimized version)
  Future<List<QueryDocumentSnapshot>> getUserNotificationsPaginated(
    String userId, {
    DocumentSnapshot? lastDocument,
    int limit = 20,
  }) async {
    try {
      // Add a timeout to prevent hanging
      const timeout = Duration(seconds: 5);

      // Try to use the indexed query first (with orderBy)
      try {
        Query query = _firestore
            .collection('notifications')
            .where('userId', isEqualTo: userId)
            .orderBy('createdAt', descending: true);

        if (lastDocument != null) {
          query = query.startAfterDocument(lastDocument);
        }

        final querySnapshot = await query.limit(limit).get().timeout(timeout);
        return querySnapshot.docs;
      } catch (indexError) {
        // If index error occurs, fall back to the non-ordered query
        debugPrint('Falling back to non-ordered query: $indexError');

        Query query = _firestore
            .collection('notifications')
            .where('userId', isEqualTo: userId);

        if (lastDocument != null) {
          query = query.startAfterDocument(lastDocument);
        }

        final querySnapshot = await query.limit(limit).get().timeout(timeout);

        // Sort the results manually (only if needed)
        final docs = querySnapshot.docs;
        docs.sort((a, b) {
          final aCreatedAt = a['createdAt'] as Timestamp?;
          final bCreatedAt = b['createdAt'] as Timestamp?;

          if (aCreatedAt == null && bCreatedAt == null) return 0;
          if (aCreatedAt == null) return 1;
          if (bCreatedAt == null) return -1;

          return bCreatedAt.compareTo(aCreatedAt); // Descending order
        });

        return docs;
      }
    } catch (e) {
      debugPrint('Error getting paginated notifications: $e');
      return [];
    }
  }

  // Get user's unread notification count by querying notifications
  // This is a fallback method in case the user document doesn't have the count
  Stream<int> getUnreadNotificationCountByQuery(String userId) {
    return _firestore
        .collection('notifications')
        .where('userId', isEqualTo: userId)
        .where('read', isEqualTo: false)
        .snapshots()
        .map((snapshot) => snapshot.docs.length);
  }

  // Delete a notification
  Future<bool> deleteNotification(String notificationId) async {
    try {
      // Get notification to check if it's unread
      final notification = await _firestore.collection('notifications').doc(notificationId).get();
      if (notification.exists) {
        final isRead = notification.data()?['read'] ?? true;
        final userId = notification.data()?['userId'];

        // Delete the notification
        await _firestore.collection('notifications').doc(notificationId).delete();

        // If notification was unread, decrement the unread count
        if (!isRead && userId != null) {
          await _firestore.collection('users').doc(userId).update({
            'unreadNotifications': FieldValue.increment(-1),
          });
        }
      }

      return true;
    } catch (e) {
      debugPrint('Error deleting notification: $e');
      return false;
    }
  }

  // Update notification settings
  Future<bool> updateNotificationSettings(Map<String, dynamic> settings) async {
    try {
      final userId = _auth.currentUser?.uid;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Update user document with notification settings
      await _firestore.collection('users').doc(userId).update({
        'notificationSettings': settings,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Update FCM topic subscriptions based on settings
      if (settings['push'] == true) {
        // Subscribe to topics based on enabled notification types
        if (settings['messages'] == true) {
          await _messagingService.subscribeToTopic('messages_$userId');
        } else {
          await _messagingService.unsubscribeFromTopic('messages_$userId');
        }

        if (settings['comments'] == true) {
          await _messagingService.subscribeToTopic('comments_$userId');
        } else {
          await _messagingService.unsubscribeFromTopic('comments_$userId');
        }

        if (settings['likes'] == true) {
          await _messagingService.subscribeToTopic('likes_$userId');
        } else {
          await _messagingService.unsubscribeFromTopic('likes_$userId');
        }

        if (settings['followers'] == true) {
          await _messagingService.subscribeToTopic('followers_$userId');
        } else {
          await _messagingService.unsubscribeFromTopic('followers_$userId');
        }

        if (settings['purchases'] == true) {
          await _messagingService.subscribeToTopic('purchases_$userId');
        } else {
          await _messagingService.unsubscribeFromTopic('purchases_$userId');
        }

        if (settings['system'] == true) {
          await _messagingService.subscribeToTopic('system_$userId');
        } else {
          await _messagingService.unsubscribeFromTopic('system_$userId');
        }
      } else {
        // Unsubscribe from all topics if push notifications are disabled
        await _messagingService.unsubscribeFromTopic('messages_$userId');
        await _messagingService.unsubscribeFromTopic('comments_$userId');
        await _messagingService.unsubscribeFromTopic('likes_$userId');
        await _messagingService.unsubscribeFromTopic('followers_$userId');
        await _messagingService.unsubscribeFromTopic('purchases_$userId');
        await _messagingService.unsubscribeFromTopic('system_$userId');
      }

      return true;
    } catch (e) {
      debugPrint('Error updating notification settings: $e');
      return false;
    }
  }

  // Delete all notifications for a user
  Future<bool> deleteAllNotifications(String userId) async {
    try {
      // Get all notifications for the user
      final notifications = await _firestore
          .collection('notifications')
          .where('userId', isEqualTo: userId)
          .get();

      // Delete each notification
      final batch = _firestore.batch();
      for (final doc in notifications.docs) {
        batch.delete(doc.reference);
      }
      await batch.commit();

      // Reset unread notification count
      await _firestore.collection('users').doc(userId).update({
        'unreadNotifications': 0,
      });

      return true;
    } catch (e) {
      debugPrint('Error deleting all notifications: $e');
      return false;
    }
  }
}
