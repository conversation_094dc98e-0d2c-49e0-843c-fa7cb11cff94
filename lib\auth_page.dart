import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'widgets/app_logo.dart';
import 'widgets/google_logo.dart';
import 'providers/user_provider.dart';
import 'widgets/terms_policy_dialog.dart';

// Simple Google G logo widget
class GoogleGLogo extends StatelessWidget {
  final double size;

  const GoogleGLogo({Key? key, this.size = 24.0}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: const BoxDecoration(
        color: Colors.white,
        shape: BoxShape.circle,
      ),
      child: Center(
        child: SizedBox(
          width: size * 0.7,
          height: size * 0.7,
          child: CustomPaint(
            painter: GoogleGPainter(),
          ),
        ),
      ),
    );
  }
}

// Custom painter for Google G logo
class GoogleGPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final double width = size.width;
    final double height = size.height;

    final Paint paint = Paint()
      ..style = PaintingStyle.fill
      ..isAntiAlias = true;

    // Draw the blue part (right)
    paint.color = const Color(0xFF4285F4);
    canvas.drawRect(
      Rect.fromLTWH(width * 0.6, height * 0.2, width * 0.4, height * 0.6),
      paint,
    );

    // Draw the red part (top left)
    paint.color = const Color(0xFFEA4335);
    canvas.drawRect(
      Rect.fromLTWH(0, 0, width * 0.6, height * 0.4),
      paint,
    );

    // Draw the yellow part (bottom left)
    paint.color = const Color(0xFFFBBC05);
    canvas.drawRect(
      Rect.fromLTWH(0, height * 0.6, width * 0.6, height * 0.4),
      paint,
    );

    // Draw the green part (middle left)
    paint.color = const Color(0xFF34A853);
    canvas.drawRect(
      Rect.fromLTWH(0, height * 0.4, width * 0.6, height * 0.2),
      paint,
    );

    // Draw white circle in the middle
    paint.color = Colors.white;
    canvas.drawCircle(
      Offset(width * 0.5, height * 0.5),
      width * 0.2,
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class AuthPage extends StatefulWidget {
  const AuthPage({Key? key}) : super(key: key);

  @override
  State<AuthPage> createState() => _AuthPageState();
}

class _AuthPageState extends State<AuthPage> {
  // Authentication states
  bool _isLoading = false;
  String? _errorMessage;
  bool _hasAcceptedTerms = false;

  // Show terms warning dialog
  void _showTermsWarning() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Terms & Policy Required',
            style: GoogleFonts.poppins(
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),
          content: Text(
            'Please accept our Terms of Service and Privacy Policy to continue.',
            style: GoogleFonts.poppins(
              fontSize: 14,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Cancel',
                style: GoogleFonts.poppins(
                  color: Colors.grey[700],
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _showTermsAndPolicyDialog();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF6A11CB),
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                'View Terms',
                style: GoogleFonts.poppins(),
              ),
            ),
          ],
        );
      },
    );
  }

  // Show terms and policy dialog
  void _showTermsAndPolicyDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return TermsPolicyDialog(
          onAccept: (accepted) {
            setState(() {
              _hasAcceptedTerms = accepted;
            });
          },
        );
      },
    );
  }

  // Official Google icon widget
  Widget _buildGoogleIcon() {
    return Container(
      width: 24,
      height: 24,
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
      ),
      child: const GoogleLogo(size: 24),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Background gradient
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  const Color(0xFF6B73FF).withAlpha(25), // 0.1 * 255 = ~25
                  const Color(0xFF000DFF).withAlpha(13), // 0.05 * 255 = ~13
                ],
              ),
            ),
          ),

          // Main content
          SafeArea(
            child: Row(
              children: [
                // Left panel (for tablets and desktops)
                if (MediaQuery.of(context).size.width > 900)
                  Expanded(
                    flex: 5,
                    child: Container(
                      decoration: const BoxDecoration(
                        color: Color(0xFF4A55E5),
                        image: DecorationImage(
                          image: NetworkImage(
                            'https://images.unsplash.com/photo-1555066931-bf19f8fd1085',
                          ),
                          fit: BoxFit.cover,
                          opacity: 0.2,
                        ),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const AppLogo(
                            size: 120,
                            isAnimated: true,
                            isGradient: false,
                            primaryColor: Colors.white,
                          ),
                          const SizedBox(height: 24),
                          Text(
                            'ASATU',
                            style: GoogleFonts.poppins(
                              fontSize: 48,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              letterSpacing: 2,
                            ),
                          ),
                          const SizedBox(height: 16),
                          SizedBox(
                            width: 300,
                            child: Text(
                              'Connect with developers, share your projects, and grow your skills',
                              textAlign: TextAlign.center,
                              style: GoogleFonts.poppins(
                                fontSize: 16,
                                color: Colors.white.withAlpha(230), // 0.9 * 255 = ~230
                                height: 1.5,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                // Right panel (form area)
                Expanded(
                  flex: MediaQuery.of(context).size.width > 900 ? 7 : 10,
                  child: Align(
                    alignment: Alignment.topCenter,
                    child: SingleChildScrollView(
                      child: ConstrainedBox(
                        constraints: const BoxConstraints(
                          maxWidth: 480,
                        ),
                        child: Padding(
                          padding: const EdgeInsets.only(left: 24.0, right: 24.0, top: 48.0, bottom: 24.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Visible logo only on mobile
                              if (MediaQuery.of(context).size.width <= 900)
                                Padding(
                                  padding: const EdgeInsets.symmetric(vertical: 24.0),
                                  child: Center(
                                    child: Column(
                                      children: [
                                        const AppLogo(
                                          size: 80,
                                          isAnimated: true,
                                          primaryColor: Color(0xFF4A55E5),
                                        ),
                                        const SizedBox(height: 16),
                                        Text(
                                          'ASATU',
                                          style: GoogleFonts.poppins(
                                            fontSize: 32,
                                            fontWeight: FontWeight.bold,
                                            color: const Color(0xFF4A55E5),
                                            letterSpacing: 1.5,
                                          ),
                                        ),
                                        const SizedBox(height: 8),
                                        Text(
                                          'Developer Community & Marketplace',
                                          style: GoogleFonts.poppins(
                                            fontSize: 14,
                                            fontWeight: FontWeight.w500,
                                            color: Colors.grey[600],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),



                              // Social login form
                              _buildSocialLoginForm(),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Social Login Form Builder
  Widget _buildSocialLoginForm() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Error message
        if (_errorMessage != null && _errorMessage!.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(bottom: 24),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.red.shade200),
                boxShadow: [
                  BoxShadow(
                    color: Colors.red.shade100.withAlpha(76), // 0.3 * 255 = ~76
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Icon(Icons.error_outline, color: Colors.red.shade700, size: 24),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      _errorMessage!,
                      style: GoogleFonts.roboto(
                        color: Colors.red.shade700,
                        fontSize: 15,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

        // Social login options
        Container(
          margin: const EdgeInsets.only(bottom: 32),
          child: Column(
            children: [
              Text(
                'Welcome to Asatu',
                style: GoogleFonts.roboto(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Sign in to continue',
                style: GoogleFonts.roboto(
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),



        // Google login button - User-friendly design with rounded corners
        SizedBox(
          width: double.infinity,
          height: 54,
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.black87,
              backgroundColor: Colors.grey.shade50,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: BorderSide(color: Colors.blue.shade100, width: 1),
              ),
              padding: EdgeInsets.zero,
            ),
            onPressed: _isLoading
                ? null
                : () async {
                    if (!_hasAcceptedTerms) {
                      _showTermsWarning();
                      return;
                    }
                    setState(() {
                      _isLoading = true;
                      _errorMessage = null;
                    });

                    try {
                      debugPrint('Auth page: Google login button pressed');
                      final userProvider = Provider.of<UserProvider>(context, listen: false);

                      // Show a snackbar to indicate the process is starting
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Connecting to Google...'),
                            duration: Duration(seconds: 2),
                          ),
                        );
                      }

                      // Try to sign in with Google
                      try {
                        await userProvider.signInWithGoogle();

                        // Save authentication state to shared preferences
                        final prefs = await SharedPreferences.getInstance();
                        await prefs.setBool('is_authenticated', true);
                        debugPrint('Saved authentication state to shared preferences');
                      } catch (googleError) {
                        debugPrint('Google sign-in error, trying alternative approach: $googleError');

                        // Check if we already have a Firebase user (Google Sign-In might have worked despite the error)
                        final currentUser = FirebaseAuth.instance.currentUser;
                        if (currentUser == null) {
                          // If we don't have a user, rethrow the original error
                          rethrow;
                        }

                        debugPrint('Found existing Firebase user: ${currentUser.email}');

                        // Save authentication state to shared preferences
                        final prefs = await SharedPreferences.getInstance();
                        await prefs.setBool('is_authenticated', true);
                        if (currentUser.email != null) {
                          await prefs.setString('auth_email', currentUser.email!);
                        }
                      }

                      // At this point, we should have a Firebase user
                      final currentUser = FirebaseAuth.instance.currentUser;
                      if (currentUser != null && currentUser.email != null) {
                        debugPrint('Using Firebase user: ${currentUser.email}');

                        // Check if user document exists
                        final userDoc = await FirebaseFirestore.instance
                            .collection('users')
                            .doc(currentUser.uid)
                            .get();

                        if (!userDoc.exists) {
                          // Create user document
                          await FirebaseFirestore.instance.collection('users').doc(currentUser.uid).set({
                            'uid': currentUser.uid,
                            'email': currentUser.email,
                            'displayName': currentUser.displayName ?? 'Google User',
                            'photoURL': currentUser.photoURL,
                            'emailVerified': currentUser.emailVerified,
                            'createdAt': FieldValue.serverTimestamp(),
                            'updatedAt': FieldValue.serverTimestamp(),
                            'profileSetupCompleted': false,
                            'isAdmin': false,
                            'isBanned': false,
                            'lastLogin': FieldValue.serverTimestamp(),
                            // Financial fields
                            'balance': 0.0,
                            'totalEarnings': 0.0,
                            'pendingWithdrawals': 0.0,
                            'unreadNotifications': 0,
                          });

                          debugPrint('Created user document for: ${currentUser.email}');
                        }

                        // Get a token for future authentication
                        try {
                          final idToken = await currentUser.getIdToken();
                          if (idToken != null) {
                            final prefs = await SharedPreferences.getInstance();
                            await prefs.setString('auth_token', idToken);
                            debugPrint('Saved auth token to shared preferences');
                          }
                        } catch (tokenError) {
                          debugPrint('Error getting ID token: $tokenError');
                        }

                        // Navigate to home or profile setup
                        if (!mounted) return;
                        if (userDoc.exists && userDoc.data()?['profileSetupCompleted'] == true) {
                          Navigator.pushReplacementNamed(context, '/home');
                        } else {
                          Navigator.pushReplacementNamed(context, '/profile_setup');
                        }
                        return;
                      }
                      debugPrint('Auth page: Google sign-in successful');

                      // Navigate to home or profile setup based on profile completion
                      if (!mounted) return;
                      if (userProvider.isProfileComplete) {
                        Navigator.pushReplacementNamed(context, '/home');
                      } else {
                        Navigator.pushReplacementNamed(context, '/profile_setup');
                      }
                    } catch (e) {
                      debugPrint('Auth page: Google sign-in error: $e');

                      // Show a more user-friendly error message
                      String errorMessage = e.toString();
                      if (errorMessage.contains('ApiException: 10:')) {
                        errorMessage = 'Google Sign-In failed. Please make sure you have Google Play Services installed and updated.';
                      } else if (errorMessage.contains('network_error')) {
                        errorMessage = 'Network error. Please check your internet connection and try again.';
                      } else if (errorMessage.contains('cancelled')) {
                        errorMessage = 'Sign-in was cancelled.';
                      } else if (errorMessage.contains('PigeonUserDetails')) {
                        errorMessage = 'Google Sign-In failed. Please try again.';
                      }

                      setState(() {
                        _errorMessage = errorMessage;
                        _isLoading = false;
                      });
                      FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
                    }
                  },
            child: _isLoading
                ? Center(
                    child: SizedBox(
                      height: 24,
                      width: 24,
                      child: CircularProgressIndicator(
                        color: Colors.grey.shade700,
                        strokeWidth: 2,
                      ),
                    ),
                  )
                : Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        _buildGoogleIcon(),
                        const SizedBox(width: 12),
                        Text(
                          'Sign in with Google',
                          style: GoogleFonts.roboto(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                        ),
                      ],
                    ),
                  ),
          ),
        ),
        const SizedBox(height: 20),

        // Microsoft login button (coming soon)
        SizedBox(
          width: double.infinity,
          height: 54,
          child: ElevatedButton(
            onPressed: _isLoading
                ? null
                : () {
                    if (!_hasAcceptedTerms) {
                      _showTermsWarning();
                      return;
                    }
                    // Show coming soon message
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Microsoft login coming soon!'),
                        duration: Duration(seconds: 2),
                      ),
                    );
                  },
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.black87,
              backgroundColor: Colors.grey.shade50,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: BorderSide(color: Colors.blue.shade100, width: 1),
              ),
              padding: EdgeInsets.zero,
            ),
            child: Stack(
              children: [
                // Main button content
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.window, color: Colors.blue.shade700, size: 20),
                      const SizedBox(width: 12),
                      Text(
                        'Sign in with Microsoft',
                        style: GoogleFonts.roboto(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                // Coming soon badge - tiny and well-positioned
                Positioned(
                  top: 4,
                  right: 4,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.red.shade400,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      'Soon',
                      style: GoogleFonts.roboto(
                        fontSize: 8,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 20),

        // GitHub login button (coming soon)
        SizedBox(
          width: double.infinity,
          height: 54,
          child: ElevatedButton(
            onPressed: _isLoading
                ? null
                : () {
                    if (!_hasAcceptedTerms) {
                      _showTermsWarning();
                      return;
                    }
                    // Show coming soon message
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('GitHub login coming soon!'),
                        duration: Duration(seconds: 2),
                      ),
                    );
                  },
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.black87,
              backgroundColor: Colors.grey.shade50,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: BorderSide(color: Colors.blue.shade100, width: 1),
              ),
              padding: EdgeInsets.zero,
            ),
            child: Stack(
              children: [
                // Main button content
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.code, color: Colors.black87, size: 20),
                      const SizedBox(width: 12),
                      Text(
                        'Sign in with GitHub',
                        style: GoogleFonts.roboto(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                // Coming soon badge - tiny and well-positioned
                Positioned(
                  top: 4,
                  right: 4,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.red.shade400,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      'Soon',
                      style: GoogleFonts.roboto(
                        fontSize: 8,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),

        const SizedBox(height: 40),

        // Terms and Policy section at the bottom
        GestureDetector(
          onTap: () {
            _showTermsAndPolicyDialog();
          },
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Row(
              children: [
                Checkbox(
                  value: _hasAcceptedTerms,
                  onChanged: (value) {
                    setState(() {
                      _hasAcceptedTerms = value ?? false;
                    });
                  },
                  activeColor: const Color(0xFF6A11CB),
                ),
                Expanded(
                  child: Text.rich(
                    TextSpan(
                      text: 'I agree to the ',
                      style: GoogleFonts.roboto(
                        fontSize: 14,
                        color: Colors.black87,
                      ),
                      children: [
                        TextSpan(
                          text: 'Terms of Service',
                          style: GoogleFonts.roboto(
                            color: const Color(0xFF6A11CB),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const TextSpan(text: ' and '),
                        TextSpan(
                          text: 'Privacy Policy',
                          style: GoogleFonts.roboto(
                            color: const Color(0xFF6A11CB),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}