import 'package:flutter/material.dart';

/// A service that provides utilities for responsive layouts and prevents overflow issues
class ResponsiveLayoutService {
  static final ResponsiveLayoutService _instance = ResponsiveLayoutService._internal();
  
  factory ResponsiveLayoutService() {
    return _instance;
  }
  
  ResponsiveLayoutService._internal();
  
  /// Get the screen size category based on width
  ScreenSizeCategory getScreenSizeCategory(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width < 360) {
      return ScreenSizeCategory.extraSmall;
    } else if (width < 600) {
      return ScreenSizeCategory.small;
    } else if (width < 900) {
      return ScreenSizeCategory.medium;
    } else if (width < 1200) {
      return ScreenSizeCategory.large;
    } else {
      return ScreenSizeCategory.extraLarge;
    }
  }
  
  /// Get a value based on screen size category
  T getValueForScreenSize<T>({
    required BuildContext context,
    required T extraSmall,
    T? small,
    T? medium,
    T? large,
    T? extraLarge,
  }) {
    final screenSize = getScreenSizeCategory(context);
    
    switch (screenSize) {
      case ScreenSizeCategory.extraSmall:
        return extraSmall;
      case ScreenSizeCategory.small:
        return small ?? extraSmall;
      case ScreenSizeCategory.medium:
        return medium ?? small ?? extraSmall;
      case ScreenSizeCategory.large:
        return large ?? medium ?? small ?? extraSmall;
      case ScreenSizeCategory.extraLarge:
        return extraLarge ?? large ?? medium ?? small ?? extraSmall;
    }
  }
  
  /// Get font size based on screen size
  double getResponsiveFontSize(
    BuildContext context, {
    required double baseFontSize,
    double? smallScreenFontSize,
    double? largeScreenFontSize,
  }) {
    final screenSize = getScreenSizeCategory(context);
    
    switch (screenSize) {
      case ScreenSizeCategory.extraSmall:
        return smallScreenFontSize ?? (baseFontSize * 0.8);
      case ScreenSizeCategory.small:
        return smallScreenFontSize ?? (baseFontSize * 0.9);
      case ScreenSizeCategory.medium:
        return baseFontSize;
      case ScreenSizeCategory.large:
        return largeScreenFontSize ?? (baseFontSize * 1.1);
      case ScreenSizeCategory.extraLarge:
        return largeScreenFontSize ?? (baseFontSize * 1.2);
    }
  }
  
  /// Get padding based on screen size
  EdgeInsets getResponsivePadding(
    BuildContext context, {
    required EdgeInsets defaultPadding,
    EdgeInsets? smallScreenPadding,
    EdgeInsets? largeScreenPadding,
  }) {
    final screenSize = getScreenSizeCategory(context);
    
    switch (screenSize) {
      case ScreenSizeCategory.extraSmall:
      case ScreenSizeCategory.small:
        return smallScreenPadding ?? EdgeInsets.all(defaultPadding.top / 1.5);
      case ScreenSizeCategory.medium:
        return defaultPadding;
      case ScreenSizeCategory.large:
      case ScreenSizeCategory.extraLarge:
        return largeScreenPadding ?? EdgeInsets.all(defaultPadding.top * 1.5);
    }
  }
  
  /// Get a safe widget that prevents overflow
  Widget getSafeWidget({
    required Widget child,
    bool preventOverflow = true,
    TextOverflow textOverflow = TextOverflow.ellipsis,
    int? maxLines,
  }) {
    if (preventOverflow && child is Text) {
      final Text textWidget = child;
      return Text(
        textWidget.data ?? '',
        style: textWidget.style,
        overflow: textOverflow,
        maxLines: maxLines ?? textWidget.maxLines,
        textAlign: textWidget.textAlign,
        softWrap: true,
      );
    } else if (preventOverflow) {
      return Flexible(
        child: child,
      );
    }
    
    return child;
  }
  
  /// Get a safe row that prevents overflow
  Widget getSafeRow({
    required List<Widget> children,
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
    CrossAxisAlignment crossAxisAlignment = CrossAxisAlignment.center,
    bool preventOverflow = true,
  }) {
    if (preventOverflow) {
      return Row(
        mainAxisAlignment: mainAxisAlignment,
        crossAxisAlignment: crossAxisAlignment,
        mainAxisSize: MainAxisSize.min,
        children: children.map((child) {
          if (child is Flexible || child is Expanded) {
            return child;
          }
          return Flexible(
            fit: FlexFit.loose,
            child: child,
          );
        }).toList(),
      );
    }
    
    return Row(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      children: children,
    );
  }
  
  /// Get a responsive grid column count based on screen size
  int getGridColumnsForScreenType({
    required BuildContext context,
    required int mobile,
    int? tablet,
    int? desktop,
  }) {
    final screenSize = getScreenSizeCategory(context);
    
    switch (screenSize) {
      case ScreenSizeCategory.extraSmall:
      case ScreenSizeCategory.small:
        return mobile;
      case ScreenSizeCategory.medium:
        return tablet ?? (mobile * 1.5).round();
      case ScreenSizeCategory.large:
      case ScreenSizeCategory.extraLarge:
        return desktop ?? (mobile * 2).round();
    }
  }
}

/// Extension methods for BuildContext to easily access responsive layout utilities
extension ResponsiveLayoutExtension on BuildContext {
  /// Get the screen size category
  ScreenSizeCategory get screenSizeCategory => 
      ResponsiveLayoutService().getScreenSizeCategory(this);
  
  /// Get a value based on screen size
  T responsiveValue<T>({
    required T extraSmall,
    T? small,
    T? medium,
    T? large,
    T? extraLarge,
  }) {
    return ResponsiveLayoutService().getValueForScreenSize(
      context: this,
      extraSmall: extraSmall,
      small: small,
      medium: medium,
      large: large,
      extraLarge: extraLarge,
    );
  }
  
  /// Get a responsive font size
  double responsiveFontSize({
    required double size,
    double? smallScreenSize,
    double? largeScreenSize,
  }) {
    return ResponsiveLayoutService().getResponsiveFontSize(
      this,
      baseFontSize: size,
      smallScreenFontSize: smallScreenSize,
      largeScreenFontSize: largeScreenSize,
    );
  }
  
  /// Get responsive padding
  EdgeInsets responsivePadding({
    required EdgeInsets padding,
    EdgeInsets? smallScreenPadding,
    EdgeInsets? largeScreenPadding,
  }) {
    return ResponsiveLayoutService().getResponsivePadding(
      this,
      defaultPadding: padding,
      smallScreenPadding: smallScreenPadding,
      largeScreenPadding: largeScreenPadding,
    );
  }
  
  /// Check if the screen is small
  bool get isSmallScreen => 
      screenSizeCategory == ScreenSizeCategory.small || 
      screenSizeCategory == ScreenSizeCategory.extraSmall;
  
  /// Check if the screen is medium
  bool get isMediumScreen => 
      screenSizeCategory == ScreenSizeCategory.medium;
  
  /// Check if the screen is large
  bool get isLargeScreen => 
      screenSizeCategory == ScreenSizeCategory.large || 
      screenSizeCategory == ScreenSizeCategory.extraLarge;
}

/// Extension methods for Widget to easily create safe widgets
extension SafeWidgetExtension on Widget {
  /// Create a safe widget that prevents overflow
  Widget safe({
    bool preventOverflow = true,
    TextOverflow textOverflow = TextOverflow.ellipsis,
    int? maxLines,
  }) {
    return ResponsiveLayoutService().getSafeWidget(
      child: this,
      preventOverflow: preventOverflow,
      textOverflow: textOverflow,
      maxLines: maxLines,
    );
  }
}

/// Screen size categories
enum ScreenSizeCategory {
  extraSmall,  // < 360
  small,       // < 600
  medium,      // < 900
  large,       // < 1200
  extraLarge,  // >= 1200
}
