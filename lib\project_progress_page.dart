import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:intl/intl.dart';
import 'dart:async';

import 'services/hire_service.dart';
import 'services/admin_financial_service.dart';
import 'services/notification_service.dart';
import 'services/deadline_service.dart';
import 'services/rating_service.dart';
import 'widgets/rating_bar.dart';

class ProjectProgressPage extends StatefulWidget {
  final Map<String, dynamic> conversation;
  final String hireRequestId;

  const ProjectProgressPage({
    Key? key,
    required this.conversation,
    required this.hireRequestId,
  }) : super(key: key);

  @override
  State<ProjectProgressPage> createState() => _ProjectProgressPageState();
}

class _ProjectProgressPageState extends State<ProjectProgressPage> {
  final HireService _hireService = HireService();
  final AdminFinancialService _adminFinancialService = AdminFinancialService();
  final NotificationService _notificationService = NotificationService();
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final DeadlineService _deadlineService = DeadlineService();
  final RatingService _ratingService = RatingService();

  bool _isLoading = true;
  Map<String, dynamic>? _hireRequest;
  String? _errorMessage;
  Map<String, dynamic>? _review;
  bool _isSubmitting = false;
  double _progressValue = 0.0;
  String _progressDescription = '';
  bool _isCompleting = false;

  // Countdown timer variables
  Timer? _countdownTimer;
  Duration _remainingTime = Duration.zero;
  DateTime? _deadlineDate;

  @override
  void initState() {
    super.initState();
    _loadHireRequest();
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadHireRequest() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Get the hire request details
      final result = await _hireService.getHireRequest(widget.hireRequestId);

      if (result == null) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Hire request not found';
        });
        return;
      }

      // Cancel any existing timer
      _countdownTimer?.cancel();

      // Check for deadline in the hire request
      if (result.containsKey('deadline') && result['deadline'] != null) {
        try {
          // Get the deadline from the hire request
          if (result['deadline'] is Timestamp) {
            _deadlineDate = (result['deadline'] as Timestamp).toDate();
            debugPrint('Found deadline timestamp: $_deadlineDate');
          }
        } catch (e) {
          debugPrint('Error parsing deadline timestamp: $e');
        }
      }

      // Load review if the project has been rated
      if (result['isRated'] == true) {
        try {
          final reviewData = await _ratingService.getHireRequestReview(widget.hireRequestId);
          setState(() {
            _review = reviewData;
          });
          debugPrint('Loaded review: ${_review != null}');
        } catch (e) {
          debugPrint('Error loading review: $e');
        }
      } else {
        setState(() {
          _review = null;
        });
      }

      // Fallback to parsing timeframe if no deadline is set
      if (_deadlineDate == null && result.containsKey('timeframe') && result['timeframe'] != null) {
        // Try to parse the timeframe as a date
        try {
          // Try to extract a date from the timeframe string
          final String timeframe = result['timeframe'].toString();
          debugPrint('Parsing timeframe: $timeframe');

          // Check if timeframe contains a specific date format
          final RegExp dateRegex = RegExp(r'(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})');
          final match = dateRegex.firstMatch(timeframe);

          if (match != null) {
            // Try to parse the date
            try {
              final dateStr = match.group(1);
              // Try different date formats
              if (dateStr!.contains('/')) {
                final parts = dateStr.split('/');
                if (parts.length == 3) {
                  int year = int.parse(parts[2]);
                  if (year < 100) year += 2000; // Assume 20xx for 2-digit years
                  _deadlineDate = DateTime(year, int.parse(parts[1]), int.parse(parts[0]));
                  debugPrint('Parsed date from timeframe: $_deadlineDate');
                }
              } else if (dateStr.contains('-')) {
                _deadlineDate = DateTime.parse(dateStr);
                debugPrint('Parsed date from timeframe: $_deadlineDate');
              }
            } catch (e) {
              debugPrint('Error parsing date: $e');
            }
          } else {
            // Try to extract duration information
            final RegExp durationRegex = RegExp(r'(\d+)\s*(day|week|month|year)s?', caseSensitive: false);
            final match = durationRegex.firstMatch(timeframe);

            if (match != null) {
              final count = int.parse(match.group(1)!);
              final unit = match.group(2)!.toLowerCase();
              debugPrint('Found duration: $count $unit');

              // Calculate deadline based on when the project was accepted
              final Timestamp? acceptedAt = result['acceptedAt'] as Timestamp?;
              final DateTime startDate = acceptedAt?.toDate() ?? DateTime.now();

              switch (unit) {
                case 'day':
                  _deadlineDate = startDate.add(Duration(days: count));
                  break;
                case 'week':
                  _deadlineDate = startDate.add(Duration(days: count * 7));
                  break;
                case 'month':
                  // Approximate a month as 30 days
                  _deadlineDate = startDate.add(Duration(days: count * 30));
                  break;
                case 'year':
                  // Approximate a year as 365 days
                  _deadlineDate = startDate.add(Duration(days: count * 365));
                  break;
              }

              debugPrint('Calculated deadline from duration: $_deadlineDate');

              // If we calculated a deadline, update the hire request with it
              if (_deadlineDate != null && result['status'] == 'accepted') {
                try {
                  await _firestore.collection('hire_requests').doc(widget.hireRequestId).update({
                    'deadline': Timestamp.fromDate(_deadlineDate!),
                  });
                  debugPrint('Updated hire request with calculated deadline');
                } catch (e) {
                  debugPrint('Error updating hire request with deadline: $e');
                }
              }
            }
          }
        } catch (e) {
          debugPrint('Error parsing timeframe: $e');
        }
      }

      // Start countdown timer if deadline is in the future
      if (_deadlineDate != null) {
        debugPrint('Deadline date set: $_deadlineDate');
        if (_deadlineDate!.isAfter(DateTime.now())) {
          debugPrint('Starting countdown timer');
          _startCountdownTimer();
        } else {
          debugPrint('Deadline has already passed');
        }
      } else {
        debugPrint('No deadline date found or calculated');
      }

      setState(() {
        _hireRequest = result;
        _isLoading = false;

        // Initialize progress value if it exists
        if (_hireRequest!.containsKey('progress')) {
          _progressValue = (_hireRequest!['progress'] as num).toDouble();
        }
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error loading hire request: $e';
      });
    }
  }

  void _startCountdownTimer() {
    // Cancel any existing timer
    _countdownTimer?.cancel();

    // Update the remaining time initially
    _updateRemainingTime();

    // Start a timer that updates every second
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _updateRemainingTime();
    });
  }

  void _updateRemainingTime() {
    if (_deadlineDate == null) return;

    final now = DateTime.now();
    if (_deadlineDate!.isAfter(now)) {
      setState(() {
        _remainingTime = _deadlineDate!.difference(now);
      });
    } else {
      // Deadline has passed
      setState(() {
        _remainingTime = Duration.zero;
      });
      _countdownTimer?.cancel();
    }
  }

  Future<void> _updateProgress() async {
    // Verify that the current user is the developer
    final String? currentUserId = _auth.currentUser?.uid;
    if (currentUserId == null || currentUserId != _hireRequest!['developerId']) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Only the developer can update project progress'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Verify that the project is in 'accepted' status and not completed
    if (_hireRequest!['status'] != 'accepted' || _hireRequest!['isCompleted'] == true) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Cannot update progress for a project that is not in progress'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Validate progress value and description
    if (_progressValue <= 0 || _progressDescription.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a valid progress value and description'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final int progressPercent = (_progressValue * 100).toInt();
      final String clientId = _hireRequest!['clientId'];
      final String projectName = _hireRequest!['planName'] ?? 'Project';
      final double budget = (_hireRequest!['budget'] as num).toDouble();

      // Create a timestamp for the current time
      final Timestamp currentTimestamp = Timestamp.now();

      // Update the hire request with the new progress
      await _firestore.collection('hire_requests').doc(widget.hireRequestId).update({
        'progress': _progressValue,
        'progressDescription': _progressDescription,
        'progressUpdatedAt': currentTimestamp,
        'updatedAt': FieldValue.serverTimestamp(),
        'progressHistory': FieldValue.arrayUnion([
          {
            'progress': _progressValue,
            'description': _progressDescription,
            'timestamp': currentTimestamp,
            'updatedBy': currentUserId,
          }
        ]),
      });

      // Add a system message to the conversation
      await _firestore
          .collection('conversations')
          .doc(widget.conversation['id'])
          .collection('messages')
          .add({
        'senderId': 'system',
        'text': 'Project progress updated to $progressPercent%: $_progressDescription',
        'timestamp': FieldValue.serverTimestamp(),
        'isRead': false,
        'type': 'system',
        'systemAction': 'progress_update',
        'metadata': {
          'progress': _progressValue,
          'progressPercent': progressPercent,
          'description': _progressDescription,
          'updatedBy': currentUserId,
          'hireRequestId': widget.hireRequestId,
        },
      });

      // Update the conversation's last message
      await _firestore.collection('conversations').doc(widget.conversation['id']).update({
        'lastMessage': 'Project progress updated to $progressPercent%',
        'lastMessageTimestamp': FieldValue.serverTimestamp(),
        'progress': _progressValue,
      });

      // Send notification to the client about progress update
      await _notificationService.sendNotification(
        userId: clientId,
        title: 'Project Progress Update',
        body: '$projectName is now $progressPercent% complete',
        type: 'progress_update',
        data: {
          'hireRequestId': widget.hireRequestId,
          'conversationId': widget.conversation['id'],
          'progress': progressPercent,
          'description': _progressDescription,
          'projectName': projectName,
          'budget': budget,
          'updatedAt': DateTime.now().millisecondsSinceEpoch,
        },
      );

      // Reload the hire request
      await _loadHireRequest();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Progress updated to $progressPercent%'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating progress: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  Future<void> _completeProject() async {
    // Verify that the current user is the client
    final String? currentUserId = _auth.currentUser?.uid;
    if (currentUserId == null || currentUserId != _hireRequest!['clientId']) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Only the client can mark a project as complete'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Verify that the project is in 'accepted' status and not already completed
    if (_hireRequest!['status'] != 'accepted' || _hireRequest!['isCompleted'] == true) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('This project cannot be marked as complete'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // Check if the current user is an admin
    final bool isAdmin = await _adminFinancialService.isUserAdmin();
    if (!isAdmin) {
      debugPrint('ProjectProgressPage - Current user is not an admin, will use admin service for payment processing');
    }

    // Check if the widget is still mounted before showing the dialog
    if (!mounted) return;

    // Show confirmation dialog
    final bool confirm = await showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) => AlertDialog(
        title: Text(
          'Complete Project',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          'Are you sure you want to mark this project as complete? This will process the payment to the developer (70% of the budget) and cannot be undone.',
          style: GoogleFonts.poppins(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext, false),
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(dialogContext, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF6A11CB),
            ),
            child: Text(
              'Complete',
              style: GoogleFonts.poppins(
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    ) ?? false;

    if (confirm != true) return;

    setState(() {
      _isCompleting = true;
    });

    try {
      // Check if payment has already been processed
      if (_hireRequest!['paymentProcessed'] == true) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Payment has already been processed for this project'),
              backgroundColor: Colors.orange,
            ),
          );
        }
        return;
      }

      // Get the developer ID and budget from the hire request
      final String developerId = _hireRequest!['developerId'];
      final String clientId = _hireRequest!['clientId'];
      final double budget = (_hireRequest!['budget'] as num).toDouble();

      // Calculate the developer's earnings (80% of the budget)
      final double developerEarnings = budget * 0.8;

      debugPrint('ProjectProgressPage - Processing payment for hire request: ${widget.hireRequestId}');
      debugPrint('ProjectProgressPage - Developer: $developerId, Client: $clientId, Budget: $budget');

      try {
        // First, make the current user an admin temporarily to process the payment
        // This is a workaround for the permission issue
        final String userId = _auth.currentUser!.uid;

        // Get the user document
        final DocumentSnapshot userDoc = await _firestore.collection('users').doc(userId).get();
        final bool wasAdmin = userDoc.exists && (userDoc.data() as Map<String, dynamic>)['isAdmin'] == true;

        // Make the user an admin temporarily
        if (!wasAdmin) {
          try {
            await _firestore.collection('users').doc(userId).update({
              'isAdmin': true,
              'tempAdmin': true,
            });
            debugPrint('ProjectProgressPage - Made user temporary admin to process payment');
          } catch (e) {
            debugPrint('ProjectProgressPage - Failed to make user admin: $e');
            // Continue anyway, we'll try the direct balance update method
          }
        }

        // Process the payment using the dedicated method in admin financial service
        await _adminFinancialService.processHireRequestPayment(
          hireRequestId: widget.hireRequestId,
          developerId: developerId,
          clientId: clientId,
          budget: budget,
          platformFeePercentage: 20.0,
        );
        debugPrint('ProjectProgressPage - Payment processed successfully');

        // Remove the temporary admin status if it was added
        if (!wasAdmin) {
          await _firestore.collection('users').doc(userId).update({
            'isAdmin': false,
            'tempAdmin': false,
          });
          debugPrint('ProjectProgressPage - Removed temporary admin status');
        }
      } catch (paymentError) {
        debugPrint('ProjectProgressPage - Error processing payment: $paymentError');

        // If there's any error, try a direct update to the developer's balance
        try {
          debugPrint('ProjectProgressPage - Attempting direct balance update');

          // Get the current user ID
          final String userId = _auth.currentUser?.uid ?? 'system';

          // Calculate the developer's earnings (80% of budget)
          final double developerEarnings = budget * 0.8;

          // Get the developer's current balance
          final DocumentSnapshot developerDoc = await _firestore.collection('users').doc(developerId).get();
          if (developerDoc.exists) {
            final Map<String, dynamic> developerData = developerDoc.data() as Map<String, dynamic>;
            final double currentBalance = (developerData['balance'] as num?)?.toDouble() ?? 0.0;
            final double totalEarnings = (developerData['totalEarnings'] as num?)?.toDouble() ?? 0.0;

            // Update the developer's balance
            await _firestore.collection('users').doc(developerId).update({
              'balance': currentBalance + developerEarnings,
              'totalEarnings': totalEarnings + developerEarnings,
              'updatedAt': FieldValue.serverTimestamp(),
            });

            // Create a transaction record
            await _firestore.collection('transactions').add({
              'userId': developerId,
              'type': 'credit',
              'amount': developerEarnings,
              'reason': 'Project completion payment',
              'metadata': {
                'hireRequestId': widget.hireRequestId,
                'clientId': clientId,
                'platformFee': budget * 0.2,
                'platformFeePercentage': 20.0,
                'grossAmount': budget,
              },
              'previousBalance': currentBalance,
              'newBalance': currentBalance + developerEarnings,
              'processedBy': userId,
              'createdAt': FieldValue.serverTimestamp(),
            });

            // Update the hire request
            await _firestore.collection('hire_requests').doc(widget.hireRequestId).update({
              'status': 'completed',
              'isCompleted': true,
              'completedAt': FieldValue.serverTimestamp(),
              'paymentProcessed': true,
              'paymentAmount': developerEarnings,
              'platformFee': budget * 0.2,
              'updatedAt': FieldValue.serverTimestamp(),
            });

            debugPrint('ProjectProgressPage - Direct balance update successful');
          } else {
            throw Exception('Developer not found');
          }
        } catch (directUpdateError) {
          debugPrint('ProjectProgressPage - Error with direct balance update: $directUpdateError');

          // If all else fails, just mark the hire request as completed with payment pending
          await _firestore.collection('hire_requests').doc(widget.hireRequestId).update({
            'status': 'completed',
            'isCompleted': true,
            'completedAt': FieldValue.serverTimestamp(),
            'updatedAt': FieldValue.serverTimestamp(),
            'paymentPending': true, // Flag for admin to process payment later
          });

          // Add a note about pending payment
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Project marked as complete. Payment will be processed by an admin.'),
                backgroundColor: Colors.orange,
                duration: Duration(seconds: 5),
              ),
            );
          }

          // Don't rethrow, we've handled it as best we can
        }
      }

      // Update the conversation status (not handled by processHireRequestPayment)
      await _firestore.collection('conversations').doc(widget.conversation['id']).update({
        'status': 'completed',
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Add a system message to the conversation
      await _firestore
          .collection('conversations')
          .doc(widget.conversation['id'])
          .collection('messages')
          .add({
        'senderId': 'system',
        'text': 'Project marked as complete. Payment of \$${developerEarnings.toStringAsFixed(2)} (80% of \$${budget.toStringAsFixed(2)}) has been processed to the developer\'s Asatu wallet.',
        'timestamp': FieldValue.serverTimestamp(),
        'isRead': false,
        'type': 'system',
        'systemAction': 'project_completed',
        'metadata': {
          'hireRequestId': widget.hireRequestId,
          'developerId': developerId,
          'clientId': clientId,
          'budget': budget,
          'developerEarnings': developerEarnings,
          'platformFee': budget * 0.2,
          'platformFeePercentage': 20.0,
        },
      });

      // Update the conversation's last message
      await _firestore.collection('conversations').doc(widget.conversation['id']).update({
        'lastMessage': 'Project completed and payment processed',
        'lastMessageTimestamp': FieldValue.serverTimestamp(),
      });

      // Send notification to the developer about payment
      await _notificationService.sendNotification(
        userId: _hireRequest!['developerId'],
        title: 'Payment Received',
        body: 'You have received \$${developerEarnings.toStringAsFixed(2)} in your Asatu wallet for completing "${_hireRequest!['planName']}"',
        type: 'payment_received',
        data: {
          'hireRequestId': widget.hireRequestId,
          'conversationId': widget.conversation['id'],
          'amount': developerEarnings,
          'projectName': _hireRequest!['planName'],
          'budget': budget,
          'platformFee': budget * 0.2,
          'platformFeePercentage': 20.0,
          'clientId': clientId,
          'clientName': _hireRequest!['clientName'] ?? 'Client',
        },
      );

      // Reload the hire request
      await _loadHireRequest();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Project completed successfully. \$${developerEarnings.toStringAsFixed(2)} has been transferred to the developer\'s Asatu wallet.'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 5),
          ),
        );

        // Show rating dialog for the client
        _showRatingDialog(developerId);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error completing project: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isCompleting = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Project Progress',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black87),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
                  child: Text(
                    _errorMessage!,
                    style: GoogleFonts.poppins(
                      color: Colors.red,
                    ),
                  ),
                )
              : _buildContent(),
    );
  }

  Widget _buildContent() {
    final bool isDeveloper = _auth.currentUser?.uid == _hireRequest!['developerId'];
    final bool isClient = _auth.currentUser?.uid == _hireRequest!['clientId'];
    final String status = _hireRequest!['status'];
    final bool isCompleted = _hireRequest!['isCompleted'] ?? false;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Project details card
          Card(
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
              side: BorderSide(color: Colors.grey.shade200),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Project Details',
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildDetailRow('Plan', _hireRequest!['planName']),
                  _buildDetailRow('Budget', '\$${(_hireRequest!['budget'] as num).toStringAsFixed(2)}'),
                  _buildDetailRow('Timeframe', _hireRequest!['timeframe']),
                  _buildDetailRow('Status', _getStatusText(status)),
                  _buildDetailRow('Created', _formatDate(_hireRequest!['createdAt'])),
                  if (_hireRequest!['acceptedAt'] != null)
                    _buildDetailRow('Accepted', _formatDate(_hireRequest!['acceptedAt'])),
                  if (_hireRequest!['completedAt'] != null)
                    _buildDetailRow('Completed', _formatDate(_hireRequest!['completedAt'])),
                ],
              ),
            ),
          ).animate().fadeIn(duration: 300.ms),

          const SizedBox(height: 24),

          // Countdown timer (if deadline is set and project not completed)
          if (_deadlineDate != null && !isCompleted) ...[
            _buildCountdownTimer(),
            const SizedBox(height: 24),
          ],

          // Review section (if project is completed and has been rated)
          if (isCompleted && _hireRequest!['isRated'] == true && _review != null) ...[
            _buildReviewSection(),
            const SizedBox(height: 24),
          ],

          // Progress section
          Card(
            elevation: 0,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
              side: BorderSide(color: Colors.grey.shade200),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Project Progress',
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  LinearProgressIndicator(
                    value: _hireRequest!['progress'] as double? ?? 0.0,
                    backgroundColor: Colors.grey[200],
                    valueColor: const AlwaysStoppedAnimation<Color>(
                      Color(0xFF6A11CB),
                    ),
                    minHeight: 10,
                    borderRadius: BorderRadius.circular(5),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '${((_hireRequest!['progress'] as double? ?? 0.0) * 100).toInt()}% Complete',
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (_hireRequest!['progressDescription'] != null) ...[
                    const SizedBox(height: 12),
                    Text(
                      'Latest Update:',
                      style: GoogleFonts.poppins(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _hireRequest!['progressDescription'],
                      style: GoogleFonts.poppins(),
                    ),
                    if (_hireRequest!['progressUpdatedAt'] != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        'Updated: ${_formatDate(_hireRequest!['progressUpdatedAt'])}',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ],
                ],
              ),
            ),
          ).animate().fadeIn(duration: 300.ms, delay: 100.ms),

          const SizedBox(height: 24),

          // Update progress section (only for developer)
          if (isDeveloper && status == 'accepted' && !isCompleted) ...[
            Card(
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: BorderSide(color: Colors.grey.shade200),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Update Progress',
                      style: GoogleFonts.poppins(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Progress Value:',
                      style: GoogleFonts.poppins(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Slider(
                      value: _progressValue,
                      onChanged: (value) {
                        setState(() {
                          _progressValue = value;
                        });
                      },
                      min: 0.0,
                      max: 1.0,
                      divisions: 20,
                      label: '${(_progressValue * 100).toInt()}%',
                      activeColor: const Color(0xFF6A11CB),
                    ),
                    Text(
                      '${(_progressValue * 100).toInt()}%',
                      style: GoogleFonts.poppins(),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Progress Description:',
                      style: GoogleFonts.poppins(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      decoration: InputDecoration(
                        hintText: 'Describe your progress...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                      ),
                      maxLines: 3,
                      onChanged: (value) {
                        setState(() {
                          _progressDescription = value;
                        });
                      },
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isSubmitting ? null : _updateProgress,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF6A11CB),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: _isSubmitting
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                            : Text(
                                'Update Progress',
                                style: GoogleFonts.poppins(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ).animate().fadeIn(duration: 300.ms, delay: 200.ms),
          ],

          // Complete project button (only for client)
          if (isClient && status == 'accepted' && !isCompleted) ...[
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isCompleting ? null : _completeProject,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isCompleting
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        'Mark Project as Complete',
                        style: GoogleFonts.poppins(
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
              ),
            ).animate().fadeIn(duration: 300.ms, delay: 300.ms),
          ],

          // Project completed message
          if (isCompleted) ...[
            const SizedBox(height: 24),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: Column(
                children: [
                  const Icon(
                    Icons.check_circle,
                    color: Colors.green,
                    size: 48,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Project Completed',
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.green[800],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    isDeveloper
                        ? 'Payment has been processed to your Asatu wallet.'
                        : 'Thank you for completing this project!',
                    style: GoogleFonts.poppins(
                      color: Colors.green[800],
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 12),
                  if (isDeveloper && _hireRequest!['paymentProcessed'] == true) ...[
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.green[100]!),
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Project Budget:',
                                style: GoogleFonts.poppins(
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Text(
                                '\$${(_hireRequest!['budget'] as num).toStringAsFixed(2)}',
                                style: GoogleFonts.poppins(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Platform Fee (20%):',
                                style: GoogleFonts.poppins(
                                  fontWeight: FontWeight.w500,
                                  color: Colors.grey[700],
                                ),
                              ),
                              Text(
                                '-\$${((_hireRequest!['budget'] as num) * 0.2).toStringAsFixed(2)}',
                                style: GoogleFonts.poppins(
                                  color: Colors.grey[700],
                                ),
                              ),
                            ],
                          ),
                          const Divider(height: 16),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Your Earnings:',
                                style: GoogleFonts.poppins(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16,
                                ),
                              ),
                              Text(
                                '\$${((_hireRequest!['budget'] as num) * 0.7).toStringAsFixed(2)}',
                                style: GoogleFonts.poppins(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16,
                                  color: Colors.green[700],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                  if (_hireRequest!['completedAt'] != null) ...[
                    const SizedBox(height: 8),
                    Text(
                      'Completed on: ${_formatDate(_hireRequest!['completedAt'])}',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ],
              ),
            ).animate().fadeIn(duration: 300.ms, delay: 300.ms),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.poppins(),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(dynamic timestamp) {
    if (timestamp == null) return 'N/A';

    if (timestamp is Timestamp) {
      final DateTime dateTime = timestamp.toDate();
      return DateFormat('MMM d, yyyy h:mm a').format(dateTime);
    }

    return 'N/A';
  }

  String _formatRemainingTime(Duration duration) {
    if (duration.inSeconds <= 0) {
      return 'Deadline passed';
    }

    final days = duration.inDays;
    final hours = duration.inHours.remainder(24);
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (days > 0) {
      return '$days day${days > 1 ? 's' : ''} $hours hr${hours > 1 ? 's' : ''}';
    } else if (hours > 0) {
      return '$hours hr${hours > 1 ? 's' : ''} $minutes min${minutes > 1 ? 's' : ''}';
    } else if (minutes > 0) {
      return '$minutes min${minutes > 1 ? 's' : ''} $seconds sec${seconds > 1 ? 's' : ''}';
    } else {
      return '$seconds second${seconds > 1 ? 's' : ''}';
    }
  }

  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'Pending';
      case 'accepted':
        return 'In Progress';
      case 'declined':
      case 'rejected':
        return 'Declined';
      case 'completed':
        return 'Completed';
      default:
        return status;
    }
  }

  // Check if the current user is the developer for this hire request
  bool _isCurrentUserDeveloper() {
    if (_hireRequest == null || _auth.currentUser == null) return false;
    return _hireRequest!['developerId'] == _auth.currentUser!.uid;
  }



  // Show rating dialog
  void _showRatingDialog(String developerId) {
    if (!mounted) return;

    // Use a StatefulBuilder to properly handle state changes within the dialog
    showDialog(
      context: context,
      barrierDismissible: false, // User must take an action
      builder: (BuildContext dialogContext) {
        int rating = 0; // Start with no stars selected
        final TextEditingController reviewController = TextEditingController();

        return StatefulBuilder(
          builder: (context, setState) => AlertDialog(
            title: Text(
              'Rate Your Experience',
              style: GoogleFonts.poppins(
                fontWeight: FontWeight.bold,
              ),
            ),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    'How would you rate your experience with this developer?',
                    style: GoogleFonts.poppins(),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(5, (index) {
                      final int starPosition = index + 1;
                      return IconButton(
                        icon: Icon(
                          starPosition <= rating ? Icons.star : Icons.star_border,
                          color: starPosition <= rating ? const Color(0xFFFFD700) : Colors.grey[400],
                          size: 40,
                        ),
                        onPressed: () {
                          setState(() {
                            rating = starPosition;
                          });
                        },
                        padding: const EdgeInsets.symmetric(horizontal: 4),
                        constraints: const BoxConstraints(),
                      );
                    }),
                  ),
                  const SizedBox(height: 20),
                  TextField(
                    controller: reviewController,
                    maxLines: 4,
                    decoration: InputDecoration(
                      hintText: 'Write your review here...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: const EdgeInsets.all(12),
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.pop(dialogContext);
                  // Just close the dialog without submitting a rating
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('You can rate the developer later from your completed projects'),
                      duration: Duration(seconds: 3),
                    ),
                  );
                },
                child: Text(
                  'Skip',
                  style: GoogleFonts.poppins(),
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  // Validate that a rating has been selected
                  if (rating == 0) {
                    ScaffoldMessenger.of(dialogContext).showSnackBar(
                      const SnackBar(
                        content: Text('Please select a rating before submitting'),
                        duration: Duration(seconds: 2),
                      ),
                    );
                    return;
                  }

                  Navigator.pop(dialogContext);
                  _submitRating(developerId, rating, reviewController.text.trim());
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF6A11CB),
                  foregroundColor: Colors.white,
                ),
                child: Text(
                  'Submit',
                  style: GoogleFonts.poppins(),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Submit rating to Firebase
  Future<void> _submitRating(String developerId, int rating, String comment) async {
    if (_hireRequest == null) return;

    // Check if the widget is still mounted before proceeding
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      debugPrint('ProjectProgressPage - Submitting rating: $rating stars, comment: $comment');

      // Get project type from hire request
      final String projectType = _hireRequest!['planName'] ?? 'Custom Project';

      // Submit the review
      final result = await _ratingService.submitProjectReview(
        hireRequestId: widget.hireRequestId,
        developerId: developerId,
        rating: rating,
        comment: comment,
        projectType: projectType,
      );

      // Check if the widget is still mounted before updating UI
      if (!mounted) return;

      if (result['success']) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Thank you for your review!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );

        // Reload the hire request to show the review
        await _loadHireRequest();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error submitting review: ${result['error']}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } catch (e) {
      debugPrint('ProjectProgressPage - Error submitting rating: $e');

      // Check if the widget is still mounted before updating UI
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error submitting review: $e'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 5),
        ),
      );
    } finally {
      // Check if the widget is still mounted before updating state
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Show dialog to request deadline extension
  void _showExtensionRequestDialog() async {
    if (_hireRequest == null || _deadlineDate == null) return;

    // Default to 3 days extension
    final DateTime initialDate = _deadlineDate!.add(const Duration(days: 3));
    DateTime selectedDate = initialDate;
    final TextEditingController reasonController = TextEditingController();

    if (!mounted) return;

    // Show dialog to get new deadline and reason
    final bool? result = await showDialog<bool>(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Text(
            'Request Deadline Extension',
            style: GoogleFonts.poppins(
              fontWeight: FontWeight.bold,
            ),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Current deadline: ${DateFormat('MMM d, yyyy').format(_deadlineDate!)}',
                  style: GoogleFonts.poppins(),
                ),
                const SizedBox(height: 16),
                Text(
                  'New deadline:',
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                InkWell(
                  onTap: () async {
                    final DateTime? picked = await showDatePicker(
                      context: context,
                      initialDate: selectedDate,
                      firstDate: DateTime.now().add(const Duration(days: 1)),
                      lastDate: DateTime.now().add(const Duration(days: 60)),
                    );
                    if (picked != null && picked != selectedDate) {
                      setState(() {
                        selectedDate = picked;
                      });
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          DateFormat('MMM d, yyyy').format(selectedDate),
                          style: GoogleFonts.poppins(),
                        ),
                        const Icon(Icons.calendar_today, size: 20),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Reason for extension:',
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: reasonController,
                  maxLines: 3,
                  decoration: InputDecoration(
                    hintText: 'Explain why you need more time...',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(4),
                    ),
                    contentPadding: const EdgeInsets.all(12),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: Text(
                'Cancel',
                style: GoogleFonts.poppins(),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                if (reasonController.text.trim().isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Please provide a reason for the extension'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }
                Navigator.pop(context, true);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF6A11CB),
                foregroundColor: Colors.white,
              ),
              child: Text(
                'Request Extension',
                style: GoogleFonts.poppins(),
              ),
            ),
          ],
        ),
      ),
    );

    // If user confirmed, send the extension request
    if (result == true && mounted) {
      setState(() {
        _isLoading = true;
      });

      try {
        final bool success = await _deadlineService.requestDeadlineExtension(
          hireRequestId: widget.hireRequestId,
          conversationId: widget.conversation['id'],
          newDeadline: selectedDate,
          reason: reasonController.text.trim(),
        );

        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          if (success) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Extension request sent successfully'),
                backgroundColor: Colors.green,
              ),
            );

            // Reload the hire request to show the pending extension request
            _loadHireRequest();
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Failed to send extension request'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }

    // Clean up
    reasonController.dispose();
  }

  // Build the review section
  Widget _buildReviewSection() {
    if (_review == null) {
      return const SizedBox.shrink();
    }

    final int rating = (_review!['rating'] as num?)?.toInt() ?? 5;
    final String comment = _review!['comment'] ?? 'No comment provided';
    final String reviewerName = _review!['reviewerName'] ?? 'Anonymous';
    final String reviewerAvatar = _review!['reviewerAvatar'] ?? '';
    final Timestamp? timestamp = _review!['createdAt'] as Timestamp?;
    final DateTime reviewDate = timestamp?.toDate() ?? DateTime.now();

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade200),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Client Review',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundImage: reviewerAvatar.isNotEmpty
                      ? NetworkImage(reviewerAvatar)
                      : null,
                  child: reviewerAvatar.isEmpty
                      ? const Icon(Icons.person, size: 24)
                      : null,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        reviewerName,
                        style: GoogleFonts.poppins(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        DateFormat('MMM d, yyyy').format(reviewDate),
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            RatingBar(
              rating: rating.toDouble(),
              size: 20,
              color: const Color(0xFFFFD700),
              showText: true,
            ),
            const SizedBox(height: 12),
            Text(
              comment,
              style: GoogleFonts.poppins(),
            ),
          ],
        ),
      ),
    ).animate().fadeIn(duration: 300.ms, delay: 100.ms);
  }

  Widget _buildCountdownTimer() {
    if (_deadlineDate == null) {
      return const SizedBox.shrink();
    }

    final bool isExpired = _remainingTime.inSeconds <= 0;
    final Color timerColor = isExpired ? Colors.red : const Color(0xFF6A11CB);

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade200),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isExpired ? Icons.timer_off : Icons.timer,
                  color: timerColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Deadline Countdown',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    color: timerColor.withAlpha(25),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: timerColor.withAlpha(75),
                    ),
                  ),
                  child: Text(
                    _formatRemainingTime(_remainingTime),
                    style: GoogleFonts.poppins(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: timerColor,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              isExpired
                ? 'The deadline has passed. Please update the project status or request an extension.'
                : 'Time remaining until the project deadline.',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            if (_deadlineDate != null) ...[
              const SizedBox(height: 8),
              Text(
                'Deadline: ${DateFormat('MMM d, yyyy').format(_deadlineDate!)}',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),

              // Only show extension request button for developers and if deadline is approaching
              if (_isCurrentUserDeveloper() && _remainingTime.inDays <= 3 && _remainingTime.inSeconds > 0) ...[
                const SizedBox(height: 16),
                OutlinedButton.icon(
                  onPressed: _showExtensionRequestDialog,
                  icon: const Icon(Icons.timer_outlined),
                  label: Text(
                    'Request Deadline Extension',
                    style: GoogleFonts.poppins(),
                  ),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: const Color(0xFF6A11CB),
                    side: const BorderSide(color: Color(0xFF6A11CB)),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  ),
                ),
              ],
            ],
          ],
        ),
      ),
    ).animate().fadeIn(duration: 300.ms, delay: 200.ms);
  }
}
