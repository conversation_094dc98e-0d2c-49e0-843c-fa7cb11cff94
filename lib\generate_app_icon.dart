import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';

// This is a utility class to generate app icons programmatically
// Run this file separately to generate the app icon

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await createAppIcon();
  exit(0);
}

Future<void> createAppIcon() async {
  final recorder = ui.PictureRecorder();
  final canvas = Canvas(recorder);
  
  // Draw the icon
  const size = Size(1024, 1024);
  
  // Background gradient
  final bgPaint = Paint()
    ..shader = ui.Gradient.linear(
      Offset.zero,
      Offset(size.width, size.height),
      [
        const Color(0xFF6A11CB),
        const Color(0xFF2575FC),
      ],
    );
  
  canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), bgPaint);
  
  // Create the picture
  final picture = recorder.endRecording();
  final img = await picture.toImage(size.width.toInt(), size.height.toInt());
  final pngBytes = await img.toByteData(format: ui.ImageByteFormat.png);
  
  if (pngBytes != null) {
    final buffer = pngBytes.buffer.asUint8List();
    
    // Save the image to the assets/icons directory
    final file = File('assets/icons/app_icon.png');
    await file.writeAsBytes(buffer);
    
    // Also save the foreground version
    final foregroundFile = File('assets/icons/app_icon_foreground.png');
    await foregroundFile.writeAsBytes(buffer);
    
    print('App icon generated successfully!');
  } else {
    print('Failed to generate app icon');
  }
}
