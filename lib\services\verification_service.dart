import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/notification_service.dart';

/// A service class to handle user verification operations
class VerificationService {
  // Singleton pattern
  static final VerificationService _instance = VerificationService._internal();
  factory VerificationService() => _instance;
  VerificationService._internal();

  // Firebase instances
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final NotificationService _notificationService = NotificationService();

  // Verification criteria constants
  static const int _minFollowersRequired = 1000;
  static const double _minBalanceRequired = 10.0; // Minimum balance required in wallet

  // Get current user verification status
  Future<Map<String, dynamic>> getUserVerificationStatus() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      final userDoc = await _firestore.collection('users').doc(currentUser.uid).get();
      if (!userDoc.exists) {
        throw Exception('User document not found');
      }

      final userData = userDoc.data()!;

      return {
        'isVerified': userData['isVerified'] ?? false,
        'verificationStatus': userData['verificationStatus'] ?? 'unverified',
        'verificationRequestDate': userData['verificationRequestDate'],
        'verificationApprovalDate': userData['verificationApprovalDate'],
        'verificationRejectionDate': userData['verificationRejectionDate'],
        'verificationRejectionReason': userData['verificationRejectionReason'],
      };
    } catch (e) {
      debugPrint('Error getting user verification status: $e');
      rethrow;
    }
  }

  // Check if user meets verification criteria
  Future<Map<String, dynamic>> checkVerificationEligibility() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      final userDoc = await _firestore.collection('users').doc(currentUser.uid).get();
      if (!userDoc.exists) {
        throw Exception('User document not found');
      }

      final userData = userDoc.data()!;

      // Check if user already has a pending or approved verification
      final verificationStatus = userData['verificationStatus'] ?? 'unverified';
      if (verificationStatus == 'pending') {
        return {
          'eligible': false,
          'reason': 'You already have a pending verification request',
          'criteria': {
            'followersCount': userData['followersCount'] ?? 0,
            'minFollowersRequired': _minFollowersRequired,
            'balance': userData['balance'] ?? 0.0,
            'minBalanceRequired': _minBalanceRequired,
            'hasFraudHistory': userData['hasFraudHistory'] ?? false,
            'averageRating': userData['averageRating'] ?? 0.0,
          }
        };
      }

      if (verificationStatus == 'verified') {
        return {
          'eligible': false,
          'reason': 'Your account is already verified',
          'criteria': {
            'followersCount': userData['followersCount'] ?? 0,
            'minFollowersRequired': _minFollowersRequired,
            'balance': userData['balance'] ?? 0.0,
            'minBalanceRequired': _minBalanceRequired,
            'hasFraudHistory': userData['hasFraudHistory'] ?? false,
            'averageRating': userData['averageRating'] ?? 0.0,
          }
        };
      }

      // Check followers count
      final followersCount = userData['followersCount'] ?? 0;
      if (followersCount < _minFollowersRequired) {
        return {
          'eligible': false,
          'reason': 'You need at least $_minFollowersRequired followers to be verified',
          'criteria': {
            'followersCount': followersCount,
            'minFollowersRequired': _minFollowersRequired,
            'balance': userData['balance'] ?? 0.0,
            'minBalanceRequired': _minBalanceRequired,
            'hasFraudHistory': userData['hasFraudHistory'] ?? false,
            'averageRating': userData['averageRating'] ?? 0.0,
          }
        };
      }

      // Check wallet balance
      final balance = (userData['balance'] as num?)?.toDouble() ?? 0.0;
      if (balance < _minBalanceRequired) {
        return {
          'eligible': false,
          'reason': 'You need at least \$$_minBalanceRequired in your wallet to be verified',
          'criteria': {
            'followersCount': followersCount,
            'minFollowersRequired': _minFollowersRequired,
            'balance': balance,
            'minBalanceRequired': _minBalanceRequired,
            'hasFraudHistory': userData['hasFraudHistory'] ?? false,
            'averageRating': userData['averageRating'] ?? 0.0,
          }
        };
      }

      // Check fraud history
      final hasFraudHistory = userData['hasFraudHistory'] ?? false;
      if (hasFraudHistory) {
        return {
          'eligible': false,
          'reason': 'Accounts with fraud history cannot be verified',
          'criteria': {
            'followersCount': followersCount,
            'minFollowersRequired': _minFollowersRequired,
            'balance': balance,
            'minBalanceRequired': _minBalanceRequired,
            'hasFraudHistory': hasFraudHistory,
            'averageRating': userData['averageRating'] ?? 0.0,
          }
        };
      }

      // Check user rating
      final averageRating = (userData['averageRating'] as num?)?.toDouble() ?? 0.0;
      if (averageRating < 4.0 && averageRating > 0) { // Only check if user has ratings
        return {
          'eligible': false,
          'reason': 'You need an average rating of at least 4.0 to be verified',
          'criteria': {
            'followersCount': followersCount,
            'minFollowersRequired': _minFollowersRequired,
            'balance': balance,
            'minBalanceRequired': _minBalanceRequired,
            'hasFraudHistory': hasFraudHistory,
            'averageRating': averageRating,
          }
        };
      }

      // All criteria met
      return {
        'eligible': true,
        'reason': 'You are eligible for verification',
        'criteria': {
          'followersCount': followersCount,
          'minFollowersRequired': _minFollowersRequired,
          'balance': balance,
          'minBalanceRequired': _minBalanceRequired,
          'hasFraudHistory': hasFraudHistory,
          'averageRating': averageRating,
        }
      };
    } catch (e) {
      debugPrint('Error checking verification eligibility: $e');
      rethrow;
    }
  }

  // Submit verification request
  Future<void> submitVerificationRequest() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Check eligibility first
      final eligibility = await checkVerificationEligibility();
      if (eligibility['eligible'] != true) {
        throw Exception(eligibility['reason'] ?? 'You are not eligible for verification');
      }

      // Update user document with verification request
      await _firestore.collection('users').doc(currentUser.uid).update({
        'verificationStatus': 'pending',
        'verificationRequestDate': FieldValue.serverTimestamp(),
      });

      // Create verification request document
      await _firestore.collection('verification_requests').add({
        'userId': currentUser.uid,
        'userEmail': currentUser.email,
        'userName': currentUser.displayName,
        'status': 'pending',
        'requestDate': FieldValue.serverTimestamp(),
        'criteria': eligibility['criteria'],
      });
    } catch (e) {
      debugPrint('Error submitting verification request: $e');
      rethrow;
    }
  }

  // Get all verification requests (admin only)
  Future<List<Map<String, dynamic>>> getAllVerificationRequests() async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Check if user is admin
      final userDoc = await _firestore.collection('users').doc(currentUser.uid).get();
      if (!userDoc.exists || userDoc.data()?['isAdmin'] != true) {
        throw Exception('Only admins can view all verification requests');
      }

      final requestsSnapshot = await _firestore
          .collection('verification_requests')
          .orderBy('requestDate', descending: true)
          .get();

      return requestsSnapshot.docs.map((doc) {
        final data = doc.data();
        data['id'] = doc.id;
        return data;
      }).toList();
    } catch (e) {
      debugPrint('Error getting verification requests: $e');
      rethrow;
    }
  }

  // Approve verification request (admin only)
  Future<void> approveVerificationRequest(String requestId) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Check if user is admin
      final userDoc = await _firestore.collection('users').doc(currentUser.uid).get();
      if (!userDoc.exists || userDoc.data()?['isAdmin'] != true) {
        throw Exception('Only admins can approve verification requests');
      }

      // Get the request document
      final requestDoc = await _firestore.collection('verification_requests').doc(requestId).get();
      if (!requestDoc.exists) {
        throw Exception('Verification request not found');
      }

      final requestData = requestDoc.data()!;
      final userId = requestData['userId'];

      // Update the request document
      await _firestore.collection('verification_requests').doc(requestId).update({
        'status': 'approved',
        'approvedBy': currentUser.uid,
        'approvalDate': FieldValue.serverTimestamp(),
      });

      // Update the user document
      await _firestore.collection('users').doc(userId).update({
        'isVerified': true,
        'verificationStatus': 'verified',
        'verificationApprovalDate': FieldValue.serverTimestamp(),
      });

      // Send notification to user
      await _notificationService.sendNotification(
        userId: userId,
        title: 'Verification Approved',
        body: 'Congratulations! Your account has been verified.',
        type: 'verification',
        data: {'status': 'approved'},
      );
    } catch (e) {
      debugPrint('Error approving verification request: $e');
      rethrow;
    }
  }

  // Reject verification request (admin only)
  Future<void> rejectVerificationRequest(String requestId, String reason) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Check if user is admin
      final userDoc = await _firestore.collection('users').doc(currentUser.uid).get();
      if (!userDoc.exists || userDoc.data()?['isAdmin'] != true) {
        throw Exception('Only admins can reject verification requests');
      }

      // Get the request document
      final requestDoc = await _firestore.collection('verification_requests').doc(requestId).get();
      if (!requestDoc.exists) {
        throw Exception('Verification request not found');
      }

      final requestData = requestDoc.data()!;
      final userId = requestData['userId'];

      // Update the request document
      await _firestore.collection('verification_requests').doc(requestId).update({
        'status': 'rejected',
        'rejectedBy': currentUser.uid,
        'rejectionDate': FieldValue.serverTimestamp(),
        'rejectionReason': reason,
      });

      // Update the user document
      await _firestore.collection('users').doc(userId).update({
        'verificationStatus': 'rejected',
        'verificationRejectionDate': FieldValue.serverTimestamp(),
        'verificationRejectionReason': reason,
      });

      // Send notification to user
      await _notificationService.sendNotification(
        userId: userId,
        title: 'Verification Rejected',
        body: 'Your verification request has been rejected. Reason: $reason',
        type: 'verification',
        data: {'status': 'rejected', 'reason': reason},
      );
    } catch (e) {
      debugPrint('Error rejecting verification request: $e');
      rethrow;
    }
  }
}
