import 'package:flutter/material.dart';

class InteractiveRatingBar extends StatefulWidget {
  final int initialRating;
  final int maxRating;
  final double size;
  final Color activeColor;
  final Color inactiveColor;
  final Function(int) onRatingChanged;
  final MainAxisAlignment alignment;

  const InteractiveRatingBar({
    Key? key,
    this.initialRating = 0,
    this.maxRating = 5,
    this.size = 40.0,
    this.activeColor = const Color(0xFFFFD700), // Gold color
    this.inactiveColor = const Color(0xFFD3D3D3), // Light gray
    required this.onRatingChanged,
    this.alignment = MainAxisAlignment.center,
  }) : super(key: key);

  @override
  State<InteractiveRatingBar> createState() => _InteractiveRatingBarState();
}

class _InteractiveRatingBarState extends State<InteractiveRatingBar> {
  late int _rating;

  @override
  void initState() {
    super.initState();
    _rating = widget.initialRating;
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: widget.alignment,
      children: List.generate(widget.maxRating, (index) {
        final int starPosition = index + 1;
        
        return GestureDetector(
          onTap: () {
            setState(() {
              _rating = starPosition;
            });
            widget.onRatingChanged(_rating);
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4.0),
            child: Icon(
              starPosition <= _rating ? Icons.star : Icons.star_border,
              color: starPosition <= _rating ? widget.activeColor : widget.inactiveColor,
              size: widget.size,
            ),
          ),
        );
      }),
    );
  }
}
