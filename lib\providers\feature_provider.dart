import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FeatureProvider extends ChangeNotifier {
  // Feature flags
  final Map<String, bool> _featureFlags = {
    'dark_mode': true,
    'push_notifications': true,
    'in_app_messaging': true,
    'community_forums': true,
    'asset_marketplace': true,
    'mentorship_program': false,
    'live_streaming': false,
    'ai_content_suggestions': false,
  };
  Map<String, bool> get featureFlags => _featureFlags;

  // Feature descriptions
  final Map<String, String> featureDescriptions = {
    'dark_mode': 'Enable dark mode support throughout the app',
    'push_notifications': 'Enable push notifications for user activities',
    'in_app_messaging': 'Allow users to message each other within the app',
    'community_forums': 'Enable community discussion forums',
    'asset_marketplace': 'Enable buying and selling of digital assets',
    'mentorship_program': 'Enable mentorship matching and management',
    'live_streaming': 'Enable live streaming capabilities',
    'ai_content_suggestions': 'Enable AI-powered content recommendations',
  };

  // Constructor
  FeatureProvider() {
    _loadFeatureFlags();
  }

  // Load feature flags from shared preferences
  Future<void> _loadFeatureFlags() async {
    final prefs = await SharedPreferences.getInstance();
    
    final featureFlagsMap = prefs.getStringList('feature_flags_keys');
    final featureFlagsValues = prefs.getStringList('feature_flags_values');
    
    if (featureFlagsMap != null && featureFlagsValues != null && 
        featureFlagsMap.length == featureFlagsValues.length) {
      for (int i = 0; i < featureFlagsMap.length; i++) {
        _featureFlags[featureFlagsMap[i]] = featureFlagsValues[i] == 'true';
      }
    }
    
    notifyListeners();
  }

  // Save feature flags to shared preferences
  Future<void> _saveFeatureFlags() async {
    final prefs = await SharedPreferences.getInstance();
    
    await prefs.setStringList('feature_flags_keys', _featureFlags.keys.toList());
    await prefs.setStringList('feature_flags_values', 
        _featureFlags.values.map((value) => value.toString()).toList());
  }

  // Check if a feature is enabled
  bool isFeatureEnabled(String feature) {
    return _featureFlags[feature] ?? false;
  }

  // Update feature flag
  void setFeatureFlag(String feature, bool enabled) {
    _featureFlags[feature] = enabled;
    _saveFeatureFlags();
    notifyListeners();
  }

  // Add a new feature flag
  void addFeatureFlag(String feature, String description, bool enabled) {
    _featureFlags[feature] = enabled;
    featureDescriptions[feature] = description;
    _saveFeatureFlags();
    notifyListeners();
  }

  // Get all feature flags
  List<Map<String, dynamic>> getAllFeatures() {
    return _featureFlags.entries.map((entry) {
      return {
        'name': entry.key,
        'enabled': entry.value,
        'description': featureDescriptions[entry.key] ?? '',
      };
    }).toList();
  }
}
