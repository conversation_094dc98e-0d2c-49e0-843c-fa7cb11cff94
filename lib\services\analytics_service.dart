import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// Service for handling Firebase Analytics
class AnalyticsService {
  // Singleton instance
  static final AnalyticsService _instance = AnalyticsService._internal();
  factory AnalyticsService() => _instance;
  AnalyticsService._internal();

  // Firebase instances
  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get the analytics instance
  FirebaseAnalytics get analytics => _analytics;

  // Initialize the service
  Future<void> initialize() async {
    try {
      // Set analytics collection enabled (can be toggled based on user preferences)
      await _analytics.setAnalyticsCollectionEnabled(true);

      // Set user properties if user is logged in
      final user = _auth.currentUser;
      if (user != null) {
        await setUserProperties(userId: user.uid);
      }

      // Listen for auth state changes to update user properties
      _auth.authStateChanges().listen((User? user) {
        if (user != null) {
          setUserProperties(userId: user.uid);
        }
      });

      debugPrint('AnalyticsService initialized successfully');
    } catch (e) {
      debugPrint('Error initializing AnalyticsService: $e');
    }
  }

  // Set user properties for better analytics segmentation
  Future<void> setUserProperties({required String userId}) async {
    try {
      // Set user ID
      await _analytics.setUserId(id: userId);

      // Get user data from Firestore
      final userDoc = await _firestore.collection('users').doc(userId).get();
      if (userDoc.exists && userDoc.data() != null) {
        final userData = userDoc.data()!;

        // Set user properties based on Firestore data
        if (userData['username'] != null) {
          await _analytics.setUserProperty(
            name: 'username',
            value: userData['username'],
          );
        }

        if (userData['isVerified'] != null) {
          await _analytics.setUserProperty(
            name: 'is_verified',
            value: userData['isVerified'].toString(),
          );
        }

        if (userData['followersCount'] != null) {
          await _analytics.setUserProperty(
            name: 'followers_count',
            value: userData['followersCount'].toString(),
          );
        }

        // Track user type (developer, client, both)
        if (userData['userType'] != null) {
          await _analytics.setUserProperty(
            name: 'user_type',
            value: userData['userType'],
          );
        }
      }
    } catch (e) {
      debugPrint('Error setting user properties: $e');
    }
  }

  // Log custom events
  Future<void> logEvent({
    required String name,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      await _analytics.logEvent(
        name: name,
        parameters: parameters,
      );
      debugPrint('Logged event: $name with parameters: $parameters');
    } catch (e) {
      debugPrint('Error logging event: $e');
    }
  }

  // Log screen views
  Future<void> logScreenView({
    required String screenName,
    String? screenClass,
  }) async {
    try {
      await _analytics.logScreenView(
        screenName: screenName,
        screenClass: screenClass,
      );
      debugPrint('Logged screen view: $screenName');
    } catch (e) {
      debugPrint('Error logging screen view: $e');
    }
  }

  // Log user engagement
  Future<void> logUserEngagement({
    required String type,
    required String contentId,
    String? contentType,
  }) async {
    try {
      await logEvent(
        name: 'user_engagement',
        parameters: {
          'engagement_type': type,
          'content_id': contentId,
          'content_type': contentType,
        },
      );
    } catch (e) {
      debugPrint('Error logging user engagement: $e');
    }
  }

  // Log search events
  Future<void> logSearch({
    required String searchTerm,
    String? searchCategory,
  }) async {
    try {
      await _analytics.logSearch(
        searchTerm: searchTerm,
      );
      
      // Log additional search parameters
      if (searchCategory != null) {
        await logEvent(
          name: 'search_with_category',
          parameters: {
            'search_term': searchTerm,
            'search_category': searchCategory,
          },
        );
      }
    } catch (e) {
      debugPrint('Error logging search: $e');
    }
  }
}
