import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;

/// A service class to handle AI moderation of messages
class AIModerationService {
  // Singleton pattern
  static final AIModerationService _instance = AIModerationService._internal();
  factory AIModerationService() => _instance;
  AIModerationService._internal();

  // Firebase instances
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Constants
  static const String _geminiEndpoint = 'https://api.openrouter.ai/api/v1/chat/completions';
  static const String _geminiApiKey = 'sk-or-v1-0b6...330'; // Replace with the full API key in production

  // Patterns to detect fee bypass attempts
  final List<RegExp> _contactInfoPatterns = [
    // Phone numbers
    RegExp(r'\b\d{3}[-.\s]?\d{3}[-.\s]?\d{4}\b'), // US/Canada format
    RegExp(r'\b\+\d{1,3}[-.\s]?\d{1,4}[-.\s]?\d{1,4}[-.\s]?\d{1,4}\b'), // International format

    // Email addresses
    RegExp(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),

    // Social media handles
    RegExp(r'\b@[A-Za-z0-9_]{1,15}\b'), // Twitter/X
    RegExp(r'\bfb\.com/[A-Za-z0-9.]{5,}\b'), // Facebook
    RegExp(r'\binstagram\.com/[A-Za-z0-9_.]{1,30}\b'), // Instagram
    RegExp(r'\blinkedin\.com/in/[A-Za-z0-9_-]{5,30}\b'), // LinkedIn

    // Websites
    RegExp(r'\b(?:https?://)?(?:www\.)?[A-Za-z0-9-]+\.[A-Za-z]{2,}(?:/[^\s]*)?'),
  ];

  // Patterns for payment terms
  final List<RegExp> _paymentPatterns = [
    RegExp(r'\b(?:paypal|venmo|cashapp|zelle|wise|bank\s*transfer)\b', caseSensitive: false),
    RegExp(r'\b(?:payment|pay|send\s*money|transfer|transaction)\b', caseSensitive: false),
    RegExp(r'\b(?:direct|outside|external|off\s*platform)\b', caseSensitive: false),
  ];

  // Patterns for fee avoidance
  final List<RegExp> _feeAvoidancePatterns = [
    RegExp(r'\b(?:avoid|bypass|skip|no|without)\s*(?:fee|commission|charge|platform)\b', caseSensitive: false),
    RegExp(r'\b(?:save|discount|cheaper|less|lower)\s*(?:fee|commission|charge|cost|price)\b', caseSensitive: false),
    RegExp(r'\b(?:30%|thirty\s*percent|platform\s*fee)\b', caseSensitive: false),
  ];

  // Patterns for off-platform transactions
  final List<RegExp> _offPlatformPatterns = [
    RegExp(r'\b(?:meet|talk|discuss|contact|reach)\s*(?:outside|elsewhere|off|direct)\b', caseSensitive: false),
    RegExp(r'\b(?:whatsapp|telegram|signal|discord|skype)\b', caseSensitive: false),
    RegExp(r'\b(?:private|direct|personal)\s*(?:message|chat|conversation|contact)\b', caseSensitive: false),
  ];

  // Get current user ID
  String? get currentUserId => _auth.currentUser?.uid;

  // Test the Gemini API connection
  Future<bool> testGeminiConnection() async {
    try {
      final response = await http.post(
        Uri.parse(_geminiEndpoint),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_geminiApiKey',
          'HTTP-Referer': 'https://asatu.com', // Required by OpenRouter
        },
        body: jsonEncode({
          'model': 'google/gemini-2.0-flash-exp:free',
          'messages': [
            {
              'role': 'user',
              'content': 'Hello, this is a test message. Please respond with "API is working" if you receive this.'
            }
          ],
        }),
      );

      debugPrint('AIModerationService - testGeminiConnection: Status code: ${response.statusCode}');
      debugPrint('AIModerationService - testGeminiConnection: Response body: ${response.body}');

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        final String content = jsonResponse['choices'][0]['message']['content'] ?? '';
        return content.contains('API is working') || content.contains('working');
      }
      return false;
    } catch (e) {
      debugPrint('AIModerationService - testGeminiConnection: Error: $e');
      return false;
    }
  }

  // Use Gemini to moderate a message
  Future<Map<String, dynamic>?> _moderateWithGemini(String message) async {
    try {
      final response = await http.post(
        Uri.parse(_geminiEndpoint),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_geminiApiKey',
          'HTTP-Referer': 'https://asatu.com', // Required by OpenRouter
        },
        body: jsonEncode({
          'model': 'google/gemini-2.0-flash-exp:free',
          'messages': [
            {
              'role': 'system',
              'content': '''You are a content moderation AI for a freelance platform called Asatu.
              Your job is to detect messages that might be attempting to bypass the platform's 30% fee by taking transactions off-platform.
              Analyze the message and determine if it contains:
              1. Contact information (phone numbers, emails, social media, websites)
              2. Payment terms or methods (PayPal, Venmo, bank transfer, etc.)
              3. Fee avoidance language (avoid fees, save money, etc.)
              4. Suggestions to move communication off-platform (WhatsApp, Telegram, etc.)

              Respond in JSON format with these fields:
              {
                "flagged": true/false,
                "reason": "brief explanation",
                "severity": "minor"/"moderate"/"severe",
                "confidence": 0.0-1.0
              }

              Severity levels:
              - severe: Clear attempt to bypass fees with contact info and payment terms
              - moderate: Suspicious but not definitive
              - minor: Low risk but worth noting

              Only flag messages that are genuinely attempting to bypass platform fees or violate terms.'''
            },
            {
              'role': 'user',
              'content': message
            }
          ],
          'response_format': { 'type': 'json_object' }
        }),
      );

      if (response.statusCode == 200) {
        final jsonResponse = jsonDecode(response.body);
        final String content = jsonResponse['choices'][0]['message']['content'] ?? '';

        try {
          // Parse the JSON response from the content
          final Map<String, dynamic> moderationResult = jsonDecode(content);

          // Validate the result has the expected fields
          if (moderationResult.containsKey('flagged') &&
              moderationResult.containsKey('reason') &&
              moderationResult.containsKey('severity') &&
              moderationResult.containsKey('confidence')) {

            debugPrint('AIModerationService - Gemini moderation result: $moderationResult');
            return moderationResult;
          }
        } catch (parseError) {
          debugPrint('AIModerationService - Error parsing Gemini response: $parseError');
          debugPrint('AIModerationService - Raw content: $content');
        }
      }

      // If we get here, something went wrong with the API call or parsing
      debugPrint('AIModerationService - Gemini API error: ${response.statusCode} - ${response.body}');
      return null;
    } catch (e) {
      debugPrint('AIModerationService - Error using Gemini for moderation: $e');
      return null;
    }
  }

  // Moderate a message
  Future<Map<String, dynamic>> moderateMessage(String message) async {
    try {
      // Default result
      final Map<String, dynamic> result = {
        'flagged': false,
        'reason': '',
        'severity': '',
        'confidence': 0.0,
      };

      // Skip empty messages
      if (message.trim().isEmpty) {
        return result;
      }

      // First try to use Gemini for more accurate moderation
      final geminiResult = await _moderateWithGemini(message);

      if (geminiResult != null) {
        // Log the Gemini moderation result
        await _logModerationResult(message, geminiResult);
        return geminiResult;
      }

      // Fallback to pattern-based moderation if Gemini fails
      debugPrint('AIModerationService - Falling back to pattern-based moderation');

      // Check for contact information
      final bool hasContactInfo = _containsPattern(message, _contactInfoPatterns);

      // Check for payment terms
      final bool hasPaymentTerms = _containsPattern(message, _paymentPatterns);

      // Check for fee avoidance
      final bool hasFeeAvoidance = _containsPattern(message, _feeAvoidancePatterns);

      // Check for off-platform transactions
      final bool hasOffPlatform = _containsPattern(message, _offPlatformPatterns);

      // Determine if message should be flagged
      if (hasContactInfo && (hasPaymentTerms || hasFeeAvoidance || hasOffPlatform)) {
        // High severity - contact info with payment/fee avoidance/off-platform
        result['flagged'] = true;
        result['reason'] = 'Potential fee bypass with contact information';
        result['severity'] = 'severe';
        result['confidence'] = 0.9;
      } else if (hasFeeAvoidance && hasPaymentTerms) {
        // High severity - explicit fee avoidance with payment terms
        result['flagged'] = true;
        result['reason'] = 'Explicit fee avoidance discussion';
        result['severity'] = 'severe';
        result['confidence'] = 0.85;
      } else if (hasContactInfo && !hasPaymentTerms && !hasFeeAvoidance && !hasOffPlatform) {
        // Low severity - just contact info
        result['flagged'] = true;
        result['reason'] = 'Contact information shared';
        result['severity'] = 'minor';
        result['confidence'] = 0.7;
      } else if (hasOffPlatform && hasPaymentTerms) {
        // Medium severity - off-platform with payment terms
        result['flagged'] = true;
        result['reason'] = 'Discussion about off-platform payments';
        result['severity'] = 'moderate';
        result['confidence'] = 0.8;
      } else if (hasOffPlatform) {
        // Low severity - just off-platform
        result['flagged'] = true;
        result['reason'] = 'Suggestion to move conversation off-platform';
        result['severity'] = 'minor';
        result['confidence'] = 0.6;
      } else if (hasPaymentTerms && !hasFeeAvoidance) {
        // Low severity - just payment terms
        result['flagged'] = true;
        result['reason'] = 'Payment discussion';
        result['severity'] = 'minor';
        result['confidence'] = 0.5;
      }

      // Log the moderation result for training
      await _logModerationResult(message, result);

      return result;
    } catch (e) {
      debugPrint('Error moderating message: $e');
      // Return safe default in case of error
      return {
        'flagged': false,
        'reason': '',
        'severity': '',
        'confidence': 0.0,
      };
    }
  }

  // Check if text contains any of the patterns
  bool _containsPattern(String text, List<RegExp> patterns) {
    for (final pattern in patterns) {
      if (pattern.hasMatch(text)) {
        return true;
      }
    }
    return false;
  }

  // Log moderation result for training and improvement
  Future<void> _logModerationResult(String message, Map<String, dynamic> result) async {
    try {
      if (currentUserId == null) return;

      await _firestore.collection('moderation_logs').add({
        'message': message,
        'flagged': result['flagged'],
        'reason': result['reason'],
        'severity': result['severity'],
        'confidence': result['confidence'],
        'timestamp': FieldValue.serverTimestamp(),
        'userId': currentUserId,
        'adminReviewed': false,
        'adminFeedback': null,
      });
    } catch (e) {
      debugPrint('Error logging moderation result: $e');
    }
  }

  // Update moderation model with admin feedback
  Future<void> updateWithAdminFeedback(
    String logId,
    bool isFalsePositive,
    String? correctSeverity,
  ) async {
    try {
      final Map<String, dynamic> updateData = {
        'adminReviewed': true,
        'adminFeedback': isFalsePositive ? 'false_positive' : 'true_positive',
        'adminReviewTimestamp': FieldValue.serverTimestamp(),
        'adminId': currentUserId,
      };

      if (correctSeverity != null) {
        updateData['correctSeverity'] = correctSeverity;
      }

      await _firestore.collection('moderation_logs').doc(logId).update(updateData);

      // In a real implementation, this feedback would be used to retrain the model
      // For now, we just log it for future improvements
    } catch (e) {
      debugPrint('Error updating with admin feedback: $e');
    }
  }

  // Get moderation statistics
  Future<Map<String, dynamic>> getModerationStats() async {
    try {
      // Get total moderation logs
      final totalSnapshot = await _firestore
          .collection('moderation_logs')
          .count()
          .get();

      // Get flagged messages
      final flaggedSnapshot = await _firestore
          .collection('moderation_logs')
          .where('flagged', isEqualTo: true)
          .count()
          .get();

      // Get admin reviewed logs
      final reviewedSnapshot = await _firestore
          .collection('moderation_logs')
          .where('adminReviewed', isEqualTo: true)
          .count()
          .get();

      // Get false positives
      final falsePositiveSnapshot = await _firestore
          .collection('moderation_logs')
          .where('adminFeedback', isEqualTo: 'false_positive')
          .count()
          .get();

      // Calculate accuracy
      final double accuracy = (reviewedSnapshot.count ?? 0) > 0
          ? ((reviewedSnapshot.count ?? 0) - (falsePositiveSnapshot.count ?? 0)) / (reviewedSnapshot.count ?? 1)
          : 0.0;

      return {
        'totalMessages': totalSnapshot.count ?? 0,
        'flaggedMessages': flaggedSnapshot.count ?? 0,
        'reviewedMessages': reviewedSnapshot.count ?? 0,
        'falsePositives': falsePositiveSnapshot.count ?? 0,
        'accuracy': accuracy,
      };
    } catch (e) {
      debugPrint('Error getting moderation stats: $e');
      return {
        'totalMessages': 0,
        'flaggedMessages': 0,
        'reviewedMessages': 0,
        'falsePositives': 0,
        'accuracy': 0.0,
      };
    }
  }
}
