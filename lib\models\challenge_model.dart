import 'package:cloud_firestore/cloud_firestore.dart';

class ChallengeModel {
  final String id;
  final String title;
  final String description;
  final String category;
  final String creatorId;
  final String creatorName;
  final String creatorAvatar;
  final double prizeAmount;
  final String status; // 'active', 'completed', 'cancelled'
  final DateTime createdAt;
  final DateTime deadline;
  final List<String> requirements;
  final List<String> tags;
  final int submissionCount;
  final String? winnerId;
  final String? winnerName;
  final DateTime? completedAt;
  final Map<String, dynamic>? additionalData;

  ChallengeModel({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.creatorId,
    required this.creatorName,
    required this.creatorAvatar,
    required this.prizeAmount,
    required this.status,
    required this.createdAt,
    required this.deadline,
    required this.requirements,
    required this.tags,
    this.submissionCount = 0,
    this.winnerId,
    this.winnerName,
    this.completedAt,
    this.additionalData,
  });

  // Create from Firestore document
  factory ChallengeModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ChallengeModel(
      id: doc.id,
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      category: data['category'] ?? '',
      creatorId: data['creatorId'] ?? '',
      creatorName: data['creatorName'] ?? '',
      creatorAvatar: data['creatorAvatar'] ?? '',
      prizeAmount: (data['prizeAmount'] as num?)?.toDouble() ?? 0.0,
      status: data['status'] ?? 'active',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      deadline: (data['deadline'] as Timestamp?)?.toDate() ?? DateTime.now(),
      requirements: List<String>.from(data['requirements'] ?? []),
      tags: List<String>.from(data['tags'] ?? []),
      submissionCount: data['submissionCount'] ?? 0,
      winnerId: data['winnerId'],
      winnerName: data['winnerName'],
      completedAt: (data['completedAt'] as Timestamp?)?.toDate(),
      additionalData: data['additionalData'],
    );
  }

  // Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'description': description,
      'category': category,
      'creatorId': creatorId,
      'creatorName': creatorName,
      'creatorAvatar': creatorAvatar,
      'prizeAmount': prizeAmount,
      'status': status,
      'createdAt': Timestamp.fromDate(createdAt),
      'deadline': Timestamp.fromDate(deadline),
      'requirements': requirements,
      'tags': tags,
      'submissionCount': submissionCount,
      'winnerId': winnerId,
      'winnerName': winnerName,
      'completedAt': completedAt != null ? Timestamp.fromDate(completedAt!) : null,
      'additionalData': additionalData,
    };
  }

  // Get days remaining
  int get daysRemaining {
    final now = DateTime.now();
    if (deadline.isBefore(now)) return 0;
    return deadline.difference(now).inDays;
  }

  // Check if challenge is expired
  bool get isExpired => DateTime.now().isAfter(deadline);

  // Check if challenge is active
  bool get isActive => status == 'active' && !isExpired;

  // Copy with method
  ChallengeModel copyWith({
    String? id,
    String? title,
    String? description,
    String? category,
    String? creatorId,
    String? creatorName,
    String? creatorAvatar,
    double? prizeAmount,
    String? status,
    DateTime? createdAt,
    DateTime? deadline,
    List<String>? requirements,
    List<String>? tags,
    int? submissionCount,
    String? winnerId,
    String? winnerName,
    DateTime? completedAt,
    Map<String, dynamic>? additionalData,
  }) {
    return ChallengeModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      category: category ?? this.category,
      creatorId: creatorId ?? this.creatorId,
      creatorName: creatorName ?? this.creatorName,
      creatorAvatar: creatorAvatar ?? this.creatorAvatar,
      prizeAmount: prizeAmount ?? this.prizeAmount,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      deadline: deadline ?? this.deadline,
      requirements: requirements ?? this.requirements,
      tags: tags ?? this.tags,
      submissionCount: submissionCount ?? this.submissionCount,
      winnerId: winnerId ?? this.winnerId,
      winnerName: winnerName ?? this.winnerName,
      completedAt: completedAt ?? this.completedAt,
      additionalData: additionalData ?? this.additionalData,
    );
  }
}

class ChallengeSubmissionModel {
  final String id;
  final String challengeId;
  final String participantId;
  final String participantName;
  final String participantAvatar;
  final String title;
  final String description;
  final List<String> attachmentUrls;
  final List<String> imageUrls;
  final String? codeUrl;
  final String? demoUrl;
  final DateTime submittedAt;
  final bool isWinner;
  final int votes;
  final int voteCount;
  final int? rank;
  final double? prizeAmount;
  final List<String> voterIds;
  final Map<String, dynamic>? additionalData;

  ChallengeSubmissionModel({
    required this.id,
    required this.challengeId,
    required this.participantId,
    required this.participantName,
    required this.participantAvatar,
    required this.title,
    required this.description,
    required this.attachmentUrls,
    required this.imageUrls,
    this.codeUrl,
    this.demoUrl,
    required this.submittedAt,
    this.isWinner = false,
    this.votes = 0,
    this.voteCount = 0,
    this.rank,
    this.prizeAmount,
    required this.voterIds,
    this.additionalData,
  });

  // Create from Firestore document
  factory ChallengeSubmissionModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ChallengeSubmissionModel(
      id: doc.id,
      challengeId: data['challengeId'] ?? '',
      participantId: data['participantId'] ?? '',
      participantName: data['participantName'] ?? '',
      participantAvatar: data['participantAvatar'] ?? '',
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      attachmentUrls: List<String>.from(data['attachmentUrls'] ?? []),
      imageUrls: List<String>.from(data['imageUrls'] ?? []),
      codeUrl: data['codeUrl'],
      demoUrl: data['demoUrl'],
      submittedAt: (data['submittedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isWinner: data['isWinner'] ?? false,
      votes: data['votes'] ?? 0,
      voteCount: data['voteCount'] ?? 0,
      rank: data['rank'],
      prizeAmount: (data['prizeAmount'] as num?)?.toDouble(),
      voterIds: List<String>.from(data['voterIds'] ?? []),
      additionalData: data['additionalData'],
    );
  }

  // Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'challengeId': challengeId,
      'participantId': participantId,
      'participantName': participantName,
      'participantAvatar': participantAvatar,
      'title': title,
      'description': description,
      'attachmentUrls': attachmentUrls,
      'imageUrls': imageUrls,
      'codeUrl': codeUrl,
      'demoUrl': demoUrl,
      'submittedAt': Timestamp.fromDate(submittedAt),
      'isWinner': isWinner,
      'votes': votes,
      'voteCount': voteCount,
      'rank': rank,
      'prizeAmount': prizeAmount,
      'voterIds': voterIds,
      'additionalData': additionalData,
    };
  }

  // Copy with method
  ChallengeSubmissionModel copyWith({
    String? id,
    String? challengeId,
    String? participantId,
    String? participantName,
    String? participantAvatar,
    String? title,
    String? description,
    List<String>? attachmentUrls,
    List<String>? imageUrls,
    String? codeUrl,
    String? demoUrl,
    DateTime? submittedAt,
    bool? isWinner,
    int? votes,
    int? voteCount,
    int? rank,
    double? prizeAmount,
    List<String>? voterIds,
    Map<String, dynamic>? additionalData,
  }) {
    return ChallengeSubmissionModel(
      id: id ?? this.id,
      challengeId: challengeId ?? this.challengeId,
      participantId: participantId ?? this.participantId,
      participantName: participantName ?? this.participantName,
      participantAvatar: participantAvatar ?? this.participantAvatar,
      title: title ?? this.title,
      description: description ?? this.description,
      attachmentUrls: attachmentUrls ?? this.attachmentUrls,
      imageUrls: imageUrls ?? this.imageUrls,
      codeUrl: codeUrl ?? this.codeUrl,
      demoUrl: demoUrl ?? this.demoUrl,
      submittedAt: submittedAt ?? this.submittedAt,
      isWinner: isWinner ?? this.isWinner,
      votes: votes ?? this.votes,
      voteCount: voteCount ?? this.voteCount,
      rank: rank ?? this.rank,
      prizeAmount: prizeAmount ?? this.prizeAmount,
      voterIds: voterIds ?? this.voterIds,
      additionalData: additionalData ?? this.additionalData,
    );
  }
}
