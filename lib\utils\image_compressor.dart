import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path/path.dart' as path;

/// Utility class for compressing images
class ImageCompressor {
  /// Compresses an image file with the specified quality
  /// Returns the compressed image file
  /// Uses aggressive compression to significantly reduce file size while maintaining acceptable quality
  static Future<File> compressImage(
    File file, {
    int quality = 50, // Lower default quality for better compression (reduced from 60)
    int? minWidth,
    int? minHeight,
    bool aggressive = false, // Option for even more aggressive compression
  }) async {
    try {
      final String dir = path.dirname(file.path);
      final String fileName = path.basename(file.path);
      final String targetPath = '$dir/compressed_$fileName';

      // Get file extension
      final String ext = path.extension(file.path).toLowerCase();

      // Determine compression quality based on file size
      final int fileSize = await file.length();
      int adjustedQuality = quality;

      // For larger files, use more aggressive compression
      if (fileSize > 5 * 1024 * 1024) { // > 5MB
        adjustedQuality = aggressive ? 30 : 40; // More aggressive (was 40/50)
      } else if (fileSize > 2 * 1024 * 1024) { // > 2MB
        adjustedQuality = aggressive ? 35 : 45; // More aggressive (was 50/60)
      } else if (fileSize > 1 * 1024 * 1024) { // > 1MB
        adjustedQuality = aggressive ? 40 : 50; // More aggressive (was 55/65)
      } else {
        // Even for smaller files, be more aggressive
        adjustedQuality = aggressive ? 45 : 55; // More aggressive for all files
      }

      debugPrint('ImageCompressor - Original file size: ${(fileSize / 1024).toStringAsFixed(2)} KB, using quality: $adjustedQuality');

      // Compress based on file type
      File? result;

      if (ext == '.jpg' || ext == '.jpeg') {
        result = await _compressJpg(
          file,
          targetPath,
          quality: adjustedQuality,
          minWidth: minWidth,
          minHeight: minHeight,
        );
      } else if (ext == '.png') {
        result = await _compressPng(
          file,
          targetPath,
          quality: adjustedQuality,
          minWidth: minWidth,
          minHeight: minHeight,
        );
      } else if (ext == '.heic' || ext == '.heif') {
        // Convert HEIC to JPEG and compress
        result = await _compressJpg(
          file,
          targetPath.replaceAll(ext, '.jpg'),
          quality: adjustedQuality,
          minWidth: minWidth,
          minHeight: minHeight,
        );
      } else {
        // For unsupported formats, return the original file
        return file;
      }

      if (result != null) {
        final int compressedSize = await result.length();
        final double compressionRatio = fileSize / compressedSize;
        debugPrint('ImageCompressor - Compressed file size: ${(compressedSize / 1024).toStringAsFixed(2)} KB, ratio: ${compressionRatio.toStringAsFixed(2)}x');

        // If compression wasn't effective enough, try again with more aggressive settings
        if (compressionRatio < 1.5 && !aggressive && fileSize > 1 * 1024 * 1024) {
          debugPrint('ImageCompressor - Compression not effective enough, trying more aggressive compression');
          return compressImage(file, quality: adjustedQuality - 20, minWidth: minWidth, minHeight: minHeight, aggressive: true);
        }
      }

      return result ?? file;
    } catch (e) {
      debugPrint('Error compressing image: $e');
      // If compression fails, return the original file
      return file;
    }
  }

  /// Compresses multiple images with the specified quality
  /// Returns a list of compressed image files
  static Future<List<File>> compressImages(
    List<File> files, {
    int quality = 80,
    int? minWidth,
    int? minHeight,
  }) async {
    try {
      final List<File> compressedFiles = [];

      for (final file in files) {
        final compressedFile = await compressImage(
          file,
          quality: quality,
          minWidth: minWidth,
          minHeight: minHeight,
        );
        compressedFiles.add(compressedFile);
      }

      return compressedFiles;
    } catch (e) {
      debugPrint('Error compressing multiple images: $e');
      // If compression fails, return the original files
      return files;
    }
  }

  /// Compresses a JPEG image
  static Future<File?> _compressJpg(
    File file,
    String targetPath, {
    int quality = 80,
    int? minWidth,
    int? minHeight,
  }) async {
    final result = await FlutterImageCompress.compressAndGetFile(
      file.absolute.path,
      targetPath,
      quality: quality,
      minWidth: minWidth ?? 1080, // Default to 1080p if not specified
      minHeight: minHeight ?? 1920,
      format: CompressFormat.jpeg,
    );

    return result != null ? File(result.path) : null;
  }

  /// Compresses a PNG image
  static Future<File?> _compressPng(
    File file,
    String targetPath, {
    int quality = 80,
    int? minWidth,
    int? minHeight,
  }) async {
    final result = await FlutterImageCompress.compressAndGetFile(
      file.absolute.path,
      targetPath,
      quality: quality,
      minWidth: minWidth ?? 1080, // Default to 1080p if not specified
      minHeight: minHeight ?? 1920,
      format: CompressFormat.png,
    );

    return result != null ? File(result.path) : null;
  }

  /// Calculates the compression ratio between original and compressed files
  static Future<double> getCompressionRatio(File original, File compressed) async {
    final int originalSize = await original.length();
    final int compressedSize = await compressed.length();

    if (originalSize == 0) return 1.0;
    return originalSize / compressedSize;
  }

  /// Checks if an image needs compression based on file size threshold
  static Future<bool> needsCompression(File file, {int thresholdKB = 500}) async {
    final int fileSize = await file.length();
    return fileSize > thresholdKB * 1024; // Convert KB to bytes
  }
}
