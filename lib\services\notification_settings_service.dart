import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service to handle notification settings throughout the app
class NotificationSettingsService extends ChangeNotifier {
  // Singleton instance
  static final NotificationSettingsService _instance = NotificationSettingsService._internal();
  factory NotificationSettingsService() => _instance;
  NotificationSettingsService._internal();

  // Firebase instances
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Notification settings
  bool _newMessages = true;
  bool _newFollowers = true;
  bool _assetApprovals = true;
  bool _transactionUpdates = true;
  bool _marketingNotifications = false;
  bool _appUpdates = true;
  bool _hirePlanRequests = true;
  bool _communityUpdates = true;

  // Getters
  bool get newMessages => _newMessages;
  bool get newFollowers => _newFollowers;
  bool get assetApprovals => _assetApprovals;
  bool get transactionUpdates => _transactionUpdates;
  bool get marketingNotifications => _marketingNotifications;
  bool get appUpdates => _appUpdates;
  bool get hirePlanRequests => _hirePlanRequests;
  bool get communityUpdates => _communityUpdates;

  // Initialize the service
  Future<void> initialize() async {
    try {
      // First try to get settings from SharedPreferences for quick loading
      final prefs = await SharedPreferences.getInstance();
      
      // Load settings from SharedPreferences with defaults
      _newMessages = prefs.getBool('notification_new_messages') ?? true;
      _newFollowers = prefs.getBool('notification_new_followers') ?? true;
      _assetApprovals = prefs.getBool('notification_asset_approvals') ?? true;
      _transactionUpdates = prefs.getBool('notification_transaction_updates') ?? true;
      _marketingNotifications = prefs.getBool('notification_marketing') ?? false;
      _appUpdates = prefs.getBool('notification_app_updates') ?? true;
      _hirePlanRequests = prefs.getBool('notification_hire_plan_requests') ?? true;
      _communityUpdates = prefs.getBool('notification_community_updates') ?? true;
      
      // If user is logged in, try to get their settings from Firestore
      final currentUser = _auth.currentUser;
      if (currentUser != null) {
        try {
          final userDoc = await _firestore.collection('users').doc(currentUser.uid).get();
          if (userDoc.exists) {
            final userData = userDoc.data();
            if (userData != null && userData.containsKey('notificationSettings')) {
              final settings = userData['notificationSettings'] as Map<String, dynamic>;
              
              // Update settings from Firestore
              _newMessages = settings['newMessages'] ?? _newMessages;
              _newFollowers = settings['newFollowers'] ?? _newFollowers;
              _assetApprovals = settings['assetApprovals'] ?? _assetApprovals;
              _transactionUpdates = settings['transactionUpdates'] ?? _transactionUpdates;
              _marketingNotifications = settings['marketingNotifications'] ?? _marketingNotifications;
              _appUpdates = settings['appUpdates'] ?? _appUpdates;
              _hirePlanRequests = settings['hirePlanRequests'] ?? _hirePlanRequests;
              _communityUpdates = settings['communityUpdates'] ?? _communityUpdates;
              
              // Save to SharedPreferences for faster loading next time
              await _saveToSharedPreferences();
            }
          }
        } catch (e) {
          debugPrint('Error loading notification settings from Firestore: $e');
        }
      }
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error initializing notification settings service: $e');
    }
  }

  // Save settings to SharedPreferences
  Future<void> _saveToSharedPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('notification_new_messages', _newMessages);
      await prefs.setBool('notification_new_followers', _newFollowers);
      await prefs.setBool('notification_asset_approvals', _assetApprovals);
      await prefs.setBool('notification_transaction_updates', _transactionUpdates);
      await prefs.setBool('notification_marketing', _marketingNotifications);
      await prefs.setBool('notification_app_updates', _appUpdates);
      await prefs.setBool('notification_hire_plan_requests', _hirePlanRequests);
      await prefs.setBool('notification_community_updates', _communityUpdates);
    } catch (e) {
      debugPrint('Error saving notification settings to SharedPreferences: $e');
    }
  }

  // Update notification settings
  Future<void> updateSettings({
    bool? newMessages,
    bool? newFollowers,
    bool? assetApprovals,
    bool? transactionUpdates,
    bool? marketingNotifications,
    bool? appUpdates,
    bool? hirePlanRequests,
    bool? communityUpdates,
  }) async {
    // Update local settings
    if (newMessages != null) _newMessages = newMessages;
    if (newFollowers != null) _newFollowers = newFollowers;
    if (assetApprovals != null) _assetApprovals = assetApprovals;
    if (transactionUpdates != null) _transactionUpdates = transactionUpdates;
    if (marketingNotifications != null) _marketingNotifications = marketingNotifications;
    if (appUpdates != null) _appUpdates = appUpdates;
    if (hirePlanRequests != null) _hirePlanRequests = hirePlanRequests;
    if (communityUpdates != null) _communityUpdates = communityUpdates;
    
    try {
      // Save to SharedPreferences
      await _saveToSharedPreferences();
      
      // If user is logged in, update Firestore
      final currentUser = _auth.currentUser;
      if (currentUser != null) {
        await _firestore.collection('users').doc(currentUser.uid).update({
          'notificationSettings': {
            'newMessages': _newMessages,
            'newFollowers': _newFollowers,
            'assetApprovals': _assetApprovals,
            'transactionUpdates': _transactionUpdates,
            'marketingNotifications': _marketingNotifications,
            'appUpdates': _appUpdates,
            'hirePlanRequests': _hirePlanRequests,
            'communityUpdates': _communityUpdates,
          },
          'updatedAt': FieldValue.serverTimestamp(),
        });
      }
      
      notifyListeners();
    } catch (e) {
      debugPrint('Error updating notification settings: $e');
      rethrow;
    }
  }
}
