import 'package:cloud_firestore/cloud_firestore.dart';

/// User model class to handle user data
class UserModel {
  final String uid;
  final String email;
  final String displayName;
  final String? photoURL;
  final String? username;
  final String? bio;
  final String? location;
  final List<String> interests;
  final List<Map<String, dynamic>> skills;
  final bool emailVerified;
  final bool profileSetupCompleted;
  final bool isAdmin;
  final bool isBanned;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime lastLogin;
  final int followersCount;
  final int followingCount;
  final int assetsCount;
  final int downloadsCount;
  final double rating;
  final double balance;
  final double totalEarnings;
  final double pendingWithdrawals;
  final Map<String, dynamic>? notificationSettings;
  final String theme;

  UserModel({
    required this.uid,
    required this.email,
    required this.displayName,
    this.photoURL,
    this.username,
    this.bio,
    this.location,
    this.interests = const [],
    this.skills = const [],
    this.emailVerified = false,
    this.profileSetupCompleted = false,
    this.isAdmin = false,
    this.isBanned = false,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastLogin,
    this.followersCount = 0,
    this.followingCount = 0,
    this.assetsCount = 0,
    this.downloadsCount = 0,
    this.rating = 0.0,
    this.balance = 0.0,
    this.totalEarnings = 0.0,
    this.pendingWithdrawals = 0.0,
    this.notificationSettings,
    this.theme = 'system',
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now(),
        lastLogin = lastLogin ?? DateTime.now();

  // Create a UserModel from a Firestore document
  factory UserModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>? ?? {};

    // Parse timestamps
    final Timestamp createdAtTimestamp = data['createdAt'] as Timestamp? ??
        Timestamp.fromDate(DateTime.now());
    final Timestamp updatedAtTimestamp = data['updatedAt'] as Timestamp? ??
        Timestamp.fromDate(DateTime.now());
    final Timestamp lastLoginTimestamp = data['lastLogin'] as Timestamp? ??
        Timestamp.fromDate(DateTime.now());

    // Parse interests
    List<String> interests = [];
    if (data['interests'] != null) {
      interests = List<String>.from(data['interests'] as List? ?? []);
    }

    // Parse skills
    List<Map<String, dynamic>> skills = [];
    if (data['skills'] != null) {
      skills = List<Map<String, dynamic>>.from(
        (data['skills'] as List? ?? []).map((skill) =>
          skill is Map<String, dynamic> ? skill : <String, dynamic>{}
        ),
      );
    }

    return UserModel(
      uid: doc.id,
      email: data['email'] as String? ?? '',
      displayName: data['displayName'] as String? ?? '',
      photoURL: data['photoURL'] as String?,
      username: data['username'] as String?,
      bio: data['bio'] as String?,
      location: data['location'] as String?,
      interests: interests,
      skills: skills,
      emailVerified: data['emailVerified'] as bool? ?? false,
      profileSetupCompleted: data['profileSetupCompleted'] as bool? ?? false,
      isAdmin: data['isAdmin'] as bool? ?? false,
      isBanned: data['isBanned'] as bool? ?? false,
      createdAt: createdAtTimestamp.toDate(),
      updatedAt: updatedAtTimestamp.toDate(),
      lastLogin: lastLoginTimestamp.toDate(),
      followersCount: data['followersCount'] as int? ?? 0,
      followingCount: data['followingCount'] as int? ?? 0,
      assetsCount: data['assetsCount'] as int? ?? 0,
      downloadsCount: data['downloadsCount'] as int? ?? 0,
      rating: (data['rating'] as num?)?.toDouble() ?? 0.0,
      balance: (data['balance'] as num?)?.toDouble() ?? 0.0,
      totalEarnings: (data['totalEarnings'] as num?)?.toDouble() ?? 0.0,
      pendingWithdrawals: (data['pendingWithdrawals'] as num?)?.toDouble() ?? 0.0,
      notificationSettings: data['notificationSettings'] as Map<String, dynamic>?,
      theme: data['theme'] as String? ?? 'system',
    );
  }

  // Create a UserModel from a Firebase User and additional data
  factory UserModel.fromFirebaseUser(
    Map<String, dynamic> userData,
    Map<String, dynamic>? profileData,
  ) {
    // Parse timestamps
    final Timestamp createdAtTimestamp = userData['createdAt'] as Timestamp? ??
        Timestamp.fromDate(DateTime.now());
    final Timestamp updatedAtTimestamp = userData['updatedAt'] as Timestamp? ??
        Timestamp.fromDate(DateTime.now());
    final Timestamp lastLoginTimestamp = userData['lastLogin'] as Timestamp? ??
        Timestamp.fromDate(DateTime.now());

    // Parse interests
    List<String> interests = [];
    if (profileData != null && profileData['interests'] != null) {
      interests = List<String>.from(profileData['interests'] as List? ?? []);
    }

    // Parse skills
    List<Map<String, dynamic>> skills = [];
    if (profileData != null && profileData['skills'] != null) {
      skills = List<Map<String, dynamic>>.from(
        (profileData['skills'] as List? ?? []).map((skill) =>
          skill is Map<String, dynamic> ? skill : <String, dynamic>{}
        ),
      );
    }

    return UserModel(
      uid: userData['uid'] as String? ?? '',
      email: userData['email'] as String? ?? '',
      displayName: userData['displayName'] as String? ?? '',
      photoURL: userData['photoURL'] as String?,
      username: userData['username'] as String?,
      bio: profileData?['bio'] as String?,
      location: profileData?['location'] as String?,
      interests: interests,
      skills: skills,
      emailVerified: userData['emailVerified'] as bool? ?? false,
      profileSetupCompleted: userData['profileSetupCompleted'] as bool? ?? false,
      isAdmin: userData['isAdmin'] as bool? ?? false,
      isBanned: userData['isBanned'] as bool? ?? false,
      createdAt: createdAtTimestamp.toDate(),
      updatedAt: updatedAtTimestamp.toDate(),
      lastLogin: lastLoginTimestamp.toDate(),
      followersCount: userData['followersCount'] as int? ?? 0,
      followingCount: userData['followingCount'] as int? ?? 0,
      assetsCount: userData['assetsCount'] as int? ?? 0,
      downloadsCount: userData['downloadsCount'] as int? ?? 0,
      rating: (userData['rating'] as num?)?.toDouble() ?? 0.0,
      balance: (userData['balance'] as num?)?.toDouble() ?? 0.0,
      totalEarnings: (userData['totalEarnings'] as num?)?.toDouble() ?? 0.0,
      pendingWithdrawals: (userData['pendingWithdrawals'] as num?)?.toDouble() ?? 0.0,
      notificationSettings: userData['notificationSettings'] as Map<String, dynamic>?,
      theme: userData['theme'] as String? ?? 'system',
    );
  }

  // Convert UserModel to a Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'uid': uid,
      'email': email,
      'displayName': displayName,
      'photoURL': photoURL,
      'username': username,
      'bio': bio,
      'location': location,
      'interests': interests,
      'skills': skills,
      'emailVerified': emailVerified,
      'profileSetupCompleted': profileSetupCompleted,
      'isAdmin': isAdmin,
      'isBanned': isBanned,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(DateTime.now()),
      'lastLogin': Timestamp.fromDate(lastLogin),
      'followersCount': followersCount,
      'followingCount': followingCount,
      'assetsCount': assetsCount,
      'downloadsCount': downloadsCount,
      'rating': rating,
      'balance': balance,
      'totalEarnings': totalEarnings,
      'pendingWithdrawals': pendingWithdrawals,
      'notificationSettings': notificationSettings,
      'theme': theme,
    };
  }

  // Create a copy of UserModel with updated fields
  UserModel copyWith({
    String? uid,
    String? email,
    String? displayName,
    String? photoURL,
    String? username,
    String? bio,
    String? location,
    List<String>? interests,
    List<Map<String, dynamic>>? skills,
    bool? emailVerified,
    bool? profileSetupCompleted,
    bool? isAdmin,
    bool? isBanned,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastLogin,
    int? followersCount,
    int? followingCount,
    int? assetsCount,
    int? downloadsCount,
    double? rating,
    double? balance,
    double? totalEarnings,
    double? pendingWithdrawals,
    Map<String, dynamic>? notificationSettings,
    String? theme,
  }) {
    return UserModel(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoURL: photoURL ?? this.photoURL,
      username: username ?? this.username,
      bio: bio ?? this.bio,
      location: location ?? this.location,
      interests: interests ?? this.interests,
      skills: skills ?? this.skills,
      emailVerified: emailVerified ?? this.emailVerified,
      profileSetupCompleted: profileSetupCompleted ?? this.profileSetupCompleted,
      isAdmin: isAdmin ?? this.isAdmin,
      isBanned: isBanned ?? this.isBanned,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastLogin: lastLogin ?? this.lastLogin,
      followersCount: followersCount ?? this.followersCount,
      followingCount: followingCount ?? this.followingCount,
      assetsCount: assetsCount ?? this.assetsCount,
      downloadsCount: downloadsCount ?? this.downloadsCount,
      rating: rating ?? this.rating,
      balance: balance ?? this.balance,
      totalEarnings: totalEarnings ?? this.totalEarnings,
      pendingWithdrawals: pendingWithdrawals ?? this.pendingWithdrawals,
      notificationSettings: notificationSettings ?? this.notificationSettings,
      theme: theme ?? this.theme,
    );
  }
}
