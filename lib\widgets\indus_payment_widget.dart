import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../services/payment_migration_service.dart';

/// Widget for handling payments via Indus AppStore In-App Purchases
class IndusPaymentWidget extends StatefulWidget {
  final String paymentType; // 'wallet_topup', 'asset_purchase', 'premium'
  final double? amount; // For asset purchases
  final String? assetId; // For asset purchases
  final Function(Map<String, dynamic>)? onPaymentComplete;
  final Function(String)? onPaymentError;

  const IndusPaymentWidget({
    super.key,
    required this.paymentType,
    this.amount,
    this.assetId,
    this.onPaymentComplete,
    this.onPaymentError,
  });

  @override
  State<IndusPaymentWidget> createState() => _IndusPaymentWidgetState();
}

class _IndusPaymentWidgetState extends State<IndusPaymentWidget> {
  final PaymentMigrationService _paymentService = PaymentMigrationService();
  bool _isLoading = false;
  List<Map<String, dynamic>> _paymentOptions = [];

  @override
  void initState() {
    super.initState();
    _loadPaymentOptions();
  }

  void _loadPaymentOptions() {
    setState(() {
      if (widget.paymentType == 'wallet_topup') {
        _paymentOptions = _paymentService.getWalletTopUpOptions();
      } else {
        // For asset purchases, show payment method options
        _paymentOptions = [
          {
            'id': 'wallet',
            'title': 'Pay with Wallet',
            'description': 'Use your Asatu wallet balance',
            'icon': Icons.account_balance_wallet,
            'method': 'wallet',
          },
          if (_paymentService.isIAPAvailable) {
            'id': 'iap',
            'title': 'In-App Purchase',
            'description': 'Pay via Indus AppStore',
            'icon': Icons.payment,
            'method': 'iap',
          },
          {
            'id': 'stripe',
            'title': 'Credit/Debit Card',
            'description': 'Pay with card (legacy)',
            'icon': Icons.credit_card,
            'method': 'stripe',
          },
        ];
      }
    });
  }

  Future<void> _processPayment(Map<String, dynamic> option) async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      Map<String, dynamic> result;

      if (widget.paymentType == 'wallet_topup') {
        final amount = option['amount'] as double;
        result = await _paymentService.processWalletTopUp(amount);
      } else if (widget.paymentType == 'asset_purchase') {
        if (widget.amount == null || widget.assetId == null) {
          throw Exception('Amount and asset ID are required for asset purchases');
        }
        
        result = await _paymentService.processAssetPurchase(
          assetId: widget.assetId!,
          price: widget.amount!,
          paymentMethod: option['method'],
        );
      } else {
        throw Exception('Unsupported payment type: ${widget.paymentType}');
      }

      if (result['success'] == true) {
        widget.onPaymentComplete?.call(result);
        if (mounted) {
          _showSuccessMessage(result['message'] ?? 'Payment successful!');
        }
      } else {
        widget.onPaymentError?.call(result['error'] ?? 'Payment failed');
        if (mounted) {
          _showErrorMessage(result['error'] ?? 'Payment failed');
        }
      }
    } catch (e) {
      widget.onPaymentError?.call(e.toString());
      if (mounted) {
        _showErrorMessage(e.toString());
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: GoogleFonts.poppins(color: Colors.white),
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: GoogleFonts.poppins(color: Colors.white),
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                widget.paymentType == 'wallet_topup'
                    ? Icons.account_balance_wallet
                    : Icons.shopping_cart,
                color: const Color(0xFF6A11CB),
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  widget.paymentType == 'wallet_topup'
                      ? 'Add Money to Wallet'
                      : 'Purchase Asset',
                  style: GoogleFonts.poppins(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Payment options
          if (_paymentOptions.isNotEmpty) ...[
            Text(
              widget.paymentType == 'wallet_topup'
                  ? 'Select Amount'
                  : 'Select Payment Method',
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 12),
            
            ...(_paymentOptions.map((option) => _buildPaymentOption(option))),
          ] else ...[
            Center(
              child: Text(
                'No payment options available',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ),
          ],

          // Loading indicator
          if (_isLoading) ...[
            const SizedBox(height: 20),
            const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6A11CB)),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPaymentOption(Map<String, dynamic> option) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _isLoading ? null : () => _processPayment(option),
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                // Icon
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF6A11CB).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    option['icon'] ?? Icons.payment,
                    color: const Color(0xFF6A11CB),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                
                // Content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        option['title'] ?? '',
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                      ),
                      if (option['description'] != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          option['description'],
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                
                // Price/Arrow
                if (option['price'] != null) ...[
                  Text(
                    option['price'],
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF6A11CB),
                    ),
                  ),
                ] else ...[
                  const Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.grey,
                    size: 16,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
