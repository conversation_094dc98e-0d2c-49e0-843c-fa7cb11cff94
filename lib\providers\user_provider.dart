import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../services/firebase_service.dart';
import '../services/analytics_service.dart';
import '../services/admin_service.dart';

/// Provider class to manage user state throughout the app
class UserProvider extends ChangeNotifier {
  final FirebaseService _firebaseService = FirebaseService();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  UserModel? _user;
  bool _isLoading = false;
  String? _error;
  StreamSubscription<User?>? _authSubscription;

  // Getters
  UserModel? get user => _user;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isLoggedIn => _user != null;
  bool get isAdmin => _user?.isAdmin ?? false;
  bool get isProfileComplete => _user?.profileSetupCompleted ?? false;
  bool get isEmailVerified => _user?.emailVerified ?? false;

  // Initialize the provider
  Future<void> initialize() async {
    _setLoading(true);

    try {
      // Initialize Firebase service
      await _firebaseService.initialize();

      // Listen to auth state changes
      _authSubscription = _firebaseService.userStream.listen((User? firebaseUser) async {
        if (firebaseUser != null) {
          // User is logged in, fetch their data
          await _fetchUserData(firebaseUser.uid);

          // Start online status updates
          _startOnlineStatusUpdates();
        } else {
          // User is logged out
          _user = null;

          // Stop online status updates
          _onlineStatusTimer?.cancel();

          notifyListeners();
        }
      });

      _setLoading(false);
    } catch (e) {
      _setError('Failed to initialize user provider: $e');
    }
  }

  // Fetch user data from Firestore
  Future<void> _fetchUserData(String uid) async {
    _setLoading(true);

    try {
      // Get user document
      final DocumentSnapshot userDoc = await _firestore.collection('users').doc(uid).get();

      if (!userDoc.exists) {
        debugPrint('User document does not exist for uid: $uid');

        // Get the current Firebase user
        final User? currentUser = FirebaseAuth.instance.currentUser;

        if (currentUser != null) {
          debugPrint('Creating new user document for: ${currentUser.displayName}');

          // Create a new user document manually
          // Generate a unique username suggestion based on the display name
          final String usernameSuggestion = _generateUsernameSuggestion(currentUser.displayName ?? 'Google User');

          // Create user document
          await _firestore.collection('users').doc(currentUser.uid).set({
            'uid': currentUser.uid,
            'email': currentUser.email,
            'displayName': currentUser.displayName ?? 'Google User',
            'photoURL': currentUser.photoURL,
            'usernameSuggestion': usernameSuggestion,
            'usernameSuggestionLowercase': usernameSuggestion.toLowerCase(),
            'emailVerified': currentUser.emailVerified,
            'createdAt': FieldValue.serverTimestamp(),
            'updatedAt': FieldValue.serverTimestamp(),
            'profileSetupCompleted': false,
            'isAdmin': false,
            'isBanned': false,
            'lastLogin': FieldValue.serverTimestamp(),
            // Financial fields
            'balance': 0.0,
            'totalEarnings': 0.0,
            'pendingWithdrawals': 0.0,
            'unreadNotifications': 0,
          }, SetOptions(merge: true));

          // Try to fetch the user document again
          final DocumentSnapshot newUserDoc = await _firestore.collection('users').doc(uid).get();

          if (!newUserDoc.exists) {
            _setError('Failed to create user data');
            return;
          }

          // Continue with the newly created document
          final userData = newUserDoc.data();
          if (userData == null) {
            _setError('User data is null');
            return;
          }

          // Create user model with empty profile data
          _user = UserModel.fromFirebaseUser(
            userData as Map<String, dynamic>,
            null,
          );

          notifyListeners();
          return;
        } else {
          _setError('User data not found');
          return;
        }
      }

      // Get the user data
      final userData = userDoc.data();
      if (userData == null) {
        _setError('User data is null');
        return;
      }

      // Get profile data if available
      Map<String, dynamic>? profileData;
      try {
        final DocumentSnapshot profileDoc = await _firestore.collection('user_profiles').doc(uid).get();
        if (profileDoc.exists) {
          final rawProfileData = profileDoc.data();
          if (rawProfileData != null) {
            profileData = rawProfileData as Map<String, dynamic>;
          }
        }
      } catch (e) {
        debugPrint('Error fetching profile data: $e');
      }

      // Create user model
      _user = UserModel.fromFirebaseUser(
        userData as Map<String, dynamic>,
        profileData,
      );

      // Update last login time
      await _firestore.collection('users').doc(uid).update({
        'lastLogin': FieldValue.serverTimestamp(),
      });

      notifyListeners();
    } catch (e) {
      debugPrint('Error in _fetchUserData: $e');
      _setError('Failed to fetch user data: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Sign up with email and password
  Future<void> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String fullName,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      await _firebaseService.signUpWithEmailAndPassword(
        email: email,
        password: password,
        fullName: fullName,
      );

      // Track signup event with analytics
      AnalyticsService().logEvent(
        name: 'sign_up',
        parameters: {
          'method': 'email',
        },
      );
    } catch (e) {
      // Track signup failure
      AnalyticsService().logEvent(
        name: 'sign_up_error',
        parameters: {
          'method': 'email',
          'error': e.toString(),
        },
      );

      _setError(e.toString());
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Sign in with email and password
  Future<void> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final userCredential = await _firebaseService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Check for account restoration if user logs back in within 30 days
      if (userCredential.user != null) {
        await _checkAndRestoreAccount(userCredential.user!.uid);
      }

      // Track login event with analytics
      AnalyticsService().logEvent(
        name: 'login',
        parameters: {
          'method': 'email',
        },
      );
    } catch (e) {
      // Track login failure
      AnalyticsService().logEvent(
        name: 'login_error',
        parameters: {
          'method': 'email',
          'error': e.toString(),
        },
      );

      _setError(e.toString());
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Sign in with Google
  Future<void> signInWithGoogle() async {
    _setLoading(true);
    _clearError();

    try {
      debugPrint('UserProvider: Starting Google sign-in');
      final userCredential = await _firebaseService.signInWithGoogle();

      // Check if user is banned and handle account restoration
      if (userCredential.user != null) {
        try {
          final userDoc = await _firestore.collection('users').doc(userCredential.user!.uid).get();
          if (userDoc.exists) {
            final userData = userDoc.data();
            if (userData != null && (userData['isBanned'] == true)) {
              await _firebaseService.signOut();
              throw Exception('Your account has been banned. Please contact support.');
            }
          }

          // Check for account restoration if user logs back in within 30 days
          await _checkAndRestoreAccount(userCredential.user!.uid);
        } catch (e) {
          debugPrint('Error checking user status: $e');
          // Continue with sign-in even if we can't check user status
        }
      }

      debugPrint('UserProvider: Google sign-in successful');

      // Track login event with analytics
      AnalyticsService().logEvent(
        name: 'login',
        parameters: {
          'method': 'google',
          'is_new_user': userCredential.additionalUserInfo?.isNewUser ?? false,
        },
      );
    } catch (e) {
      debugPrint('UserProvider: Google sign-in error: $e');

      // Track login failure
      AnalyticsService().logEvent(
        name: 'login_error',
        parameters: {
          'method': 'google',
          'error': e.toString(),
        },
      );

      _setError(e.toString());
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Sign out
  Future<void> signOut() async {
    _setLoading(true);
    _clearError();

    try {
      // Track logout event with analytics
      AnalyticsService().logEvent(
        name: 'logout',
        parameters: {
          'user_id': _user?.uid ?? 'unknown',
        },
      );

      await _firebaseService.signOut();
      _user = null;
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Reset password
  Future<void> resetPassword({required String email}) async {
    _setLoading(true);
    _clearError();

    try {
      await _firebaseService.resetPassword(email: email);
    } catch (e) {
      _setError(e.toString());
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Update user profile
  Future<void> updateUserProfile({
    required String displayName,
    required String username,
    required String bio,
    required String location,
    required List<String> interests,
    required List<Map<String, dynamic>> skills,
  }) async {
    if (_user == null) {
      _setError('User not logged in');
      return;
    }

    _setLoading(true);
    _clearError();

    try {
      // Check username availability one more time before updating
      final bool isAvailable = await isUsernameAvailable(username, _user!.uid);
      if (!isAvailable) {
        throw Exception('Username is already taken. Please choose a different username.');
      }

      // Update user document
      await _firestore.collection('users').doc(_user!.uid).update({
        'displayName': displayName,
        'username': username,
        'usernameLowercase': username.toLowerCase(), // Store lowercase version for case-insensitive searches
        'profileSetupCompleted': true,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Update or create profile document
      await _firestore.collection('user_profiles').doc(_user!.uid).set({
        'uid': _user!.uid,
        'bio': bio,
        'location': location,
        'interests': interests,
        'skills': skills,
        'updatedAt': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));

      // Refresh user data
      await _fetchUserData(_user!.uid);
    } catch (e) {
      _setError('Failed to update profile: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Check if username is available
  Future<bool> isUsernameAvailable(String username, [String? currentUserId]) async {
    try {
      // Validate username format first
      if (username.length < 4) {
        _setError('Username must be at least 4 characters long.');
        return false;
      }

      if (!RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(username)) {
        _setError('Username can only contain letters, numbers, and underscores.');
        return false;
      }

      // Convert username to lowercase for case-insensitive comparison
      final String lowercaseUsername = username.toLowerCase();

      // First check with exact match
      final QuerySnapshot exactResult = await _firestore
          .collection('users')
          .where('username', isEqualTo: username)
          .limit(1)
          .get();

      // Then check with lowercase version for case-insensitive matching
      final QuerySnapshot lowercaseResult = await _firestore
          .collection('users')
          .where('usernameLowercase', isEqualTo: lowercaseUsername)
          .limit(1)
          .get();

      // Combine results
      final List<QueryDocumentSnapshot> allDocs = [...exactResult.docs, ...lowercaseResult.docs];

      // Remove duplicates (in case the same document appears in both queries)
      final Set<String> docIds = {};
      final List<QueryDocumentSnapshot> uniqueDocs = [];

      for (final doc in allDocs) {
        if (!docIds.contains(doc.id)) {
          docIds.add(doc.id);
          uniqueDocs.add(doc);
        }
      }

      // If no documents found, username is available
      if (uniqueDocs.isEmpty) {
        return true;
      }

      // If the only document found belongs to the current user, username is available
      if (currentUserId != null && uniqueDocs.length == 1) {
        final doc = uniqueDocs.first;
        final userData = doc.data() as Map<String, dynamic>;
        if (userData['uid'] == currentUserId) {
          return true;
        }
      }

      // Username is taken by someone else
      _setError('Username is already taken. Please choose a different username.');
      return false;
    } catch (e) {
      debugPrint('Failed to check username availability: $e');
      _setError('Failed to check username availability: $e');
      // Don't assume available if check fails - safer to assume it's taken
      return false;
    }
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    _isLoading = false;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  // Generate a username suggestion based on the full name
  String _generateUsernameSuggestion(String fullName) {
    // Remove spaces and special characters
    String username = fullName.toLowerCase().replaceAll(RegExp(r'[^\w\s]+'), '');
    username = username.replaceAll(' ', '_');

    // Add a random suffix (4 random characters)
    final String randomSuffix = DateTime.now().millisecondsSinceEpoch.toString().substring(9, 13);
    return '${username}_$randomSuffix';
  }

  // Update notification settings
  Future<void> updateNotificationSettings(Map<String, dynamic> settings) async {
    _setLoading(true);
    _clearError();

    try {
      if (_user == null) {
        throw Exception('User not logged in');
      }

      // Update user document
      await _firestore.collection('users').doc(_user!.uid).update({
        'notificationSettings': settings,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Update user model
      _user = _user!.copyWith(
        notificationSettings: settings,
      );

      notifyListeners();
    } catch (e) {
      _setError(e.toString());
      rethrow;
    } finally {
      _setLoading(false);
    }
  }



  // Update theme
  Future<void> updateTheme(String theme) async {
    _setLoading(true);
    _clearError();

    try {
      if (_user == null) {
        throw Exception('User not logged in');
      }

      // Update user document
      await _firestore.collection('users').doc(_user!.uid).update({
        'theme': theme,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Update user model
      _user = _user!.copyWith(
        theme: theme,
      );

      notifyListeners();
    } catch (e) {
      _setError(e.toString());
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Check and restore account if user logs back in within 30 days of deletion request
  Future<void> _checkAndRestoreAccount(String userId) async {
    try {
      debugPrint('UserProvider: Starting account restoration check for user: $userId');

      // Use AdminService for account restoration
      final AdminService adminService = AdminService();
      final result = await adminService.checkAndAutoRestoreAccount(userId);

      debugPrint('UserProvider: Account restoration result: $result');

      if (result['success'] == true && result['restored'] == true) {
        debugPrint('UserProvider: Account successfully restored for user: $userId');

        // Refresh user data to get updated account status
        await _fetchUserData(userId);
        debugPrint('UserProvider: User data refreshed after restoration');
      } else if (result['success'] == false) {
        debugPrint('UserProvider: Failed to restore account: ${result['error']}');
      } else {
        debugPrint('UserProvider: Account restoration not needed: ${result['message']}');
      }
    } catch (e) {
      debugPrint('UserProvider: Error checking/restoring account: $e');
      // Don't throw error as this shouldn't prevent login
    }
  }

  // Update user's online status
  Future<void> _updateOnlineStatus() async {
    try {
      if (_user != null) {
        // Update the user's last active timestamp
        await _firestore.collection('users').doc(_user!.uid).update({
          'lastActive': FieldValue.serverTimestamp(),
        });

        debugPrint('UserProvider: Updated user online status');
      }
    } catch (e) {
      debugPrint('Error updating online status: $e');
    }
  }

  // Set up periodic online status updates
  Timer? _onlineStatusTimer;

  void _startOnlineStatusUpdates() {
    // Cancel any existing timer
    _onlineStatusTimer?.cancel();

    // Update status immediately
    _updateOnlineStatus();

    // Set up periodic updates every 2 minutes
    _onlineStatusTimer = Timer.periodic(const Duration(minutes: 2), (_) {
      _updateOnlineStatus();
    });

    debugPrint('UserProvider: Started online status updates');
  }

  @override
  void dispose() {
    _onlineStatusTimer?.cancel();
    _authSubscription?.cancel();
    super.dispose();
  }
}
