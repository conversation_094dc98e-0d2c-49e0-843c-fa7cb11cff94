import 'package:cloud_firestore/cloud_firestore.dart';

/// Model class for assets in the application
class AssetModel {
  final String? id;
  final String? title;
  final String? description;
  final String? authorId;
  final String? authorName;
  final String? thumbnailUrl;
  final String? fileUrl;
  final String? category;
  final String? platform;
  final double? price;
  final int? downloadCount;
  final double? rating;
  final List<String> keywords;
  final bool isApproved;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  AssetModel({
    this.id,
    this.title,
    this.description,
    this.authorId,
    this.authorName,
    this.thumbnailUrl,
    this.fileUrl,
    this.category,
    this.platform,
    this.price,
    this.downloadCount,
    this.rating,
    this.keywords = const [],
    this.isApproved = false,
    this.createdAt,
    this.updatedAt,
  });

  /// Create an AssetModel from a Firestore document
  factory AssetModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>? ?? {};
    
    return AssetModel(
      id: doc.id,
      title: data['title'] as String?,
      description: data['description'] as String?,
      authorId: data['authorId'] as String?,
      authorName: data['authorName'] as String?,
      thumbnailUrl: data['thumbnailUrl'] as String?,
      fileUrl: data['fileUrl'] as String?,
      category: data['category'] as String?,
      platform: data['platform'] as String?,
      price: (data['price'] is int) 
          ? (data['price'] as int).toDouble() 
          : data['price'] as double?,
      downloadCount: data['downloadCount'] as int?,
      rating: (data['rating'] is int) 
          ? (data['rating'] as int).toDouble() 
          : data['rating'] as double?,
      keywords: List<String>.from(data['keywords'] ?? []),
      isApproved: data['isApproved'] as bool? ?? false,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate(),
    );
  }

  /// Convert AssetModel to a Map for Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'titleLower': title?.toLowerCase(),
      'description': description,
      'authorId': authorId,
      'authorName': authorName,
      'thumbnailUrl': thumbnailUrl,
      'fileUrl': fileUrl,
      'category': category,
      'platform': platform,
      'price': price,
      'downloadCount': downloadCount ?? 0,
      'rating': rating ?? 0.0,
      'keywords': keywords,
      'isApproved': isApproved,
      'createdAt': createdAt != null ? Timestamp.fromDate(createdAt!) : FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    };
  }

  /// Create a copy of this AssetModel with the given fields replaced with the new values
  AssetModel copyWith({
    String? id,
    String? title,
    String? description,
    String? authorId,
    String? authorName,
    String? thumbnailUrl,
    String? fileUrl,
    String? category,
    String? platform,
    double? price,
    int? downloadCount,
    double? rating,
    List<String>? keywords,
    bool? isApproved,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AssetModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      authorId: authorId ?? this.authorId,
      authorName: authorName ?? this.authorName,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      fileUrl: fileUrl ?? this.fileUrl,
      category: category ?? this.category,
      platform: platform ?? this.platform,
      price: price ?? this.price,
      downloadCount: downloadCount ?? this.downloadCount,
      rating: rating ?? this.rating,
      keywords: keywords ?? this.keywords,
      isApproved: isApproved ?? this.isApproved,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
