import 'package:cloud_firestore/cloud_firestore.dart';

/// Withdrawal model class to handle withdrawal requests
class WithdrawalModel {
  final String id;
  final String userId;
  final double amount;
  final String paymentMethod;
  final Map<String, dynamic> paymentDetails;
  final String status; // 'pending', 'approved', 'rejected'
  final String? processedBy;
  final String? notes;
  final DateTime requestedAt;
  final DateTime? processedAt;
  final DateTime updatedAt;

  WithdrawalModel({
    required this.id,
    required this.userId,
    required this.amount,
    required this.paymentMethod,
    required this.paymentDetails,
    required this.status,
    this.processedBy,
    this.notes,
    DateTime? requestedAt,
    this.processedAt,
    DateTime? updatedAt,
  }) : 
    requestedAt = requestedAt ?? DateTime.now(),
    updatedAt = updatedAt ?? DateTime.now();

  // Create a WithdrawalModel from Firestore data
  factory WithdrawalModel.fromFirestore(
    DocumentSnapshot doc,
  ) {
    final data = doc.data() as Map<String, dynamic>;
    final Timestamp requestedAtTimestamp = data['requestedAt'] as Timestamp? ?? 
        Timestamp.fromDate(DateTime.now());
    final Timestamp? processedAtTimestamp = data['processedAt'] as Timestamp?;
    final Timestamp updatedAtTimestamp = data['updatedAt'] as Timestamp? ?? 
        Timestamp.fromDate(DateTime.now());

    return WithdrawalModel(
      id: doc.id,
      userId: data['userId'] as String? ?? '',
      amount: (data['amount'] as num?)?.toDouble() ?? 0.0,
      paymentMethod: data['paymentMethod'] as String? ?? '',
      paymentDetails: data['paymentDetails'] as Map<String, dynamic>? ?? {},
      status: data['status'] as String? ?? 'pending',
      processedBy: data['processedBy'] as String?,
      notes: data['notes'] as String?,
      requestedAt: requestedAtTimestamp.toDate(),
      processedAt: processedAtTimestamp?.toDate(),
      updatedAt: updatedAtTimestamp.toDate(),
    );
  }

  // Convert WithdrawalModel to a Map for Firestore
  Map<String, dynamic> toMap() {
    final Map<String, dynamic> data = {
      'userId': userId,
      'amount': amount,
      'paymentMethod': paymentMethod,
      'paymentDetails': paymentDetails,
      'status': status,
      'requestedAt': Timestamp.fromDate(requestedAt),
      'updatedAt': Timestamp.fromDate(DateTime.now()),
    };

    if (processedBy != null) {
      data['processedBy'] = processedBy;
    }

    if (notes != null) {
      data['notes'] = notes;
    }

    if (processedAt != null) {
      data['processedAt'] = Timestamp.fromDate(processedAt!);
    }

    return data;
  }
}
