<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Asatu - Digital Asset Marketplace">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="Asatu">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>Asatu</title>
  <link rel="manifest" href="manifest.json">

  <!--
    Firebase SDK - Using the latest compatible version for Flutter web
    https://firebase.google.com/docs/web/setup
  -->
  <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-app.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-auth.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-firestore.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-storage.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-functions.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-analytics.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-database.js"></script>
  <script src="https://www.gstatic.com/firebasejs/8.10.1/firebase-messaging.js"></script>

  <!-- Stripe.js for payment processing -->
  <script src="https://js.stripe.com/v3/"></script>

  <!-- Firebase initialization script -->
  <script>
    // Define the missing interop functions needed by the Flutter Firebase plugins

    // Define PromiseJsImpl
    window.PromiseJsImpl = Promise;

    // Define dartify function
    window.dartify = function(obj) {
      return obj;
    };

    // Define jsify function
    window.jsify = function(obj) {
      return obj;
    };

    // Define handleThenable function
    window.handleThenable = function(promise) {
      return promise;
    };

    // Make these functions available in all contexts
    if (typeof self !== 'undefined') {
      self.dartify = window.dartify;
      self.jsify = window.jsify;
      self.PromiseJsImpl = window.PromiseJsImpl;
      self.handleThenable = window.handleThenable;
    }

    // Also add to globalThis for newer browsers
    if (typeof globalThis !== 'undefined') {
      globalThis.dartify = window.dartify;
      globalThis.jsify = window.jsify;
      globalThis.PromiseJsImpl = window.PromiseJsImpl;
      globalThis.handleThenable = window.handleThenable;
    }

    // Firebase configuration from the Firebase console
    const firebaseConfig = {
      apiKey: "AIzaSyA-wuJncazZMOulh2AGTwUFuRcBCtjzD0A",
      authDomain: "asatu-f1852.firebaseapp.com",
      databaseURL: "https://asatu-f1852-default-rtdb.firebaseio.com",
      projectId: "asatu-f1852",
      storageBucket: "asatu-f1852.firebasestorage.app",
      messagingSenderId: "808255690080",
      appId: "1:808255690080:web:a8f9df47d004e403003a05",
      measurementId: "G-SC7H2TM8L0"
    };

    // Initialize Firebase
    if (!firebase.apps || !firebase.apps.length) {
      firebase.initializeApp(firebaseConfig);
      console.log('Firebase initialized successfully');

      // Initialize Analytics
      firebase.analytics();
      console.log('Firebase Analytics initialized');
    } else {
      console.log('Firebase already initialized');
    }
  </script>
</head>
<body>
  <!-- Flutter initialization script -->
  <script src="flutter_bootstrap.js" async></script>
</body>
</html>
