import 'dart:io';
import 'package:flutter/material.dart';
import 'dart:ui' as ui;

/// This is a simplified utility to create an app icon
/// Run this file separately to generate the icon
void main() async {
  // Create a simple app icon that matches our splash screen design
  const iconSize = 1024.0; // Standard size for app icons
  
  final recorder = ui.PictureRecorder();
  final canvas = Canvas(recorder);
  const size = Size(iconSize, iconSize);
  
  // Draw the icon
  drawAppIcon(canvas, size);
  
  // Convert to an image
  final picture = recorder.endRecording();
  final img = await picture.toImage(size.width.toInt(), size.height.toInt());
  final byteData = await img.toByteData(format: ui.ImageByteFormat.png);
  
  if (byteData != null) {
    final buffer = byteData.buffer.asUint8List();
    
    // Create the directory if it doesn't exist
    final directory = Directory('assets/icons');
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
    
    // Save the icon
    final iconFile = File('assets/icons/app_icon.png');
    await iconFile.writeAsBytes(buffer);
    
    print('App icon created successfully at: ${iconFile.path}');
  } else {
    print('Failed to create app icon');
  }
}

/// Draws a simple app icon that matches our splash screen logo
void drawAppIcon(Canvas canvas, Size size) {
  final center = Offset(size.width / 2, size.height / 2);
  
  // Background
  final bgPaint = Paint()
    ..color = const Color(0xFF6A11CB) // Purple from our gradient
    ..style = PaintingStyle.fill;
  canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), bgPaint);
  
  // Gradient overlay
  final gradientPaint = Paint()
    ..shader = ui.Gradient.linear(
      const Offset(0, 0),
      Offset(size.width, size.height),
      [
        const Color(0xFF6A11CB), // Purple
        const Color(0xFF2575FC), // Blue
      ],
    )
    ..style = PaintingStyle.fill;
  canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), gradientPaint);
  
  // Draw a circular background for the logo
  final circlePaint = Paint()
    ..color = Colors.white.withOpacity(0.2)
    ..style = PaintingStyle.fill;
  canvas.drawCircle(center, size.width * 0.4, circlePaint);
  
  // Draw the "A" letter
  drawALetter(canvas, center, size.width * 0.5);
  
  // Draw connection dots
  final dotRadius = size.width * 0.05;
  
  // Blue dot (top left)
  drawDot(canvas, 
    Offset(center.dx - size.width * 0.2, center.dy - size.width * 0.15), 
    dotRadius, 
    Colors.blue.shade700
  );
  
  // Red dot (top right)
  drawDot(canvas, 
    Offset(center.dx + size.width * 0.2, center.dy - size.width * 0.05), 
    dotRadius, 
    Colors.red.shade700
  );
  
  // Green dot (bottom)
  drawDot(canvas, 
    Offset(center.dx, center.dy + size.width * 0.2), 
    dotRadius, 
    Colors.green.shade700
  );
  
  // Draw connecting lines
  final linePaint = Paint()
    ..color = Colors.white.withOpacity(0.6)
    ..style = PaintingStyle.stroke
    ..strokeWidth = size.width * 0.01;
  
  // Connect the dots
  canvas.drawLine(
    Offset(center.dx - size.width * 0.2, center.dy - size.width * 0.15),
    Offset(center.dx + size.width * 0.2, center.dy - size.width * 0.05),
    linePaint
  );
  
  canvas.drawLine(
    Offset(center.dx - size.width * 0.2, center.dy - size.width * 0.15),
    Offset(center.dx, center.dy + size.width * 0.2),
    linePaint
  );
  
  canvas.drawLine(
    Offset(center.dx + size.width * 0.2, center.dy - size.width * 0.05),
    Offset(center.dx, center.dy + size.width * 0.2),
    linePaint
  );
}

/// Draws the "A" letter in the center of the icon
void drawALetter(Canvas canvas, Offset center, double size) {
  final paint = Paint()
    ..color = Colors.white
    ..style = PaintingStyle.fill;
  
  final path = Path();
  
  // Draw "A" shape
  path.moveTo(center.dx, center.dy - size * 0.3); // Top point
  path.lineTo(center.dx + size * 0.25, center.dy + size * 0.3); // Bottom right
  path.lineTo(center.dx + size * 0.15, center.dy + size * 0.3); // Indent right
  path.lineTo(center.dx, center.dy); // Middle right
  path.lineTo(center.dx - size * 0.15, center.dy + size * 0.3); // Middle left
  path.lineTo(center.dx - size * 0.25, center.dy + size * 0.3); // Bottom left
  path.close();
  
  // Draw the shape
  canvas.drawPath(path, paint);
  
  // Add a horizontal line for the crossbar of "A"
  final crossbarPaint = Paint()
    ..color = const Color(0xFF6A11CB)
    ..style = PaintingStyle.stroke
    ..strokeWidth = size * 0.04;
    
  canvas.drawLine(
    Offset(center.dx - size * 0.15, center.dy + size * 0.1),
    Offset(center.dx + size * 0.15, center.dy + size * 0.1),
    crossbarPaint
  );
}

/// Helper method to draw a colored dot
void drawDot(Canvas canvas, Offset position, double radius, Color color) {
  // Draw glow
  final glowPaint = Paint()
    ..color = color.withOpacity(0.5)
    ..style = PaintingStyle.fill
    ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 10);
  canvas.drawCircle(position, radius * 1.5, glowPaint);
  
  // Draw dot
  final dotPaint = Paint()
    ..color = color
    ..style = PaintingStyle.fill;
  canvas.drawCircle(position, radius, dotPaint);
}
