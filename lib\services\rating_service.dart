import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'notification_service.dart';
import 'analytics_service.dart';

class RatingService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final NotificationService _notificationService = NotificationService();
  final AnalyticsService _analyticsService = AnalyticsService();

  String? get currentUserId => _auth.currentUser?.uid;

  // Submit a review for a developer after project completion
  Future<Map<String, dynamic>> submitProjectReview({
    required String hireRequestId,
    required String developerId,
    required int rating,
    required String comment,
    String? projectType,
  }) async {
    try {
      if (currentUserId == null) {
        return {'success': false, 'error': 'User not authenticated'};
      }

      debugPrint('RatingService - submitProjectReview: Starting review submission for hire request $hireRequestId');

      // Get the hire request to verify the client is the reviewer
      final DocumentSnapshot hireRequestDoc = await _firestore
          .collection('hire_requests')
          .doc(hireRequestId)
          .get();

      if (!hireRequestDoc.exists) {
        debugPrint('RatingService - submitProjectReview: Hire request not found');
        return {'success': false, 'error': 'Hire request not found'};
      }

      final hireRequestData = hireRequestDoc.data() as Map<String, dynamic>;

      // Verify that the current user is the client
      if (hireRequestData['clientId'] != currentUserId) {
        debugPrint('RatingService - submitProjectReview: User is not the client of this hire request');
        return {'success': false, 'error': 'Only the client can review this project'};
      }

      // Verify that the project is completed
      if (hireRequestData['status'] != 'completed' || hireRequestData['isCompleted'] != true) {
        debugPrint('RatingService - submitProjectReview: Project is not completed yet');
        return {'success': false, 'error': 'Project must be completed before reviewing'};
      }

      // Check if the project has already been rated
      if (hireRequestData['isRated'] == true) {
        debugPrint('RatingService - submitProjectReview: Project has already been rated');
        return {'success': false, 'error': 'This project has already been rated'};
      }

      // Get client info for the review
      final DocumentSnapshot clientDoc = await _firestore
          .collection('users')
          .doc(currentUserId)
          .get();

      if (!clientDoc.exists) {
        debugPrint('RatingService - submitProjectReview: Client profile not found');
        return {'success': false, 'error': 'Client profile not found'};
      }

      final clientData = clientDoc.data() as Map<String, dynamic>;
      debugPrint('RatingService - submitProjectReview: Got client info: ${clientData['displayName']}');

      // Use a transaction to ensure all operations are atomic
      final reviewId = await _firestore.runTransaction<String>(
        (transaction) async {
          // Create the review document
          final reviewRef = _firestore.collection('reviews').doc();

          // Prepare review data
          final reviewData = {
            'reviewerId': currentUserId,
            'reviewerName': clientData['displayName'] ?? 'Anonymous',
            'reviewerAvatar': clientData['photoURL'] ?? '',
            'receiverId': developerId,
            'hireRequestId': hireRequestId,
            'projectName': hireRequestData['planName'] ?? 'Project',
            'projectType': projectType ?? hireRequestData['planType'] ?? hireRequestData['planName'] ?? 'Custom Project',
            'rating': rating,
            'comment': comment,
            'createdAt': FieldValue.serverTimestamp(),
          };

          // Set the review document
          transaction.set(reviewRef, reviewData);

          // Update the hire request to mark it as rated
          transaction.update(
            _firestore.collection('hire_requests').doc(hireRequestId),
            {
              'isRated': true,
              'rating': rating,
              'review': comment,
              'reviewId': reviewRef.id,
              'reviewedAt': FieldValue.serverTimestamp(),
            },
          );

          debugPrint('RatingService - submitProjectReview: Created review document with ID: ${reviewRef.id}');
          return reviewRef.id;
        },
        timeout: const Duration(seconds: 10),
      );

      debugPrint('RatingService - submitProjectReview: Transaction completed successfully');

      // Update the developer's average rating
      await _updateDeveloperRating(developerId);

      // Send notification to the developer
      await _notificationService.sendNotification(
        userId: developerId,
        title: 'New Review Received',
        body: 'You received a $rating-star review for your project',
        type: 'new_review',
        data: {
          'hireRequestId': hireRequestId,
          'reviewId': reviewId,
          'rating': rating,
          'projectName': hireRequestData['planName'] ?? 'Project',
          'comment': comment,
        },
      );

      // Update the developer's unread notification count
      try {
        final userDoc = await _firestore.collection('users').doc(developerId).get();
        if (userDoc.exists) {
          final int currentCount = userDoc.data()?['unreadNotifications'] ?? 0;
          await _firestore.collection('users').doc(developerId).update({
            'unreadNotifications': currentCount + 1,
          });
        }
      } catch (e) {
        debugPrint('RatingService - submitProjectReview: Error updating unread notification count: $e');
      }

      // Log analytics event
      _analyticsService.logEvent(
        name: 'review_submitted',
        parameters: {
          'rating': rating,
          'project_type': projectType ?? hireRequestData['planName'] ?? 'Custom Project',
          'has_comment': comment.isNotEmpty,
        },
      );

      debugPrint('RatingService - submitProjectReview: Review submission completed successfully');
      return {
        'success': true,
        'reviewId': reviewId,
      };
    } catch (e) {
      debugPrint('RatingService - submitProjectReview: Error submitting review: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  // Update a developer's average rating
  Future<void> _updateDeveloperRating(String developerId) async {
    try {
      debugPrint('RatingService - _updateDeveloperRating: Updating rating for developer $developerId');

      // Get all reviews for this developer from the reviews collection
      final QuerySnapshot reviewsSnapshot = await _firestore
          .collection('reviews')
          .where('receiverId', isEqualTo: developerId)
          .get();

      debugPrint('RatingService - _updateDeveloperRating: Found ${reviewsSnapshot.docs.length} reviews in reviews collection');

      // Calculate average rating
      double totalRating = 0;
      int reviewCount = 0;

      // Process reviews from the reviews collection
      for (final doc in reviewsSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        if (data.containsKey('rating') && data['rating'] is num) {
          totalRating += (data['rating'] as num).toDouble();
          reviewCount++;
        }
      }

      // Also check for reviews in hire_requests collection
      final QuerySnapshot hireRequestsSnapshot = await _firestore
          .collection('hire_requests')
          .where('developerId', isEqualTo: developerId)
          .where('isRated', isEqualTo: true)
          .get();

      debugPrint('RatingService - _updateDeveloperRating: Found ${hireRequestsSnapshot.docs.length} rated hire requests');

      // Process ratings from hire requests
      for (final doc in hireRequestsSnapshot.docs) {
        final data = doc.data() as Map<String, dynamic>;
        if (data.containsKey('rating') && data['rating'] is num) {
          // Check if this rating is already counted in the reviews collection
          // by matching the hire request ID
          bool alreadyCounted = false;
          for (final reviewDoc in reviewsSnapshot.docs) {
            final reviewData = reviewDoc.data() as Map<String, dynamic>;
            if (reviewData['hireRequestId'] == doc.id) {
              alreadyCounted = true;
              break;
            }
          }

          // Only count if not already counted
          if (!alreadyCounted) {
            totalRating += (data['rating'] as num).toDouble();
            reviewCount++;
          }
        }
      }

      final double averageRating = reviewCount > 0 ? totalRating / reviewCount : 0;

      debugPrint('RatingService - _updateDeveloperRating: Calculated average rating: $averageRating from $reviewCount reviews');

      // Update the developer's profile with the new rating
      await _firestore.collection('users').doc(developerId).update({
        'rating': averageRating,
        'reviewCount': reviewCount,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      debugPrint('RatingService - _updateDeveloperRating: Updated developer profile with new rating');
    } catch (e) {
      debugPrint('RatingService - _updateDeveloperRating: Error updating developer rating: $e');
    }
  }

  // Get reviews for a specific hire request
  Future<Map<String, dynamic>?> getHireRequestReview(String hireRequestId) async {
    try {
      final QuerySnapshot reviewSnapshot = await _firestore
          .collection('reviews')
          .where('hireRequestId', isEqualTo: hireRequestId)
          .limit(1)
          .get();

      if (reviewSnapshot.docs.isEmpty) {
        return null;
      }

      final doc = reviewSnapshot.docs.first;
      final data = doc.data() as Map<String, dynamic>;

      return {
        'id': doc.id,
        ...data,
      };
    } catch (e) {
      debugPrint('Error getting hire request review: $e');
      return null;
    }
  }

  // Get all reviews for a developer
  Future<List<Map<String, dynamic>>> getDeveloperReviews(String developerId) async {
    try {
      final QuerySnapshot reviewsSnapshot = await _firestore
          .collection('reviews')
          .where('receiverId', isEqualTo: developerId)
          .orderBy('createdAt', descending: true)
          .get();

      return reviewsSnapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;
        return {
          'id': doc.id,
          ...data,
        };
      }).toList();
    } catch (e) {
      debugPrint('Error getting developer reviews: $e');
      return [];
    }
  }
}
