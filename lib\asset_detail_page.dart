import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'widgets/rating_bar.dart';
import 'public_profile_page.dart';
import 'services/user_content_service.dart';

class AssetDetailPage extends StatefulWidget {
  final Map<String, dynamic>? asset;
  final String? assetId;

  const AssetDetailPage({Key? key, this.asset, this.assetId}) : super(key: key);

  @override
  State<AssetDetailPage> createState() => _AssetDetailPageState();
}

class _AssetDetailPageState extends State<AssetDetailPage> {
  int _currentImageIndex = 0;
  bool _isLiked = false;
  bool _isSaved = false;
  bool _isPurchased = false;
  final String _selectedPaymentMethod = 'credit_card'; // Default payment method
  final ScrollController _scrollController = ScrollController();
  Map<String, dynamic>? _assetData;

  // Mock wallet balance
  final double _walletBalance = 150.0;

  // Sample screenshots
  final List<String> _dummyScreenshots = [
    'assets/images/screenshot1.jpg',
    'assets/images/screenshot2.jpg',
    'assets/images/screenshot3.jpg',
    'assets/images/screenshot4.jpg',
  ];

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    // If we have an assetId but no asset data, fetch the asset data
    if (widget.assetId != null && widget.asset == null) {
      _fetchAssetData();
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    // Add scroll behavior if needed
  }

  // Fetch asset data from Firebase
  Future<void> _fetchAssetData() async {
    try {
      final userContentService = UserContentService();
      final assetData = await userContentService.getAssetById(widget.assetId!);

      if (mounted) {
        setState(() {
          _assetData = assetData;
        });
      }
    } catch (e) {
      debugPrint('Error fetching asset data: $e');
    }
  }

  // Sample reviews
  final List<Map<String, dynamic>> _reviews = [
    {
      'name': 'Sarah Johnson',
      'avatar': 'assets/images/user1.jpg',
      'rating': 5,
      'date': '2 days ago',
      'comment': 'This asset is amazing! It saved me so much time on my project. The code is clean and well-documented.',
      'isVerified': true,
    },
    {
      'name': 'Michael Chen',
      'avatar': 'assets/images/user2.jpg',
      'rating': 4,
      'date': '1 week ago',
      'comment': 'Great asset, but could use more customization options. Otherwise, it works perfectly for what I needed.',
      'isVerified': false,
    },
    {
      'name': 'Emma Wilson',
      'avatar': 'assets/images/user3.jpg',
      'rating': 5,
      'date': '2 weeks ago',
      'comment': 'Excellent quality and very easy to integrate. The support from the author was also very helpful.',
      'isVerified': true,
    },
  ];

  // Sample related assets
  final List<Map<String, dynamic>> _relatedAssets = [
    {
      'title': 'Modern UI Kit',
      'author': 'David Miller',
      'price': 39.99,
      'rating': 4.8,
      'downloads': 1250,
      'type': 'UI Kits',
      'platform': 'Flutter',
    },
    {
      'title': 'E-commerce Template',
      'author': 'Sarah Johnson',
      'price': 49.99,
      'rating': 4.7,
      'downloads': 980,
      'type': 'Templates',
      'platform': 'Flutter',
    },
    {
      'title': 'Social Media UI',
      'author': 'Michael Chen',
      'price': 0,
      'rating': 4.5,
      'downloads': 2340,
      'type': 'UI Kits',
      'platform': 'Flutter',
    },
  ];

  @override
  Widget build(BuildContext context) {
    // Use _assetData if available, otherwise use widget.asset
    final assetData = _assetData ?? widget.asset;

    // If we're still loading data and have no asset info yet, show a loading indicator
    if (widget.assetId != null && widget.asset == null && _assetData == null) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: CustomScrollView(
        slivers: [
          // App Bar with gradient background
          SliverAppBar(
            expandedHeight: 60,
            floating: false,
            pinned: true,
            backgroundColor: const Color(0xFF6A11CB),
            flexibleSpace: Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [Color(0xFF6A11CB), Color(0xFF2575FC)],
                ),
              ),
            ),
            leading: IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.white),
              onPressed: () => Navigator.of(context).pop(),
            ),
            title: Text(
              'Asset Details',
              style: GoogleFonts.poppins(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
            actions: [
              IconButton(
                icon: Icon(
                  _isSaved ? Icons.bookmark : Icons.bookmark_border,
                  color: Colors.white,
                ),
                onPressed: () {
                  setState(() {
                    _isSaved = !_isSaved;
                  });
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        _isSaved
                            ? 'Added to your wishlist'
                            : 'Removed from your wishlist',
                        style: GoogleFonts.poppins(),
                      ),
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                },
              ),
              IconButton(
                icon: Icon(
                  _isLiked ? Icons.favorite : Icons.favorite_border,
                  color: _isLiked ? Colors.red : Colors.white,
                ),
                onPressed: () {
                  setState(() {
                    _isLiked = !_isLiked;
                  });
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        _isLiked
                            ? 'Added to favorites'
                            : 'Removed from favorites',
                        style: GoogleFonts.poppins(),
                      ),
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                },
              ),
              IconButton(
                icon: const Icon(Icons.more_vert, color: Colors.white),
                onPressed: () {
                  _showOptionsBottomSheet();
                },
              ),
            ],
          ),

          // Main Content
          SliverToBoxAdapter(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Hero section with asset title and info
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Title and author
                        Padding(
                          padding: const EdgeInsets.fromLTRB(20, 20, 20, 10),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Title
                              Text(
                                widget.asset?['title'] ?? 'Asset Title',
                                style: GoogleFonts.poppins(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                ),
                              ),
                              const SizedBox(height: 4),

                              // Author with profile link
                              GestureDetector(
                                onTap: () {
                                  // Navigate to publisher profile
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => PublicProfilePage(userId: widget.asset?['authorId']),
                                    ),
                                  );
                                },
                                child: Row(
                                  children: [
                                    CircleAvatar(
                                      radius: 12,
                                      backgroundColor: Colors.grey[200],
                                      child: Text(
                                        widget.asset?['author']?.substring(0, 1) ?? 'A',
                                        style: GoogleFonts.poppins(
                                          fontSize: 10,
                                          fontWeight: FontWeight.bold,
                                          color: const Color(0xFF6A11CB),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'by ${widget.asset?['author'] ?? 'Unknown Author'}',
                                      style: GoogleFonts.poppins(
                                        color: const Color(0xFF6A11CB),
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    const SizedBox(width: 4),
                                    if (widget.asset?['verified'] == true)
                                      const Icon(
                                        Icons.verified,
                                        color: Color(0xFF6A11CB),
                                        size: 14,
                                      ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),

                        // Stats row - using SingleChildScrollView to prevent overflow
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          child: SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: Row(
                              children: [
                                // Rating
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6), // Reduced horizontal padding
                                  decoration: BoxDecoration(
                                    color: Colors.amber.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Row(
                                    children: [
                                      const Icon(
                                        Icons.star,
                                        color: Colors.amber,
                                        size: 16,
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        '${widget.asset?['rating'] ?? 4.5}',
                                        style: GoogleFonts.poppins(
                                          fontWeight: FontWeight.w600,
                                          fontSize: 12,
                                          color: Colors.amber[800],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 8),

                                // Downloads
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6), // Reduced horizontal padding
                                  decoration: BoxDecoration(
                                    color: Colors.blue.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Row(
                                    children: [
                                      const Icon(
                                        Icons.download,
                                        color: Colors.blue,
                                        size: 16,
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        '${widget.asset?['downloads'] ?? 1250}',
                                        style: GoogleFonts.poppins(
                                          fontWeight: FontWeight.w600,
                                          fontSize: 12,
                                          color: Colors.blue[700],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 8),

                                // Type tag
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6), // Reduced horizontal padding
                                  decoration: BoxDecoration(
                                    color: const Color(0xFF6A11CB).withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Text(
                                    widget.asset?['type'] ?? 'UI Kit',
                                    style: GoogleFonts.poppins(
                                      color: const Color(0xFF6A11CB),
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),

                                // Platform tag
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6), // Reduced horizontal padding
                                  decoration: BoxDecoration(
                                    color: Colors.green.withOpacity(0.1),
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Text(
                                    widget.asset?['platform'] ?? 'Flutter',
                                    style: GoogleFonts.poppins(
                                      color: Colors.green[700],
                                      fontSize: 12,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),
                      ],
                    ),
                  ),

                  // Screenshots carousel with improved design
                  Container(
                    width: double.infinity,
                    color: Colors.white,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(left: 20, top: 16, bottom: 10),
                          child: Text(
                            'Screenshots & Previews',
                            style: GoogleFonts.poppins(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                        CarouselSlider(
                          options: CarouselOptions(
                            height: 250,
                            viewportFraction: 1.0,
                            enlargeCenterPage: false,
                            enableInfiniteScroll: true,
                            autoPlay: true,
                            autoPlayInterval: const Duration(seconds: 5),
                            autoPlayAnimationDuration: const Duration(milliseconds: 800),
                            autoPlayCurve: Curves.fastOutSlowIn,
                            onPageChanged: (index, reason) {
                              setState(() {
                                _currentImageIndex = index;
                              });
                            },
                          ),
                          items: widget.asset?['screenshots'] != null && (widget.asset?['screenshots'] as List?)?.isNotEmpty == true
                              ? (widget.asset?['screenshots'] as List).map<Widget>((screenshot) {
                                  return Container(
                                    width: double.infinity,
                                    color: Colors.grey[200],
                                    child: Stack(
                                      children: [
                                        // Placeholder or actual image
                                        Center(
                                          child: Column(
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                              Icon(
                                                Icons.image,
                                                size: 64,
                                                color: Colors.grey[400],
                                              ),
                                              const SizedBox(height: 8),
                                              Text(
                                                screenshot['name'] ?? 'Screenshot',
                                                style: GoogleFonts.poppins(
                                                  color: Colors.grey[600],
                                                  fontSize: 14,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        // Gradient overlay at bottom
                                        Positioned(
                                          bottom: 0,
                                          left: 0,
                                          right: 0,
                                          child: Container(
                                            height: 60,
                                            decoration: BoxDecoration(
                                              gradient: LinearGradient(
                                                begin: Alignment.topCenter,
                                                end: Alignment.bottomCenter,
                                                colors: [
                                                  Colors.transparent,
                                                  Colors.black.withOpacity(0.7),
                                                ],
                                              ),
                                            ),
                                            padding: const EdgeInsets.all(12),
                                            child: Align(
                                              alignment: Alignment.bottomLeft,
                                              child: Text(
                                                screenshot['name'] ?? 'Screenshot',
                                                style: GoogleFonts.poppins(
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.w500,
                                                  fontSize: 14,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                }).toList()
                              : List.generate(4, (index) {
                                  return Container(
                                    width: double.infinity,
                                    color: Colors.grey[200],
                                    child: Stack(
                                      children: [
                                        // Placeholder or actual image
                                        Center(
                                          child: Icon(
                                            _getIconForType(widget.asset?['type'] ?? 'UI Kit'),
                                            size: 80,
                                            color: Colors.grey[400],
                                          ),
                                        ),
                                        // Gradient overlay at bottom
                                        Positioned(
                                          bottom: 0,
                                          left: 0,
                                          right: 0,
                                          child: Container(
                                            height: 60,
                                            decoration: BoxDecoration(
                                              gradient: LinearGradient(
                                                begin: Alignment.topCenter,
                                                end: Alignment.bottomCenter,
                                                colors: [
                                                  Colors.transparent,
                                                  Colors.black.withOpacity(0.7),
                                                ],
                                              ),
                                            ),
                                            padding: const EdgeInsets.all(12),
                                            child: Align(
                                              alignment: Alignment.bottomLeft,
                                              child: Text(
                                                'Screenshot ${index + 1}',
                                                style: GoogleFonts.poppins(
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.w500,
                                                  fontSize: 14,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                }),
                        ),
                        const SizedBox(height: 10),
                        Center(
                          child: AnimatedSmoothIndicator(
                            activeIndex: _currentImageIndex,
                            count: 4,
                            effect: ExpandingDotsEffect(
                              dotHeight: 8,
                              dotWidth: 8,
                              activeDotColor: const Color(0xFF6A11CB),
                              dotColor: Colors.grey[300]!,
                            ),
                          ),
                        ),
                        const SizedBox(height: 10),
                      ],
                    ),
                  ),

                  const SizedBox(height: 10),

                  // Price and action buttons with improved design
                  Container(
                    color: Colors.white,
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            // Price
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Price',
                                    style: GoogleFonts.poppins(
                                      color: Colors.grey[600],
                                      fontSize: 14,
                                    ),
                                  ),
                                  Row(
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    children: [
                                      Text(
                                        (widget.asset?['price'] ?? 0) > 0
                                            ? '\$${widget.asset?['price']}'
                                            : 'Free',
                                        style: GoogleFonts.poppins(
                                          fontSize: 28,
                                          fontWeight: FontWeight.bold,
                                          color: (widget.asset?['price'] ?? 0) > 0
                                              ? Colors.black87
                                              : Colors.green[600],
                                        ),
                                      ),
                                      if ((widget.asset?['price'] ?? 0) > 0 && widget.asset?['originalPrice'] != null)
                                        Padding(
                                          padding: const EdgeInsets.only(left: 8, bottom: 4),
                                          child: Text(
                                            '\$${widget.asset?['originalPrice']}',
                                            style: GoogleFonts.poppins(
                                              fontSize: 16,
                                              decoration: TextDecoration.lineThrough,
                                              color: Colors.grey[500],
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                  if ((widget.asset?['price'] ?? 0) > 0)
                                    Padding(
                                      padding: const EdgeInsets.only(top: 4),
                                      child: Text(
                                        'One-time purchase',
                                        style: GoogleFonts.poppins(
                                          color: Colors.grey[600],
                                          fontSize: 12,
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),

                            // Download/Purchase button
                            SizedBox(
                              height: 50,
                              child: ElevatedButton.icon(
                                onPressed: () {
                                  if ((widget.asset?['price'] ?? 0) > 0) {
                                    if (!_isPurchased) {
                                      // Show purchase dialog
                                      _showPurchaseDialog();
                                    } else {
                                      // Already purchased, download directly
                                      ScaffoldMessenger.of(context).showSnackBar(
                                        SnackBar(
                                          content: Text(
                                            'Downloading asset...',
                                            style: GoogleFonts.poppins(),
                                          ),
                                          behavior: SnackBarBehavior.floating,
                                        ),
                                      );
                                    }
                                  } else {
                                    // Free asset, download directly
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          'Downloading asset...',
                                          style: GoogleFonts.poppins(),
                                        ),
                                        behavior: SnackBarBehavior.floating,
                                      ),
                                    );
                                  }
                                },
                                icon: Icon(
                                  _isPurchased || (widget.asset?['price'] ?? 0) <= 0
                                      ? Icons.download
                                      : Icons.shopping_cart,
                                  color: Colors.white,
                                ),
                                label: Text(
                                  _isPurchased
                                      ? 'Download'
                                      : ((widget.asset?['price'] ?? 0) > 0 ? 'Purchase' : 'Download'),
                                  style: GoogleFonts.poppins(
                                    fontWeight: FontWeight.w600,
                                    fontSize: 16,
                                    color: Colors.white,
                                  ),
                                ),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFF6A11CB),
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 24, vertical: 12),
                                ),
                              ),
                            ),
                          ],
                        ),

                        // Additional action buttons
                        const SizedBox(height: 16),
                        Row(
                          children: [
                            Expanded(
                              child: OutlinedButton.icon(
                                onPressed: () {
                                  // Message publisher logic
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        'Opening chat with ${widget.asset?['author'] ?? 'Author'}...',
                                        style: GoogleFonts.poppins(),
                                      ),
                                      behavior: SnackBarBehavior.floating,
                                    ),
                                  );
                                },
                                icon: const Icon(Icons.message_outlined),
                                label: const Text('Message'),
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: const Color(0xFF6A11CB),
                                  side: const BorderSide(color: Color(0xFF6A11CB)),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  padding: const EdgeInsets.symmetric(vertical: 12),
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: OutlinedButton.icon(
                                onPressed: () {
                                  // Share asset logic
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        'Sharing ${widget.asset?['title'] ?? 'Asset'}...',
                                        style: GoogleFonts.poppins(),
                                      ),
                                      behavior: SnackBarBehavior.floating,
                                    ),
                                  );
                                },
                                icon: const Icon(Icons.share_outlined),
                                label: const Text('Share'),
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: Colors.black87,
                                  side: BorderSide(color: Colors.grey[300]!),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  padding: const EdgeInsets.symmetric(vertical: 12),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 10),

                  // Description section with improved design
                  Container(
                    color: Colors.white,
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Description',
                          style: GoogleFonts.poppins(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Text(
                          widget.asset?['description'] ?? 'This is a premium ${widget.asset?['type'] ?? 'UI Kit'} designed for ${widget.asset?['platform'] ?? 'Flutter'} developers. It includes all the necessary components and features to build a professional application quickly and efficiently. The code is clean, well-documented, and easy to customize to fit your specific needs.\n\nThis asset is perfect for developers who want to save time and create high-quality applications. It comes with comprehensive documentation and regular updates to ensure compatibility with the latest versions of ${widget.asset?['platform'] ?? 'Flutter'}.',
                          style: GoogleFonts.poppins(
                            color: Colors.black87,
                            fontSize: 14,
                            height: 1.6,
                          ),
                        ),

                        // Read more button
                        const SizedBox(height: 12),
                        Center(
                          child: TextButton(
                            onPressed: () {
                              // Show full description logic
                            },
                            child: Text(
                              'Read More',
                              style: GoogleFonts.poppins(
                                color: const Color(0xFF6A11CB),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 10),

                  // Technical details section with improved design
                  Container(
                    width: double.infinity,
                    color: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          child: Text(
                            'Technical Details',
                            style: GoogleFonts.poppins(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Technical details in a grid layout
                        SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 20),
                            child: Row(
                              children: [
                                _buildTechDetailItem(Icons.code, 'Version', widget.asset?['version'] ?? '1.0.0'),
                                const SizedBox(width: 12),
                                _buildTechDetailItem(Icons.calendar_today, 'Updated', widget.asset?['lastUpdated'] ?? 'June 15, 2023'),
                                const SizedBox(width: 12),
                                _buildTechDetailItem(Icons.storage, 'Size', widget.asset?['size'] ?? '2.4 MB'),
                                const SizedBox(width: 12),
                                _buildTechDetailItem(Icons.devices, 'Platform', widget.asset?['platform'] ?? 'Flutter'),
                                const SizedBox(width: 12),
                                _buildTechDetailItem(Icons.verified_user, 'License', widget.asset?['license'] ?? 'Standard License'),
                                const SizedBox(width: 12),
                                _buildTechDetailItem(Icons.support_agent, 'Support', widget.asset?['support'] ?? '6 months'),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 10),

                  // Publisher info with improved design
                  Container(
                    color: Colors.white,
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Publisher',
                          style: GoogleFonts.poppins(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Publisher card with gradient background
                        Container(
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [Color(0xFFF1F1F1), Colors.white],
                            ),
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 10,
                                offset: const Offset(0, 5),
                              ),
                            ],
                          ),
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  // Publisher avatar
                                  GestureDetector(
                                    onTap: () {
                                      // Navigate to publisher profile
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => PublicProfilePage(userId: widget.asset?['authorId']),
                                        ),
                                      );
                                    },
                                    child: Stack(
                                      children: [
                                        CircleAvatar(
                                          radius: 30,
                                          backgroundColor: Colors.grey[200],
                                          child: Text(
                                            widget.asset?['author']?.substring(0, 1) ?? 'A',
                                            style: GoogleFonts.poppins(
                                              fontSize: 24,
                                              fontWeight: FontWeight.bold,
                                              color: const Color(0xFF6A11CB),
                                            ),
                                          ),
                                        ),
                                        if (widget.asset?['verified'] == true)
                                          Positioned(
                                            bottom: 0,
                                            right: 0,
                                            child: Container(
                                              padding: const EdgeInsets.all(2),
                                              decoration: BoxDecoration(
                                                color: Colors.white,
                                                shape: BoxShape.circle,
                                                border: Border.all(color: Colors.white, width: 2),
                                              ),
                                              child: const Icon(
                                                Icons.verified,
                                                color: Color(0xFF6A11CB),
                                                size: 16,
                                              ),
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 16),

                                  // Publisher details
                                  Expanded(
                                    child: GestureDetector(
                                      onTap: () {
                                        // Navigate to publisher profile
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) => PublicProfilePage(userId: widget.asset?['authorId']),
                                          ),
                                        );
                                      },
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            widget.asset?['author'] ?? 'Unknown Author',
                                            style: GoogleFonts.poppins(
                                              fontWeight: FontWeight.w600,
                                              fontSize: 16,
                                            ),
                                          ),
                                          const SizedBox(height: 4),
                                          Row(
                                            children: [
                                              Icon(
                                                Icons.star,
                                                size: 14,
                                                color: Colors.amber[700],
                                              ),
                                              const SizedBox(width: 4),
                                              Text(
                                                '${widget.asset?['publisherRating'] ?? 4.9} (${widget.asset?['publisherReviews'] ?? 124} reviews)',
                                                style: GoogleFonts.poppins(
                                                  color: Colors.grey[700],
                                                  fontSize: 12,
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            'Member since ${widget.asset?['memberSince'] ?? 'Jan 2022'} • ${widget.asset?['publisherAssets'] ?? 24} assets',
                                            style: GoogleFonts.poppins(
                                              color: Colors.grey[600],
                                              fontSize: 12,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),

                                  // Follow button
                                  ElevatedButton(
                                    onPressed: () {
                                      // Follow publisher logic
                                      ScaffoldMessenger.of(context).showSnackBar(
                                        SnackBar(
                                          content: Text(
                                            'Following ${widget.asset?['author'] ?? 'Author'}',
                                            style: GoogleFonts.poppins(),
                                          ),
                                          behavior: SnackBarBehavior.floating,
                                        ),
                                      );
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: const Color(0xFF6A11CB),
                                      foregroundColor: Colors.white,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(20),
                                      ),
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16, vertical: 8),
                                    ),
                                    child: Text(
                                      'Follow',
                                      style: GoogleFonts.poppins(
                                        fontWeight: FontWeight.w600,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),

                        // View profile button
                        const SizedBox(height: 16),
                        SizedBox(
                          width: double.infinity,
                          child: OutlinedButton.icon(
                            onPressed: () {
                              // Navigate to publisher profile
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => PublicProfilePage(userId: widget.asset?['authorId']),
                                ),
                              );
                            },
                            icon: const Icon(Icons.person_outline),
                            label: Text(
                              'View Profile',
                              style: GoogleFonts.poppins(
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                              ),
                            ),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: const Color(0xFF6A11CB),
                              side: const BorderSide(color: Color(0xFF6A11CB)),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 10),

                  // Comments section
                  Container(
                    color: Colors.white,
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Comments',
                              style: GoogleFonts.poppins(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            TextButton(
                              onPressed: () {
                                // View all comments logic
                              },
                              child: Text(
                                'View All',
                                style: GoogleFonts.poppins(
                                  color: const Color(0xFF6A11CB),
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // Comment input field
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(12),
                          ),
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          child: Row(
                            children: [
                              CircleAvatar(
                                radius: 16,
                                backgroundColor: Colors.grey[300],
                                child: const Icon(
                                  Icons.person,
                                  size: 16,
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: TextField(
                                  decoration: InputDecoration(
                                    hintText: 'Add a comment...',
                                    hintStyle: GoogleFonts.poppins(
                                      color: Colors.grey[500],
                                      fontSize: 14,
                                    ),
                                    border: InputBorder.none,
                                  ),
                                ),
                              ),
                              IconButton(
                                icon: const Icon(Icons.send, color: Color(0xFF6A11CB)),
                                onPressed: () {
                                  // Send comment logic
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(
                                      content: Text(
                                        'Comment posted!',
                                        style: GoogleFonts.poppins(),
                                      ),
                                      behavior: SnackBarBehavior.floating,
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 20),

                        // Comments list
                        ...List.generate(_reviews.length > 3 ? 3 : _reviews.length, (index) {
                          final review = _reviews[index];
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 16),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // User avatar
                                CircleAvatar(
                                  radius: 20,
                                  backgroundColor: Colors.grey[200],
                                  child: Text(
                                    review['name'].substring(0, 1),
                                    style: GoogleFonts.poppins(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: const Color(0xFF6A11CB),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 12),

                                // Comment content
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      // User name and date
                                      Row(
                                        children: [
                                          Text(
                                            review['name'],
                                            style: GoogleFonts.poppins(
                                              fontWeight: FontWeight.w600,
                                              fontSize: 14,
                                            ),
                                          ),
                                          const SizedBox(width: 4),
                                          if (review['isVerified'] == true)
                                            const Icon(
                                              Icons.verified,
                                              color: Color(0xFF6A11CB),
                                              size: 14,
                                            ),
                                          const Spacer(),
                                          Text(
                                            review['date'],
                                            style: GoogleFonts.poppins(
                                              color: Colors.grey[500],
                                              fontSize: 12,
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 4),

                                      // Comment text
                                      Text(
                                        review['comment'],
                                        style: GoogleFonts.poppins(
                                          color: Colors.black87,
                                          fontSize: 14,
                                          height: 1.4,
                                        ),
                                      ),
                                      const SizedBox(height: 8),

                                      // Comment actions
                                      Row(
                                        children: [
                                          TextButton.icon(
                                            onPressed: () {
                                              // Like comment logic
                                            },
                                            icon: const Icon(Icons.thumb_up_outlined, size: 14),
                                            label: const Text('Like'),
                                            style: TextButton.styleFrom(
                                              foregroundColor: Colors.grey[700],
                                              padding: const EdgeInsets.symmetric(horizontal: 8),
                                              minimumSize: const Size(0, 30),
                                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                              textStyle: GoogleFonts.poppins(
                                                fontSize: 12,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ),
                                          const SizedBox(width: 16),
                                          TextButton.icon(
                                            onPressed: () {
                                              // Reply to comment logic
                                            },
                                            icon: const Icon(Icons.reply_outlined, size: 14),
                                            label: const Text('Reply'),
                                            style: TextButton.styleFrom(
                                              foregroundColor: Colors.grey[700],
                                              padding: const EdgeInsets.symmetric(horizontal: 8),
                                              minimumSize: const Size(0, 30),
                                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                              textStyle: GoogleFonts.poppins(
                                                fontSize: 12,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          );
                        }),
                      ],
                    ),
                  ),

                  const SizedBox(height: 10),

                  // Related projects section
                  Container(
                    color: Colors.white,
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Related Projects',
                              style: GoogleFonts.poppins(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            TextButton(
                              onPressed: () {
                                // View all related projects logic
                              },
                              child: Text(
                                'View All',
                                style: GoogleFonts.poppins(
                                  color: const Color(0xFF6A11CB),
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // Related projects grid
                        SizedBox(
                          height: 220,
                          child: ListView.builder(
                            scrollDirection: Axis.horizontal,
                            itemCount: _relatedAssets.length,
                            itemBuilder: (context, index) {
                              return GestureDetector(
                                onTap: () {
                                  // Navigate to asset detail page
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => AssetDetailPage(asset: _relatedAssets[index]),
                                    ),
                                  );
                                },
                                child: Container(
                                  width: 180,
                                  margin: const EdgeInsets.only(right: 16),
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.circular(16),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.05),
                                        blurRadius: 10,
                                        offset: const Offset(0, 5),
                                      ),
                                    ],
                                  ),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      // Asset image
                                      ClipRRect(
                                        borderRadius: const BorderRadius.only(
                                          topLeft: Radius.circular(16),
                                          topRight: Radius.circular(16),
                                        ),
                                        child: Container(
                                          height: 120,
                                          width: double.infinity,
                                          color: Colors.grey[200],
                                          child: Stack(
                                            children: [
                                              Center(
                                                child: Icon(
                                                  _getIconForType(_relatedAssets[index]['type']),
                                                  size: 40,
                                                  color: Colors.grey[400],
                                                ),
                                              ),
                                              Positioned(
                                                top: 8,
                                                right: 8,
                                                child: Container(
                                                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                                  decoration: BoxDecoration(
                                                    color: Colors.black.withOpacity(0.6),
                                                    borderRadius: BorderRadius.circular(12),
                                                  ),
                                                  child: Row(
                                                    children: [
                                                      const Icon(
                                                        Icons.star,
                                                        color: Colors.amber,
                                                        size: 14,
                                                      ),
                                                      const SizedBox(width: 4),
                                                      Text(
                                                        _relatedAssets[index]['rating'].toString(),
                                                        style: GoogleFonts.poppins(
                                                          color: Colors.white,
                                                          fontSize: 12,
                                                          fontWeight: FontWeight.w600,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),

                                      // Asset info
                                      Padding(
                                        padding: const EdgeInsets.all(12),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            // Asset title
                                            Text(
                                              _relatedAssets[index]['title'],
                                              style: GoogleFonts.poppins(
                                                fontWeight: FontWeight.w600,
                                                fontSize: 14,
                                              ),
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                            const SizedBox(height: 4),

                                            // Asset author
                                            Text(
                                              'by ${_relatedAssets[index]['author']}',
                                              style: GoogleFonts.poppins(
                                                color: Colors.grey[600],
                                                fontSize: 12,
                                              ),
                                            ),
                                            const SizedBox(height: 8),

                                            // Price
                                            Text(
                                              _relatedAssets[index]['price'] > 0 ? '\$${_relatedAssets[index]['price']}' : 'Free',
                                              style: GoogleFonts.poppins(
                                                color: _relatedAssets[index]['price'] > 0 ? Colors.black87 : Colors.green[600],
                                                fontWeight: FontWeight.w600,
                                                fontSize: 14,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Bottom padding
                  const SizedBox(height: 30),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Show options bottom sheet
  void _showOptionsBottomSheet() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(bottom: 20),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              _buildOptionItem(
                'Share Asset',
                Icons.share_outlined,
                () {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'Sharing ${widget.asset?['title'] ?? 'Asset'}...',
                        style: GoogleFonts.poppins(),
                      ),
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                },
              ),
              _buildOptionItem(
                'Report Asset',
                Icons.flag_outlined,
                () {
                  Navigator.of(context).pop();
                  _showReportDialog();
                },
              ),
              _buildOptionItem(
                'View Publisher Profile',
                Icons.person_outline,
                () {
                  Navigator.of(context).pop();
                  // Navigate to publisher profile
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const PublicProfilePage(),
                    ),
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }

  // Build option item for bottom sheet
  Widget _buildOptionItem(String title, IconData icon, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            Icon(icon, color: Colors.black87, size: 24),
            const SizedBox(width: 16),
            Text(
              title,
              style: GoogleFonts.poppins(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Show report dialog
  void _showReportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Report Asset',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Why are you reporting this asset?',
              style: GoogleFonts.poppins(),
            ),
            const SizedBox(height: 16),
            _buildReportOption('Copyright infringement'),
            _buildReportOption('Inappropriate content'),
            _buildReportOption('Misleading description'),
            _buildReportOption('Malicious code'),
            _buildReportOption('Other'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF6A11CB),
            ),
            child: Text(
              'Submit',
              style: GoogleFonts.poppins(
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build report option
  Widget _buildReportOption(String text) {
    return InkWell(
      onTap: () {},
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            Icon(
              Icons.radio_button_unchecked,
              size: 20,
              color: Colors.grey[600],
            ),
            const SizedBox(width: 12),
            Text(
              text,
              style: GoogleFonts.poppins(),
            ),
          ],
        ),
      ),
    );
  }

  // Build technical detail item for simple row display
  Widget _buildSimpleTechnicalDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: GoogleFonts.poppins(
              color: Colors.grey[700],
              fontSize: 14,
            ),
          ),
          Text(
            value,
            style: GoogleFonts.poppins(
              color: Colors.black87,
              fontWeight: FontWeight.w500,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  // Build rating bar for review summary
  Widget _buildRatingBar(int stars, double percentage) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Text(
            '$stars',
            style: GoogleFonts.poppins(
              color: Colors.grey[600],
              fontSize: 12,
            ),
          ),
          const SizedBox(width: 4),
          const Icon(Icons.star, color: Colors.amber, size: 14),
          const SizedBox(width: 8),
          Expanded(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: LinearProgressIndicator(
                value: percentage,
                backgroundColor: Colors.grey[200],
                valueColor: const AlwaysStoppedAnimation<Color>(Colors.amber),
                minHeight: 8,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '${(percentage * 100).toInt()}%',
            style: GoogleFonts.poppins(
              color: Colors.grey[600],
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  // Build review item
  Widget _buildReviewItem(Map<String, dynamic> review) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // User avatar
              CircleAvatar(
                radius: 20,
                backgroundColor: Colors.grey[200],
                child: Text(
                  review['name'].substring(0, 1),
                  style: GoogleFonts.poppins(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF6A11CB),
                  ),
                ),
              ),
              const SizedBox(width: 12),

              // User details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          review['name'],
                          style: GoogleFonts.poppins(
                            fontWeight: FontWeight.w600,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(width: 4),
                        if (review['isVerified'] == true)
                          const Icon(
                            Icons.verified,
                            color: Color(0xFF6A11CB),
                            size: 14,
                          ),
                      ],
                    ),
                    Row(
                      children: [
                        RatingBar(
                          rating: review['rating'].toDouble(),
                          size: 14,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          review['date'],
                          style: GoogleFonts.poppins(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            review['comment'],
            style: GoogleFonts.poppins(
              color: Colors.black87,
              fontSize: 14,
              height: 1.4,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              TextButton.icon(
                onPressed: () {
                  // Like review logic
                },
                icon: const Icon(Icons.thumb_up_outlined, size: 16),
                label: const Text('Helpful'),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.grey[700],
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  minimumSize: const Size(0, 30),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  textStyle: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              TextButton.icon(
                onPressed: () {
                  // Report review logic
                },
                icon: const Icon(Icons.flag_outlined, size: 16),
                label: const Text('Report'),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.grey[700],
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  minimumSize: const Size(0, 30),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  textStyle: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          if (review != _reviews.last) const Divider(height: 24),
        ],
      ),
    );
  }

  // Build related asset card
  Widget _buildRelatedAssetCard(Map<String, dynamic> asset) {
    return GestureDetector(
      onTap: () {
        // Navigate to asset detail page
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AssetDetailPage(asset: asset),
          ),
        );
      },
      child: Container(
        width: 180,
        margin: const EdgeInsets.only(right: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(10),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Asset image
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
              child: Stack(
                children: [
                  Container(
                    height: 120,
                    width: double.infinity,
                    color: Colors.grey[200],
                    child: Center(
                      child: Icon(
                        _getIconForType(asset['type']),
                        size: 40,
                        color: Colors.grey[400],
                      ),
                    ),
                  ),
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.black.withAlpha(150),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.star,
                            color: Colors.amber,
                            size: 14,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            asset['rating'].toString(),
                            style: GoogleFonts.poppins(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Asset info
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Asset title
                  Text(
                    asset['title'],
                    style: GoogleFonts.poppins(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),

                  // Asset author
                  Text(
                    'by ${asset['author']}',
                    style: GoogleFonts.poppins(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(height: 8),

                  // Price
                  Text(
                    asset['price'] > 0 ? '\$${asset['price']}' : 'Free',
                    style: GoogleFonts.poppins(
                      color: asset['price'] > 0 ? Colors.black87 : Colors.green[600],
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build technical detail item
  Widget _buildTechDetailItem(IconData icon, String label, String value) {
    return Container(
      width: 150,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF6A11CB).withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              size: 18,
              color: const Color(0xFF6A11CB),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: GoogleFonts.poppins(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
                Text(
                  value,
                  style: GoogleFonts.poppins(
                    color: Colors.black87,
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to get icon for asset type
  IconData _getIconForType(String type) {
    switch (type) {
      case 'UI Kits':
        return Icons.palette;
      case 'Templates':
        return Icons.web;
      case 'Blocks':
        return Icons.view_module;
      case 'Libraries':
        return Icons.code;
      case 'Icons':
        return Icons.image;
      case 'Code Snippets':
        return Icons.code;
      default:
        return Icons.extension;
    }
  }

  // Show purchase dialog
  void _showPurchaseDialog() {
    // Local state for the dialog
    String selectedPaymentMethod = _selectedPaymentMethod;
    final double assetPrice = widget.asset?['price'] ?? 0.0;
    final bool canUseWallet = _walletBalance >= assetPrice;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              child: Container(
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.8,
                  maxWidth: MediaQuery.of(context).size.width * 0.9,
                ),
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Purchase Asset',
                          style: GoogleFonts.poppins(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close, size: 20),
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                          onPressed: () => Navigator.pop(context),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),

                    // Scrollable content
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Asset info
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.grey[100],
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                children: [
                                  // Asset icon
                                  Container(
                                    width: 40,
                                    height: 40,
                                    decoration: BoxDecoration(
                                      color: Colors.grey[200],
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Center(
                                      child: Icon(
                                        _getIconForType(widget.asset?['type'] ?? 'UI Kit'),
                                        size: 24,
                                        color: Colors.grey[500],
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 12),

                                  // Asset details
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          widget.asset?['title'] ?? 'Asset Title',
                                          style: GoogleFonts.poppins(
                                            fontWeight: FontWeight.w600,
                                            fontSize: 14,
                                          ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        const SizedBox(height: 2),
                                        Text(
                                          'by ${widget.asset?['author'] ?? 'Unknown Author'}',
                                          style: GoogleFonts.poppins(
                                            color: Colors.grey[600],
                                            fontSize: 12,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                  // Price
                                  Text(
                                    '\$${assetPrice.toStringAsFixed(2)}',
                                    style: GoogleFonts.poppins(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                      color: const Color(0xFF6A11CB),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Payment methods
                            Text(
                              'Payment Method',
                              style: GoogleFonts.poppins(
                                fontWeight: FontWeight.w600,
                                fontSize: 15,
                              ),
                              textAlign: TextAlign.left,
                            ),
                            const SizedBox(height: 12),

                            // ASATU Wallet option
                            Container(
                              margin: const EdgeInsets.only(bottom: 12),
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: selectedPaymentMethod == 'wallet'
                                      ? const Color(0xFF6A11CB)
                                      : Colors.grey[300]!,
                                ),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Column(
                                children: [
                                  RadioListTile<String>(
                                    title: Row(
                                      children: [
                                        Container(
                                          padding: const EdgeInsets.all(8),
                                          decoration: BoxDecoration(
                                            color: const Color(0xFF6A11CB).withAlpha(25),
                                            shape: BoxShape.circle,
                                          ),
                                          child: const Icon(
                                            Icons.account_balance_wallet,
                                            color: Color(0xFF6A11CB),
                                            size: 18,
                                          ),
                                        ),
                                        const SizedBox(width: 12),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                'ASATU Wallet',
                                                style: GoogleFonts.poppins(
                                                  fontWeight: FontWeight.w500,
                                                  fontSize: 13,
                                                ),
                                              ),
                                              Text(
                                                'Balance: \$${_walletBalance.toStringAsFixed(2)}',
                                                style: GoogleFonts.poppins(
                                                  fontSize: 12,
                                                  color: canUseWallet ? Colors.green[700] : Colors.red[700],
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                    value: 'wallet',
                                    groupValue: selectedPaymentMethod,
                                    onChanged: canUseWallet ? (value) {
                                      setState(() {
                                        selectedPaymentMethod = value!;
                                      });
                                    } : null,
                                    activeColor: const Color(0xFF6A11CB),
                                    contentPadding: const EdgeInsets.symmetric(horizontal: 12),
                                  ),
                                  if (!canUseWallet)
                                    Padding(
                                      padding: const EdgeInsets.only(left: 24, right: 24, bottom: 12),
                                      child: Container(
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          color: Colors.red[50],
                                          borderRadius: BorderRadius.circular(8),
                                          border: Border.all(color: Colors.red[100]!),
                                        ),
                                        child: Row(
                                          children: [
                                            Icon(Icons.info_outline, size: 14, color: Colors.red[700]),
                                            const SizedBox(width: 8),
                                            Expanded(
                                              child: Text(
                                                'Insufficient balance',
                                                style: GoogleFonts.poppins(
                                                  fontSize: 11,
                                                  color: Colors.red[700],
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),

                            // Credit card option
                            Container(
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: selectedPaymentMethod == 'credit_card'
                                      ? const Color(0xFF6A11CB)
                                      : Colors.grey[300]!,
                                ),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: RadioListTile<String>(
                                title: Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: const Color(0xFF6A11CB).withAlpha(25),
                                        shape: BoxShape.circle,
                                      ),
                                      child: const Icon(
                                        Icons.credit_card,
                                        color: Color(0xFF6A11CB),
                                        size: 18,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Text(
                                      'Credit Card',
                                      style: GoogleFonts.poppins(
                                        fontWeight: FontWeight.w500,
                                        fontSize: 13,
                                      ),
                                    ),
                                  ],
                                ),
                                value: 'credit_card',
                                groupValue: selectedPaymentMethod,
                                onChanged: (value) {
                                  setState(() {
                                    selectedPaymentMethod = value!;
                                  });
                                },
                                activeColor: const Color(0xFF6A11CB),
                                contentPadding: const EdgeInsets.symmetric(horizontal: 12),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Action buttons
                    Row(
                      children: [
                        // Cancel button
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            style: OutlinedButton.styleFrom(
                              foregroundColor: Colors.black87,
                              side: BorderSide(color: Colors.grey[300]!),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                            child: Text(
                              'Cancel',
                              style: GoogleFonts.poppins(
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),

                        // Confirm button
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              // Process purchase based on selected payment method
                              if (selectedPaymentMethod == 'wallet') {
                                // Show wallet payment confirmation
                                _showWalletPaymentConfirmation(assetPrice);
                              } else {
                                // Process credit card payment
                                Navigator.pop(context);
                                setState(() {
                                  _isPurchased = true;
                                });
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      'Purchase successful!',
                                      style: GoogleFonts.poppins(),
                                    ),
                                    behavior: SnackBarBehavior.floating,
                                  ),
                                );
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF6A11CB),
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                            child: Text(
                              'Confirm',
                              style: GoogleFonts.poppins(
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          }
        );
      },
    );
  }

  // Show wallet payment confirmation dialog
  void _showWalletPaymentConfirmation(double amount) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Text(
            'Confirm Payment',
            style: GoogleFonts.poppins(
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'You are about to pay \$${amount.toStringAsFixed(2)} from your ASATU Wallet.',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withAlpha(25),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue.withAlpha(75)),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.account_balance_wallet,
                      color: Colors.blue,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Current Balance',
                            style: GoogleFonts.poppins(
                              fontWeight: FontWeight.w500,
                              color: Colors.blue[800],
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            '\$${_walletBalance.toStringAsFixed(2)}',
                            style: GoogleFonts.poppins(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: Colors.blue[800],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.amber.withAlpha(25),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.amber.withAlpha(75)),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.shopping_cart,
                      color: Colors.amber,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Purchase Amount',
                            style: GoogleFonts.poppins(
                              fontWeight: FontWeight.w500,
                              color: Colors.amber[800],
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            '\$${amount.toStringAsFixed(2)}',
                            style: GoogleFonts.poppins(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: Colors.amber[800],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withAlpha(25),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.green.withAlpha(75)),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.account_balance_wallet,
                      color: Colors.green,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Remaining Balance',
                            style: GoogleFonts.poppins(
                              fontWeight: FontWeight.w500,
                              color: Colors.green[800],
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            '\$${(_walletBalance - amount).toStringAsFixed(2)}',
                            style: GoogleFonts.poppins(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: Colors.green[800],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: Text(
                'Cancel',
                style: GoogleFonts.poppins(
                  color: Colors.grey[700],
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                // Close both dialogs
                Navigator.pop(context); // Close confirmation dialog
                Navigator.pop(context); // Close purchase dialog

                // Update state to reflect purchase
                setState(() {
                  _isPurchased = true;
                  // In a real app, we would update the wallet balance here
                  // _walletBalance -= amount;
                });

                // Show success message
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Payment successful! \$${amount.toStringAsFixed(2)} has been deducted from your wallet.',
                      style: GoogleFonts.poppins(),
                    ),
                    backgroundColor: Colors.green,
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                'Confirm Payment',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }


}
