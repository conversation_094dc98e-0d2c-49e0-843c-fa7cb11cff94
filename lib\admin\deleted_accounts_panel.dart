import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_fonts/google_fonts.dart';
import '../services/notification_service.dart';
import '../services/background_task_service.dart';

class DeletedAccountsPanel extends StatefulWidget {
  const DeletedAccountsPanel({Key? key}) : super(key: key);

  @override
  State<DeletedAccountsPanel> createState() => _DeletedAccountsPanelState();
}

class _DeletedAccountsPanelState extends State<DeletedAccountsPanel> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final NotificationService _notificationService = NotificationService();

  Map<String, dynamic>? _selectedAccount;
  bool _isProcessing = false;

  // Trigger scheduled deletions manually
  Future<void> _triggerScheduledDeletions() async {
    setState(() {
      _isProcessing = true;
    });

    try {
      // Show confirmation dialog
      final bool? confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(
            'Process Scheduled Deletions',
            style: GoogleFonts.poppins(
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            'This will process all accounts that are scheduled for deletion (30+ days old). This action cannot be undone. Continue?',
            style: GoogleFonts.poppins(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: Text(
                'Cancel',
                style: GoogleFonts.poppins(),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context, true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
              child: Text(
                'Process Deletions',
                style: GoogleFonts.poppins(),
              ),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        // Trigger the background task
        await BackgroundTaskManager.triggerAccountDeletionCheck();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Scheduled deletions processed successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error processing deletions: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Deleted Accounts',
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Manage account deletion requests and process refunds',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),

          // Stats cards and manual trigger button
          Row(
            children: [
              Expanded(child: _buildStatsCards()),
              const SizedBox(width: 16),
              ElevatedButton.icon(
                icon: const Icon(Icons.schedule),
                label: const Text('Process Scheduled Deletions'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
                onPressed: _isProcessing ? null : _triggerScheduledDeletions,
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Deleted accounts list and details
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Left panel - List of deleted accounts
                Expanded(
                  flex: 3,
                  child: _buildDeletedAccountsList(),
                ),

                // Right panel - Selected account details
                Expanded(
                  flex: 4,
                  child: _selectedAccount != null
                      ? _buildAccountDetails()
                      : Center(
                          child: Text(
                            'Select an account to view details',
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              color: Colors.grey[600],
                            ),
                          ),
                        ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Build stats cards
  Widget _buildStatsCards() {
    return StreamBuilder<QuerySnapshot>(
      stream: _firestore.collection('deleted_accounts').snapshots(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Text(
              'Error loading stats: ${snapshot.error}',
              style: GoogleFonts.poppins(color: Colors.red),
            ),
          );
        }

        if (!snapshot.hasData) {
          return const SizedBox();
        }

        final accounts = snapshot.data!.docs;

        // Calculate stats
        int pendingCount = 0;
        int processingCount = 0;
        int completedCount = 0;
        int canceledCount = 0;

        for (final account in accounts) {
          final data = account.data() as Map<String, dynamic>;
          final status = data['status'] as String? ?? 'pending';

          switch (status) {
            case 'pending':
              pendingCount++;
              break;
            case 'processing':
              processingCount++;
              break;
            case 'completed':
              completedCount++;
              break;
            case 'canceled':
              canceledCount++;
              break;
          }
        }

        return Row(
          children: [
            _buildStatCard('Pending', pendingCount, Colors.orange),
            _buildStatCard('Processing', processingCount, Colors.blue),
            _buildStatCard('Completed', completedCount, Colors.green),
            _buildStatCard('Canceled', canceledCount, Colors.red),
          ],
        );
      },
    );
  }

  // Build stat card
  Widget _buildStatCard(String title, int count, Color color) {
    return Expanded(
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: Colors.grey[700],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                count.toString(),
                style: GoogleFonts.poppins(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Build deleted accounts list
  Widget _buildDeletedAccountsList() {
    return StreamBuilder<QuerySnapshot>(
      stream: _firestore
          .collection('deleted_accounts')
          .orderBy('deletionRequestedAt', descending: true)
          .snapshots(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Text(
              'Error loading accounts: ${snapshot.error}',
              style: GoogleFonts.poppins(color: Colors.red),
            ),
          );
        }

        if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
          return Center(
            child: Text(
              'No deleted accounts found',
              style: GoogleFonts.poppins(fontSize: 16),
            ),
          );
        }

        final accounts = snapshot.data!.docs;

        return ListView.builder(
          itemCount: accounts.length,
          itemBuilder: (context, index) {
            final account = accounts[index].data() as Map<String, dynamic>;
            account['id'] = accounts[index].id;

            final userName = account['userName'] ?? 'Unknown User';
            final userEmail = account['userEmail'] ?? 'No email';
            final status = account['status'] ?? 'pending';
            final timestamp = (account['deletionRequestedAt'] as Timestamp?)?.toDate() ?? DateTime.now();
            final scheduledDeletionDate = (account['scheduledDeletionDate'] as Timestamp?)?.toDate();

            final isSelected = _selectedAccount != null && _selectedAccount!['id'] == account['id'];

            return Card(
              elevation: isSelected ? 4 : 1,
              margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(
                  color: isSelected ? Colors.blue : Colors.transparent,
                  width: 2,
                ),
              ),
              child: ListTile(
                onTap: () {
                  setState(() {
                    _selectedAccount = account;
                  });
                },
                leading: CircleAvatar(
                  backgroundColor: _getStatusColor(status).withOpacity(0.2),
                  child: Icon(
                    _getStatusIcon(status),
                    color: _getStatusColor(status),
                  ),
                ),
                title: Text(
                  userName,
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 4),
                    Text(
                      userEmail,
                      style: GoogleFonts.poppins(fontSize: 12),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Status: ${_getStatusText(status)}',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: _getStatusColor(status),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Requested: ${_formatDate(timestamp)}',
                      style: GoogleFonts.poppins(fontSize: 12, color: Colors.grey[600]),
                    ),
                    if (scheduledDeletionDate != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        'Scheduled deletion: ${_formatDate(scheduledDeletionDate)}',
                        style: GoogleFonts.poppins(fontSize: 12, color: Colors.grey[600]),
                      ),
                    ],
                  ],
                ),
                trailing: Icon(
                  Icons.chevron_right,
                  color: isSelected ? Colors.blue : Colors.grey,
                ),
              ),
            );
          },
        );
      },
    );
  }

  // Build account details
  Widget _buildAccountDetails() {
    if (_selectedAccount == null) {
      return const SizedBox();
    }

    final userName = _selectedAccount!['userName'] ?? 'Unknown User';
    final userEmail = _selectedAccount!['userEmail'] ?? 'No email';
    final userId = _selectedAccount!['userId'] ?? '';
    final status = _selectedAccount!['status'] ?? 'pending';
    final timestamp = (_selectedAccount!['deletionRequestedAt'] as Timestamp?)?.toDate() ?? DateTime.now();
    final scheduledDeletionDate = (_selectedAccount!['scheduledDeletionDate'] as Timestamp?)?.toDate();
    final availableBalance = _selectedAccount!['availableBalance'] ?? 0.0;
    final bankDetails = _selectedAccount!['bankDetails'] as Map<String, dynamic>? ?? {};

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: _getStatusColor(status).withOpacity(0.2),
                  radius: 24,
                  child: Icon(
                    _getStatusIcon(status),
                    color: _getStatusColor(status),
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        userName,
                        style: GoogleFonts.poppins(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        userEmail,
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Chip(
                  label: Text(
                    _getStatusText(status),
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  backgroundColor: _getStatusColor(status),
                ),
              ],
            ),
            const Divider(height: 32),

            // Account details
            _buildInfoSection('Account Details', [
              _buildInfoRow('User ID', userId),
              _buildInfoRow('Deletion Requested', _formatDateTime(timestamp)),
              if (scheduledDeletionDate != null)
                _buildInfoRow('Scheduled Deletion', _formatDateTime(scheduledDeletionDate)),
              _buildInfoRow('Available Balance', '\$${availableBalance.toStringAsFixed(2)}'),
            ]),

            const SizedBox(height: 24),

            // Bank details
            _buildInfoSection('Bank Details', [
              _buildInfoRow('Bank Name', bankDetails['bankName'] ?? 'Not provided'),
              _buildInfoRow('Account Number', bankDetails['accountNumber'] ?? 'Not provided'),
              _buildInfoRow('Account Holder', bankDetails['accountHolderName'] ?? 'Not provided'),
              _buildInfoRow('IFSC/Routing Code', bankDetails['routingCode'] ?? 'Not provided'),
            ]),

            const Spacer(),

            // Action buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  // Build info section
  Widget _buildInfoSection(String title, List<Widget> rows) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
        const SizedBox(height: 12),
        ...rows,
      ],
    );
  }

  // Build info row
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 150,
            child: Text(
              label,
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build action buttons
  Widget _buildActionButtons() {
    if (_selectedAccount == null) {
      return const SizedBox();
    }

    final status = _selectedAccount!['status'] ?? 'pending';

    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (status == 'pending') ...[
          OutlinedButton.icon(
            icon: const Icon(Icons.cancel),
            label: const Text('Cancel Deletion'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.red,
              side: const BorderSide(color: Colors.red),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            onPressed: _isProcessing ? null : () => _showCancelDeletionDialog(),
          ),
          const SizedBox(width: 16),
          ElevatedButton.icon(
            icon: const Icon(Icons.check_circle),
            label: const Text('Process Deletion'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            onPressed: _isProcessing ? null : () => _showProcessDeletionDialog(),
          ),
        ] else if (status == 'processing') ...[
          ElevatedButton.icon(
            icon: const Icon(Icons.check_circle),
            label: const Text('Complete Deletion'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            onPressed: _isProcessing ? null : () => _showCompleteDeletionDialog(),
          ),
        ],
      ],
    );
  }

  // Show cancel deletion dialog
  void _showCancelDeletionDialog() {
    if (_selectedAccount == null) return;

    final userName = _selectedAccount!['userName'] ?? 'this user';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Cancel Account Deletion',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          'Are you sure you want to cancel the account deletion request for $userName? The user will be notified and their account will be restored.',
          style: GoogleFonts.poppins(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'No, Keep Pending',
              style: GoogleFonts.poppins(),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _cancelDeletion();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text(
              'Yes, Cancel Deletion',
              style: GoogleFonts.poppins(),
            ),
          ),
        ],
      ),
    );
  }

  // Show process deletion dialog
  void _showProcessDeletionDialog() {
    if (_selectedAccount == null) return;

    final userName = _selectedAccount!['userName'] ?? 'this user';
    final availableBalance = _selectedAccount!['availableBalance'] ?? 0.0;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Process Account Deletion',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'You are about to process the account deletion request for $userName.',
              style: GoogleFonts.poppins(),
            ),
            const SizedBox(height: 16),
            if (availableBalance > 0) ...[
              Text(
                'This account has an available balance of \$${availableBalance.toStringAsFixed(2)} that will need to be refunded.',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w500,
                  color: Colors.blue,
                ),
              ),
              const SizedBox(height: 16),
            ],
            Text(
              'The account will be marked as "processing" and the user will be notified. You will need to complete any necessary refunds before finalizing the deletion.',
              style: GoogleFonts.poppins(),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _processDeletion();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: Text(
              'Process Deletion',
              style: GoogleFonts.poppins(),
            ),
          ),
        ],
      ),
    );
  }

  // Show complete deletion dialog
  void _showCompleteDeletionDialog() {
    if (_selectedAccount == null) return;

    final userName = _selectedAccount!['userName'] ?? 'this user';
    final availableBalance = _selectedAccount!['availableBalance'] ?? 0.0;

    final TextEditingController refundConfirmController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Complete Account Deletion',
          style: GoogleFonts.poppins(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'You are about to permanently delete the account for $userName.',
              style: GoogleFonts.poppins(),
            ),
            const SizedBox(height: 16),
            if (availableBalance > 0) ...[
              Text(
                'Please confirm that you have processed the refund of \$${availableBalance.toStringAsFixed(2)} to the user\'s bank account.',
                style: GoogleFonts.poppins(
                  fontWeight: FontWeight.w500,
                  color: Colors.orange,
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: refundConfirmController,
                decoration: InputDecoration(
                  labelText: 'Type "REFUNDED" to confirm',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],
            Text(
              'This action is irreversible. The user\'s data will be permanently deleted from the system.',
              style: GoogleFonts.poppins(
                color: Colors.red,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: GoogleFonts.poppins(),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              if (availableBalance > 0 && refundConfirmController.text.trim() != 'REFUNDED') {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Please type "REFUNDED" to confirm you have processed the refund'),
                    backgroundColor: Colors.red,
                  ),
                );
                return;
              }

              Navigator.pop(context);
              _completeDeletion();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: Text(
              'Complete Deletion',
              style: GoogleFonts.poppins(),
            ),
          ),
        ],
      ),
    );
  }

  // Cancel deletion
  Future<void> _cancelDeletion() async {
    if (_selectedAccount == null) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      final userId = _selectedAccount!['userId'];
      final accountId = _selectedAccount!['id'];

      // Update deleted_accounts record
      await _firestore.collection('deleted_accounts').doc(accountId).update({
        'status': 'canceled',
        'canceledAt': FieldValue.serverTimestamp(),
        'canceledBy': _auth.currentUser?.uid,
      });

      // Update user record
      await _firestore.collection('users').doc(userId).update({
        'accountStatus': 'active',
        'deletionRequestedAt': FieldValue.delete(),
        'scheduledDeletionDate': FieldValue.delete(),
      });

      // Send notification to user
      await _notificationService.sendNotification(
        userId: userId,
        title: 'Account Deletion Canceled',
        body: 'Your account deletion request has been canceled. Your account has been restored.',
        type: 'account',
      );

      // Log admin action
      await _firestore.collection('admin_activity_log').add({
        'action': 'Cancel Account Deletion',
        'targetUserId': userId,
        'performedBy': _auth.currentUser?.uid,
        'performedByEmail': _auth.currentUser?.email,
        'timestamp': FieldValue.serverTimestamp(),
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Account deletion canceled successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error canceling deletion: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  // Process deletion
  Future<void> _processDeletion() async {
    if (_selectedAccount == null) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      final userId = _selectedAccount!['userId'];
      final accountId = _selectedAccount!['id'];

      // Update deleted_accounts record
      await _firestore.collection('deleted_accounts').doc(accountId).update({
        'status': 'processing',
        'processingStartedAt': FieldValue.serverTimestamp(),
        'processingBy': _auth.currentUser?.uid,
      });

      // Send notification to user
      await _notificationService.sendNotification(
        userId: userId,
        title: 'Account Deletion Processing',
        body: 'Your account deletion request is now being processed. We will notify you once it is complete.',
        type: 'account',
      );

      // Log admin action
      await _firestore.collection('admin_activity_log').add({
        'action': 'Process Account Deletion',
        'targetUserId': userId,
        'performedBy': _auth.currentUser?.uid,
        'performedByEmail': _auth.currentUser?.email,
        'timestamp': FieldValue.serverTimestamp(),
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Account deletion is now being processed'),
            backgroundColor: Colors.blue,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error processing deletion: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  // Complete deletion
  Future<void> _completeDeletion() async {
    if (_selectedAccount == null) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      final userId = _selectedAccount!['userId'];
      final accountId = _selectedAccount!['id'];
      final userEmail = _selectedAccount!['userEmail'];

      // Update deleted_accounts record
      await _firestore.collection('deleted_accounts').doc(accountId).update({
        'status': 'completed',
        'completedAt': FieldValue.serverTimestamp(),
        'completedBy': _auth.currentUser?.uid,
      });

      // Delete user data (or mark as deleted)
      // Note: In a real application, you might want to anonymize the data instead of deleting it
      await _firestore.collection('users').doc(userId).update({
        'accountStatus': 'deleted',
        'email': 'deleted_${DateTime.now().millisecondsSinceEpoch}@deleted.com',
        'displayName': 'Deleted User',
        'isDeleted': true,
        'deletedAt': FieldValue.serverTimestamp(),
        'deletedBy': _auth.currentUser?.uid,
      });

      // Send email notification (would typically be handled by a Cloud Function)
      debugPrint('Sending account deletion completion email to $userEmail');

      // Log admin action
      await _firestore.collection('admin_activity_log').add({
        'action': 'Complete Account Deletion',
        'targetUserId': userId,
        'performedBy': _auth.currentUser?.uid,
        'performedByEmail': _auth.currentUser?.email,
        'timestamp': FieldValue.serverTimestamp(),
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Account deletion completed successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error completing deletion: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
          _selectedAccount = null; // Clear selection after completion
        });
      }
    }
  }

  // Helper methods
  Color _getStatusColor(String status) {
    switch (status) {
      case 'pending':
        return Colors.orange;
      case 'processing':
        return Colors.blue;
      case 'completed':
        return Colors.green;
      case 'canceled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'pending':
        return Icons.hourglass_empty;
      case 'processing':
        return Icons.sync;
      case 'completed':
        return Icons.check_circle;
      case 'canceled':
        return Icons.cancel;
      default:
        return Icons.help_outline;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'processing':
        return 'Processing';
      case 'completed':
        return 'Completed';
      case 'canceled':
        return 'Canceled';
      default:
        return 'Unknown';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateTime(DateTime date) {
    return '${date.day}/${date.month}/${date.year} at ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }
}
