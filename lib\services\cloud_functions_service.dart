import 'package:cloud_functions/cloud_functions.dart';
import 'package:flutter/foundation.dart';

/// A service class to interact with Firebase Cloud Functions
class CloudFunctionsService {
  // Singleton pattern
  static final CloudFunctionsService _instance = CloudFunctionsService._internal();
  factory CloudFunctionsService() => _instance;
  CloudFunctionsService._internal();

  // Firebase Functions instance
  final FirebaseFunctions _functions = FirebaseFunctions.instance;

  // Admin: Add earnings to a user's balance
  Future<Map<String, dynamic>> addEarnings({
    required String userId,
    required double amount,
    required String reason,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final callable = _functions.httpsCallable('addEarnings');
      final result = await callable.call({
        'userId': userId,
        'amount': amount,
        'reason': reason,
        'metadata': metadata ?? {},
      });
      
      return result.data;
    } catch (e) {
      debugPrint('Error adding earnings: $e');
      rethrow;
    }
  }

  // Request a withdrawal
  Future<Map<String, dynamic>> requestWithdrawal({
    required double amount,
    required String paymentMethod,
    required Map<String, dynamic> paymentDetails,
  }) async {
    try {
      final callable = _functions.httpsCallable('requestWithdrawal');
      final result = await callable.call({
        'amount': amount,
        'paymentMethod': paymentMethod,
        'paymentDetails': paymentDetails,
      });
      
      return result.data;
    } catch (e) {
      debugPrint('Error requesting withdrawal: $e');
      rethrow;
    }
  }

  // Admin: Process a withdrawal
  Future<Map<String, dynamic>> processWithdrawal({
    required String withdrawalId,
    required String action, // 'approve' or 'reject'
    String? notes,
  }) async {
    try {
      final callable = _functions.httpsCallable('processWithdrawal');
      final result = await callable.call({
        'withdrawalId': withdrawalId,
        'action': action,
        'notes': notes,
      });
      
      return result.data;
    } catch (e) {
      debugPrint('Error processing withdrawal: $e');
      rethrow;
    }
  }
}
