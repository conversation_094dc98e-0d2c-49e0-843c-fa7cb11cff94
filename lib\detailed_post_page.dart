import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:async';
import 'services/user_content_service.dart';
import 'services/data_performance_service.dart';
import 'services/post_update_service.dart';
import 'widgets/mention_text.dart';

class DetailedPostPage extends StatefulWidget {
  final Map<String, dynamic> post;

  const DetailedPostPage({super.key, required this.post});

  @override
  State<DetailedPostPage> createState() => _DetailedPostPageState();
}

class _DetailedPostPageState extends State<DetailedPostPage> {
  final TextEditingController _commentController = TextEditingController();
  final DataPerformanceService _performanceService = DataPerformanceService();
  final PostUpdateService _postUpdateService = PostUpdateService();
  List<Map<String, dynamic>> _comments = [];
  bool _isLoadingComments = false;
  StreamSubscription<Map<String, dynamic>>? _commentUpdateSubscription;

  // Debounce mechanism for likes
  bool _isLikingPost = false;
  final Set<String> _likingComments = <String>{};

  @override
  void initState() {
    super.initState();
    _loadFullPostData();
    _loadComments();
    _loadPostLikeStatus();
    _setupCommentUpdateListener();
  }

  // Setup listener for comment updates from other pages
  void _setupCommentUpdateListener() {
    _commentUpdateSubscription = _postUpdateService.commentUpdates.listen((updateData) {
      final String postId = updateData['postId'];
      final String commentId = updateData['commentId'];
      final Map<String, dynamic> updates = updateData['updates'];

      // Only update if this is for the current post
      if (postId == widget.post['id']) {
        // Find the comment in the current list and update it
        final commentIndex = _comments.indexWhere((comment) => comment['id'] == commentId);
        if (commentIndex != -1 && mounted) {
          setState(() {
            _comments[commentIndex].addAll(updates);
          });
          debugPrint('DetailedPostPage: Updated comment $commentId with $updates');
        }
      }
    });
  }

  @override
  void dispose() {
    _commentController.dispose();
    _commentUpdateSubscription?.cancel();
    super.dispose();
  }

  Future<void> _loadComments() async {
    if (!mounted) return;

    setState(() {
      _isLoadingComments = true;
    });

    try {
      debugPrint('DetailedPostPage: Starting ultra-fast comment loading for post ${widget.post['id']}');

      // Use performance service for ultra-fast loading
      final comments = await _performanceService.getPostComments(widget.post['id']);

      debugPrint('DetailedPostPage: Loaded ${comments.length} comments with performance service');

      if (mounted) {
        setState(() {
          _comments = comments;
          _isLoadingComments = false;
        });
        debugPrint('DetailedPostPage: Updated UI with ${_comments.length} comments');
      }
    } catch (e) {
      debugPrint('DetailedPostPage: Error loading comments: $e');
      if (mounted) {
        setState(() {
          _isLoadingComments = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black87),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Post Details',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),

      ),
      body: Column(
        children: [
          // Post content
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Post header and content
                  Container(
                    color: Colors.white,
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Author info
                        Row(
                          children: [
                            CircleAvatar(
                              radius: 24,
                              backgroundImage: widget.post['authorImage'] != null && widget.post['authorImage'].isNotEmpty
                                  ? CachedNetworkImageProvider(widget.post['authorImage'])
                                  : null,
                              backgroundColor: Colors.blue[100],
                              child: widget.post['authorImage'] == null || widget.post['authorImage'].isEmpty
                                  ? Text(
                                      (widget.post['author'] ?? 'U').substring(0, 1).toUpperCase(),
                                      style: GoogleFonts.poppins(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.blue[700],
                                      ),
                                    )
                                  : null,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    widget.post['author'] ?? 'Unknown',
                                    style: GoogleFonts.poppins(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black87,
                                    ),
                                  ),
                                  Text(
                                    widget.post['time'] ?? 'Unknown time',
                                    style: GoogleFonts.poppins(
                                      fontSize: 12,
                                      color: Colors.black54,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // Post content
                        _buildPostContent(),

                        // Post image if available
                        if (widget.post['hasImage'] == true && widget.post['imageUrl'] != null)
                          Padding(
                            padding: const EdgeInsets.only(top: 16),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(12),
                              child: CachedNetworkImage(
                                imageUrl: widget.post['imageUrl'],
                                width: double.infinity,
                                fit: BoxFit.cover,
                                placeholder: (context, url) => Container(
                                  height: 200,
                                  color: Colors.grey[200],
                                  child: const Center(child: CircularProgressIndicator()),
                                ),
                                errorWidget: (context, url, error) => Container(
                                  height: 200,
                                  color: Colors.grey[200],
                                  child: const Icon(Icons.error),
                                ),
                              ),
                            ),
                          ),

                        const SizedBox(height: 16),

                        // Post stats and actions
                        Row(
                          children: [
                            InkWell(
                              onTap: _togglePostLike,
                              child: Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      widget.post['isLiked'] == true ? Icons.favorite : Icons.favorite_border,
                                      size: 20,
                                      color: widget.post['isLiked'] == true ? Colors.red : Colors.grey[600],
                                    ),
                                    const SizedBox(width: 6),
                                    Text(
                                      '${widget.post['likes'] ?? 0}',
                                      style: GoogleFonts.poppins(
                                        fontSize: 14,
                                        color: widget.post['isLiked'] == true ? Colors.red : Colors.black54,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(width: 20),
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.comment,
                                  size: 20,
                                  color: Colors.blue[400],
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  '${_comments.length}',
                                  style: GoogleFonts.poppins(
                                    fontSize: 14,
                                    color: Colors.black54,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Comments section
                  Container(
                    color: Colors.white,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(16),
                          child: Text(
                            'Comments (${_comments.length})',
                            style: GoogleFonts.poppins(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),
                        ),
                        if (_isLoadingComments)
                          const Padding(
                            padding: EdgeInsets.all(16),
                            child: Center(child: CircularProgressIndicator()),
                          )
                        else if (_comments.isEmpty)
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: Text(
                              'No comments yet. Be the first to comment!',
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                          )
                        else
                          ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: _comments.length,
                            itemBuilder: (context, index) {
                              return _buildCommentItem(_comments[index]);
                            },
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Comment input
          _buildCommentInput(),
        ],
      ),
    );
  }

  Widget _buildPostContent() {
    final String postType = widget.post['type'] ?? 'regular';

    switch (postType) {
      case 'poll':
        return _buildPollContent();
      case 'code':
        return _buildCodeContent();
      default:
        return _buildRegularContent();
    }
  }

  Widget _buildRegularContent() {
    if (widget.post['content'] == null || widget.post['content'].isEmpty) {
      return const SizedBox.shrink();
    }

    return Text(
      widget.post['content'],
      style: GoogleFonts.poppins(
        fontSize: 16,
        color: Colors.black87,
        height: 1.6,
      ),
    );
  }

  Widget _buildPollContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.post['content'] != null && widget.post['content'].isNotEmpty)
          Text(
            widget.post['content'],
            style: GoogleFonts.poppins(
              fontSize: 16,
              color: Colors.black87,
              height: 1.6,
            ),
          ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.blue[200]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.poll, color: Colors.blue[600], size: 24),
                  const SizedBox(width: 8),
                  Text(
                    'Poll',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      color: Colors.blue[700],
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              // Show poll options if available
              if (widget.post['pollOptions'] != null)
                ...List.generate(
                  (widget.post['pollOptions'] as List).length,
                  (index) {
                    final option = widget.post['pollOptions'][index];
                    return Container(
                      margin: const EdgeInsets.only(bottom: 8),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue[300]!),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.radio_button_unchecked, color: Colors.blue[600], size: 16),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              option is Map ? (option['text'] ?? option.toString()) : option.toString(),
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                color: Colors.blue[700],
                              ),
                            ),
                          ),
                          if (option is Map && option['votes'] != null)
                            Text(
                              '${option['votes']} votes',
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                color: Colors.blue[600],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                        ],
                      ),
                    );
                  },
                ),
              // Show expiry date if available
              if (widget.post['expiryDate'] != null)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Text(
                    'Expires: ${_formatExpiryDate(widget.post['expiryDate'])}',
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      color: Colors.blue[600],
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCodeContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.post['content'] != null && widget.post['content'].isNotEmpty)
          Text(
            widget.post['content'],
            style: GoogleFonts.poppins(
              fontSize: 16,
              color: Colors.black87,
              height: 1.6,
            ),
          ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.code, color: Colors.grey[700], size: 24),
                  const SizedBox(width: 8),
                  Text(
                    'Code',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      color: Colors.grey[700],
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              // Show code content if available
              if (widget.post['codeContent'] != null && widget.post['codeContent'].isNotEmpty)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Code:',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            color: Colors.grey[700],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        IconButton(
                          onPressed: () => _copyCodeToClipboard(widget.post['codeContent']),
                          icon: Icon(
                            Icons.copy,
                            size: 18,
                            color: Colors.grey[600],
                          ),
                          tooltip: 'Copy code',
                        ),
                      ],
                    ),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.black87,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        widget.post['codeContent'],
                        style: GoogleFonts.sourceCodePro(
                          fontSize: 13,
                          color: Colors.green[300],
                          height: 1.4,
                        ),
                      ),
                    ),
                  ],
                )
              else
                Text(
                  'No code content available',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.grey[600],
                    fontStyle: FontStyle.italic,
                  ),
                ),
              // Show programming language if available
              if (widget.post['codeLanguage'] != null && widget.post['codeLanguage'].isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 8),
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      widget.post['codeLanguage'],
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCommentItem(Map<String, dynamic> comment) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CircleAvatar(
            radius: 16,
            backgroundImage: comment['userImage'] != null && comment['userImage'].isNotEmpty
                ? CachedNetworkImageProvider(comment['userImage'])
                : null,
            backgroundColor: Colors.grey[300],
            child: comment['userImage'] == null || comment['userImage'].isEmpty
                ? Text(
                    (comment['userName'] ?? 'U').substring(0, 1).toUpperCase(),
                    style: GoogleFonts.poppins(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  )
                : null,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      comment['userName'] ?? 'Unknown',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _formatCommentTime(comment['createdAt']),
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                MentionText(
                  text: comment['content'] ?? '',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.black87,
                    height: 1.4,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    InkWell(
                      onTap: () async {
                        await _toggleCommentLike(comment);
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              comment['isLiked'] == true ? Icons.favorite : Icons.favorite_border,
                              size: 16,
                              color: comment['isLiked'] == true ? Colors.red : Colors.grey[600],
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${comment['likesCount'] ?? 0}',
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                color: comment['isLiked'] == true ? Colors.red : Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    InkWell(
                      onTap: () {
                        _showReplyDialog(comment);
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.reply,
                              size: 16,
                              color: Colors.grey[600],
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Reply',
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommentInput() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.only(
        left: 16,
        right: 16,
        top: 12,
        bottom: MediaQuery.of(context).viewInsets.bottom + 12,
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 16,
            backgroundColor: Colors.blue[100],
            child: FutureBuilder<String?>(
              future: _getCurrentUserImage(),
              builder: (context, snapshot) {
                final userImage = snapshot.data;
                return userImage != null && userImage.isNotEmpty
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(16),
                        child: Image.network(
                          userImage,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Icon(
                              Icons.person,
                              size: 16,
                              color: Colors.blue[700],
                            );
                          },
                        ),
                      )
                    : Icon(
                        Icons.person,
                        size: 16,
                        color: Colors.blue[700],
                      );
              },
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: TextField(
              controller: _commentController,
              decoration: InputDecoration(
                hintText: 'Add a comment...',
                hintStyle: GoogleFonts.poppins(color: Colors.grey[500]),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              maxLines: null,
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: _addComment,
            icon: Icon(
              Icons.send,
              color: Colors.blue[600],
            ),
          ),
        ],
      ),
    );
  }

  Future<String?> _getCurrentUserImage() async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return null;

      if (currentUser.photoURL != null && currentUser.photoURL!.isNotEmpty) {
        return currentUser.photoURL;
      }

      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (userDoc.exists) {
        final userData = userDoc.data() as Map<String, dynamic>;
        final firestoreImage = userData['photoURL'];
        if (firestoreImage != null && firestoreImage.toString().isNotEmpty) {
          return firestoreImage;
        }
      }

      if (currentUser.email != null) {
        return 'https://ui-avatars.com/api/?name=${Uri.encodeComponent(currentUser.email!)}&background=4F46E5&color=fff&size=128';
      }

      return null;
    } catch (e) {
      debugPrint('Error fetching current user image: $e');
      return FirebaseAuth.instance.currentUser?.photoURL;
    }
  }

  String _formatCommentTime(dynamic timestamp) {
    try {
      DateTime dateTime;
      if (timestamp is Timestamp) {
        dateTime = timestamp.toDate();
      } else if (timestamp is DateTime) {
        dateTime = timestamp;
      } else {
        return 'Unknown time';
      }

      final now = DateTime.now();
      final difference = now.difference(dateTime);

      if (difference.inMinutes < 1) {
        return 'Just now';
      } else if (difference.inHours < 1) {
        return '${difference.inMinutes}m';
      } else if (difference.inDays < 1) {
        return '${difference.inHours}h';
      } else if (difference.inDays < 7) {
        return '${difference.inDays}d';
      } else {
        return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
      }
    } catch (e) {
      return 'Unknown time';
    }
  }

  Future<void> _toggleCommentLike(Map<String, dynamic> comment) async {
    final commentId = comment['id'];

    // Prevent multiple taps
    if (_likingComments.contains(commentId)) {
      return;
    }

    _likingComments.add(commentId);

    try {
      // Use ultra-fast performance service
      final bool isLiked = await _performanceService.toggleCommentLike(commentId);

      if (mounted) {
        setState(() {
          comment['isLiked'] = isLiked;
          // Fix negative count issue
          final currentLikes = comment['likesCount'] ?? 0;
          if (isLiked) {
            comment['likesCount'] = currentLikes + 1;
          } else {
            comment['likesCount'] = (currentLikes > 0) ? currentLikes - 1 : 0;
          }
        });

        // Broadcast the comment like update to other pages
        _postUpdateService.updateCommentLike(
          widget.post['id'],
          commentId,
          isLiked,
          comment['likesCount']
        );
      }
    } catch (e) {
      debugPrint('Error toggling comment like: $e');
    } finally {
      // Remove from debounce set after a delay
      Future.delayed(const Duration(milliseconds: 500), () {
        _likingComments.remove(commentId);
      });
    }
  }

  void _showReplyDialog(Map<String, dynamic> comment) {
    // Use the unique username field, not the display name
    final String username = comment['username'] ?? comment['userName'] ?? comment['authorName'] ?? 'User';
    final String displayName = comment['userName'] ?? comment['authorName'] ?? username;

    // Set the comment input to mention the user using their unique username
    _commentController.text = '@$username ';
    _commentController.selection = TextSelection.fromPosition(
      TextPosition(offset: _commentController.text.length),
    );

    // Show a brief message with display name but mention with username
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'Replying to $displayName (@$username)',
          style: GoogleFonts.poppins(),
        ),
        backgroundColor: Colors.blue[600],
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 1),
      ),
    );
  }

  // Load comment like statuses in background
  Future<void> _loadCommentLikeStatuses() async {
    if (!mounted) return;

    try {
      final userContentService = Provider.of<UserContentService>(context, listen: false);

      for (int i = 0; i < _comments.length; i++) {
        if (!mounted) break;

        final comment = _comments[i];
        try {
          final isLiked = await userContentService.hasLikedComment(comment['id']);

          if (mounted) {
            setState(() {
              _comments[i]['isLiked'] = isLiked;
            });
          }
        } catch (e) {
          debugPrint('Error checking like status for comment ${comment['id']}: $e');
        }
      }
    } catch (e) {
      debugPrint('Error loading comment like statuses: $e');
    }
  }

  Future<void> _addComment() async {
    final String commentText = _commentController.text.trim();

    if (commentText.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Please enter a comment',
            style: GoogleFonts.poppins(),
          ),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    try {
      debugPrint('DetailedPostPage: Adding comment: "$commentText" to post ${widget.post['id']}');

      final userContentService = Provider.of<UserContentService>(context, listen: false);
      final result = await userContentService.addComment(widget.post['id'], commentText);

      debugPrint('DetailedPostPage: Add comment result: $result');

      if (result['success']) {
        _commentController.clear();

        debugPrint('DetailedPostPage: Comment added successfully, adding to UI directly...');

        // Get current user's username from Firestore
        String currentUsername = 'user';
        try {
          final currentUser = FirebaseAuth.instance.currentUser;
          if (currentUser != null) {
            final userDoc = await FirebaseFirestore.instance
                .collection('users')
                .doc(currentUser.uid)
                .get();

            if (userDoc.exists) {
              final userData = userDoc.data() as Map<String, dynamic>;
              currentUsername = userData['username'] ?? 'user';
            }
          }
        } catch (e) {
          debugPrint('Error fetching current user username: $e');
        }

        // Create the new comment object
        final newComment = {
          'id': result['commentId'] ?? DateTime.now().millisecondsSinceEpoch.toString(),
          'content': commentText,
          'userName': FirebaseAuth.instance.currentUser?.displayName ?? 'You',
          'username': currentUsername, // Add unique username field
          'userImage': FirebaseAuth.instance.currentUser?.photoURL ?? '',
          'userId': FirebaseAuth.instance.currentUser?.uid ?? '',
          'postId': widget.post['id'],
          'likesCount': 0,
          'likes': 0,
          'createdAt': Timestamp.now(),
          'isLiked': false,
        };

        // Update post comment count in UI and add new comment
        if (mounted) {
          setState(() {
            final currentCommentCount = widget.post['comments'] ?? 0;
            widget.post['comments'] = currentCommentCount + 1;
            widget.post['commentsCount'] = currentCommentCount + 1;

            // Add new comment to the beginning of the list (newest first)
            _comments.insert(0, newComment);
          });

          // Broadcast the update to other pages
          _postUpdateService.updateCommentCount(widget.post['id'], widget.post['comments']);
          _postUpdateService.addComment(widget.post['id'], newComment);

          // Clear cache for this post's comments
          _performanceService.clearCache('comments_${widget.post['id']}');

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Comment added successfully!',
                style: GoogleFonts.poppins(),
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              duration: const Duration(seconds: 1),
            ),
          );
        }
      } else {
        debugPrint('DetailedPostPage: Failed to add comment: ${result['error']}');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to add comment: ${result['error']}',
                style: GoogleFonts.poppins(),
              ),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('DetailedPostPage: Error adding comment: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Error adding comment: $e',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<void> _loadFullPostData() async {
    try {
      final postDoc = await FirebaseFirestore.instance
          .collection('posts')
          .doc(widget.post['id'])
          .get();

      if (postDoc.exists && mounted) {
        final data = postDoc.data() as Map<String, dynamic>;
        setState(() {
          // Update post data with full Firebase data
          widget.post.addAll(data);

          // Ensure proper field mapping for different post types
          if (data['type'] == 'poll') {
            widget.post['pollOptions'] = data['pollOptions'];
            widget.post['expiryDate'] = data['pollEndDate'];
          } else if (data['type'] == 'code') {
            widget.post['codeContent'] = data['code'];
            widget.post['codeLanguage'] = data['codeLanguage'];
          }
        });
      }
    } catch (e) {
      debugPrint('Error loading full post data: $e');
    }
  }

  Future<void> _loadPostLikeStatus() async {
    try {
      // Use ultra-fast performance service
      final bool isLiked = await _performanceService.isPostLiked(widget.post['id']);

      if (mounted) {
        setState(() {
          widget.post['isLiked'] = isLiked;
        });
      }
    } catch (e) {
      debugPrint('Error loading post like status: $e');
    }
  }

  Future<void> _togglePostLike() async {
    // Prevent multiple taps
    if (_isLikingPost) {
      return;
    }

    setState(() {
      _isLikingPost = true;
    });

    try {
      final userContentService = Provider.of<UserContentService>(context, listen: false);

      // Use optimized toggle method
      final bool newLikeStatus = await userContentService.togglePostLike(widget.post['id']);

      if (mounted) {
        setState(() {
          final bool wasLiked = widget.post['isLiked'] ?? false;
          widget.post['isLiked'] = newLikeStatus;

          // Update like count based on the new status
          final currentLikes = widget.post['likes'] ?? 0;
          if (newLikeStatus && !wasLiked) {
            // Liked
            widget.post['likes'] = currentLikes + 1;
          } else if (!newLikeStatus && wasLiked) {
            // Unliked
            widget.post['likes'] = (currentLikes > 0) ? currentLikes - 1 : 0;
          }
        });
      }
    } catch (e) {
      debugPrint('Error toggling post like: $e');
    } finally {
      // Reset debounce flag after a delay
      Future.delayed(const Duration(milliseconds: 1000), () {
        if (mounted) {
          setState(() {
            _isLikingPost = false;
          });
        }
      });
    }
  }

  String _formatExpiryDate(dynamic expiryDate) {
    try {
      DateTime dateTime;
      if (expiryDate is Timestamp) {
        dateTime = expiryDate.toDate();
      } else if (expiryDate is DateTime) {
        dateTime = expiryDate;
      } else {
        return 'Unknown';
      }

      final now = DateTime.now();
      final difference = dateTime.difference(now);

      if (difference.isNegative) {
        return 'Expired';
      } else if (difference.inDays > 0) {
        return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''}';
      } else if (difference.inHours > 0) {
        return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''}';
      } else {
        return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''}';
      }
    } catch (e) {
      return 'Unknown';
    }
  }

  Future<void> _copyCodeToClipboard(String code) async {
    try {
      await Clipboard.setData(ClipboardData(text: code));
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Code copied to clipboard!',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.green[600],
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error copying code to clipboard: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to copy code',
              style: GoogleFonts.poppins(),
            ),
            backgroundColor: Colors.red[600],
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }
}