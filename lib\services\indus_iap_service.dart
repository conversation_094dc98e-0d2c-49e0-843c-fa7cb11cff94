import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:in_app_purchase/in_app_purchase.dart';

/// Indus AppStore In-App Purchase Service
/// Handles all in-app purchases for wallet top-ups, asset purchases, and premium features
class IndusIAPService {
  static final IndusIAPService _instance = IndusIAPService._internal();
  factory IndusIAPService() => _instance;
  IndusIAPService._internal();

  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  late StreamSubscription<List<PurchaseDetails>> _subscription;
  bool _isAvailable = false;
  List<ProductDetails> _products = [];
  List<PurchaseDetails> _purchases = [];

  // Product IDs for different purchase types (Indus AppStore - INR Currency)
  // Note: These will be dynamically generated based on user input amounts
  static const Map<String, String> walletTopUpProducts = {
    'wallet_50': 'com.asatu.wallet.50inr',     // ₹50
    'wallet_100': 'com.asatu.wallet.100inr',   // ₹100
    'wallet_200': 'com.asatu.wallet.200inr',   // ₹200
    'wallet_500': 'com.asatu.wallet.500inr',   // ₹500
    'wallet_1000': 'com.asatu.wallet.1000inr', // ₹1000
    'wallet_2000': 'com.asatu.wallet.2000inr', // ₹2000
    'wallet_5000': 'com.asatu.wallet.5000inr', // ₹5000
  };

  static const Map<String, String> premiumProducts = {
    'premium_monthly': 'com.asatu.premium.monthly',
    'premium_yearly': 'com.asatu.premium.yearly',
  };

  // All product IDs - computed at runtime
  static Set<String> get _kIds => {
    ...walletTopUpProducts.values,
    ...premiumProducts.values,
  };

  String? get currentUserId => _auth.currentUser?.uid;

  /// Initialize the in-app purchase service
  Future<bool> initialize() async {
    try {
      debugPrint('IndusIAPService: Initializing...');

      // Check if in-app purchase is available
      _isAvailable = await _inAppPurchase.isAvailable();
      debugPrint('IndusIAPService: Available = $_isAvailable');

      if (!_isAvailable) {
        debugPrint('IndusIAPService: In-app purchase not available');
        return false;
      }

      // Note: enablePendingPurchases() is no longer required for Android
      // as Google Play now requires all apps to support pending purchases by default

      // Listen to purchase updates
      _subscription = _inAppPurchase.purchaseStream.listen(
        _onPurchaseUpdate,
        onDone: () => debugPrint('IndusIAPService: Purchase stream done'),
        onError: (error) => debugPrint('IndusIAPService: Purchase stream error: $error'),
      );

      // Load products
      await _loadProducts();

      // Restore previous purchases
      await _restorePurchases();

      debugPrint('IndusIAPService: Initialization complete');
      return true;
    } catch (e) {
      debugPrint('IndusIAPService: Initialization error: $e');
      return false;
    }
  }

  /// Load available products from the store
  Future<void> _loadProducts() async {
    try {
      debugPrint('IndusIAPService: Loading products...');
      
      final ProductDetailsResponse response = await _inAppPurchase.queryProductDetails(_kIds);
      
      if (response.notFoundIDs.isNotEmpty) {
        debugPrint('IndusIAPService: Products not found: ${response.notFoundIDs}');
      }

      _products = response.productDetails;
      debugPrint('IndusIAPService: Loaded ${_products.length} products');
      
      for (final product in _products) {
        debugPrint('IndusIAPService: Product - ${product.id}: ${product.title} (${product.price})');
      }
    } catch (e) {
      debugPrint('IndusIAPService: Error loading products: $e');
    }
  }

  /// Restore previous purchases
  Future<void> _restorePurchases() async {
    try {
      debugPrint('IndusIAPService: Restoring purchases...');
      await _inAppPurchase.restorePurchases();
    } catch (e) {
      debugPrint('IndusIAPService: Error restoring purchases: $e');
    }
  }

  /// Handle purchase updates
  void _onPurchaseUpdate(List<PurchaseDetails> purchaseDetailsList) {
    debugPrint('IndusIAPService: Purchase update received: ${purchaseDetailsList.length} items');
    
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      debugPrint('IndusIAPService: Processing purchase: ${purchaseDetails.productID} - ${purchaseDetails.status}');
      
      if (purchaseDetails.status == PurchaseStatus.pending) {
        _showPendingUI();
      } else {
        if (purchaseDetails.status == PurchaseStatus.error) {
          _handleError(purchaseDetails.error!);
        } else if (purchaseDetails.status == PurchaseStatus.purchased ||
                   purchaseDetails.status == PurchaseStatus.restored) {
          _handleSuccessfulPurchase(purchaseDetails);
        }
        
        if (purchaseDetails.pendingCompletePurchase) {
          _inAppPurchase.completePurchase(purchaseDetails);
        }
      }
    }
  }

  /// Show pending purchase UI
  void _showPendingUI() {
    debugPrint('IndusIAPService: Purchase pending...');
    // You can show a loading indicator here
  }

  /// Handle purchase errors
  void _handleError(IAPError error) {
    debugPrint('IndusIAPService: Purchase error: ${error.message}');
    // Handle error - show error message to user
  }

  /// Handle successful purchases
  Future<void> _handleSuccessfulPurchase(PurchaseDetails purchaseDetails) async {
    debugPrint('IndusIAPService: Successful purchase: ${purchaseDetails.productID}');
    
    try {
      // Verify purchase on server (recommended for production)
      final bool isValid = await _verifyPurchase(purchaseDetails);
      
      if (isValid) {
        // Process the purchase based on product type
        await _processPurchase(purchaseDetails);
      } else {
        debugPrint('IndusIAPService: Purchase verification failed');
      }
    } catch (e) {
      debugPrint('IndusIAPService: Error processing purchase: $e');
    }
  }

  /// Verify purchase (implement server-side verification for production)
  Future<bool> _verifyPurchase(PurchaseDetails purchaseDetails) async {
    // For demo purposes, we'll return true
    // In production, you should verify the purchase on your server
    debugPrint('IndusIAPService: Verifying purchase: ${purchaseDetails.productID}');
    return true;
  }

  /// Process the purchase and update user data
  Future<void> _processPurchase(PurchaseDetails purchaseDetails) async {
    final String productId = purchaseDetails.productID;
    final String? userId = currentUserId;
    
    if (userId == null) {
      debugPrint('IndusIAPService: User not authenticated');
      return;
    }

    try {
      // Handle wallet top-up products
      if (walletTopUpProducts.containsValue(productId)) {
        await _processWalletTopUp(productId, purchaseDetails, userId);
      }
      // Handle premium subscription products
      else if (premiumProducts.containsValue(productId)) {
        await _processPremiumSubscription(productId, purchaseDetails, userId);
      }
      // Handle asset purchases (dynamic products)
      else {
        await _processAssetPurchase(productId, purchaseDetails, userId);
      }
    } catch (e) {
      debugPrint('IndusIAPService: Error processing purchase: $e');
    }
  }

  /// Process wallet top-up (INR Currency)
  Future<void> _processWalletTopUp(String productId, PurchaseDetails purchaseDetails, String userId) async {
    // Get the amount based on product ID (INR amounts)
    double amount = 0.0;
    switch (productId) {
      case 'com.asatu.wallet.50inr':
        amount = 50.0;
        break;
      case 'com.asatu.wallet.100inr':
        amount = 100.0;
        break;
      case 'com.asatu.wallet.200inr':
        amount = 200.0;
        break;
      case 'com.asatu.wallet.500inr':
        amount = 500.0;
        break;
      case 'com.asatu.wallet.1000inr':
        amount = 1000.0;
        break;
      case 'com.asatu.wallet.2000inr':
        amount = 2000.0;
        break;
      case 'com.asatu.wallet.5000inr':
        amount = 5000.0;
        break;
      default:
        // For custom amounts, extract from product ID
        // Format: com.asatu.wallet.custom.{amount}inr
        if (productId.startsWith('com.asatu.wallet.custom.') && productId.endsWith('inr')) {
          final amountStr = productId
              .replaceFirst('com.asatu.wallet.custom.', '')
              .replaceFirst('inr', '');
          amount = double.tryParse(amountStr) ?? 0.0;
        }
        break;
    }

    if (amount > 0) {
      await _addBalanceToWallet(userId, amount, purchaseDetails);
    }
  }

  /// Process premium subscription
  Future<void> _processPremiumSubscription(String productId, PurchaseDetails purchaseDetails, String userId) async {
    // Calculate subscription end date
    DateTime endDate;
    if (productId == 'com.asatu.premium.monthly') {
      endDate = DateTime.now().add(const Duration(days: 30));
    } else {
      endDate = DateTime.now().add(const Duration(days: 365));
    }

    // Update user's premium status
    await _firestore.collection('users').doc(userId).update({
      'isPremium': true,
      'premiumEndDate': Timestamp.fromDate(endDate),
      'premiumType': productId == 'com.asatu.premium.monthly' ? 'monthly' : 'yearly',
      'updatedAt': FieldValue.serverTimestamp(),
    });

    // Create transaction record
    await _createTransactionRecord(
      userId: userId,
      type: 'premium_subscription',
      amount: 0.0, // Premium doesn't add wallet balance
      reason: 'Premium subscription purchase',
      purchaseDetails: purchaseDetails,
    );

    debugPrint('IndusIAPService: Premium subscription activated');
  }

  /// Process asset purchase
  Future<void> _processAssetPurchase(String productId, PurchaseDetails purchaseDetails, String userId) async {
    // For asset purchases, the productId should contain asset information
    // Format: com.asatu.asset.{assetId}
    if (productId.startsWith('com.asatu.asset.')) {
      final String assetId = productId.replaceFirst('com.asatu.asset.', '');
      
      // Add asset to user's purchased assets
      await _firestore.collection('user_purchases').add({
        'userId': userId,
        'assetId': assetId,
        'purchaseId': purchaseDetails.purchaseID,
        'productId': productId,
        'purchasedAt': FieldValue.serverTimestamp(),
        'platform': 'indus_appstore',
      });

      debugPrint('IndusIAPService: Asset $assetId purchased');
    }
  }

  /// Add balance to user wallet
  Future<void> _addBalanceToWallet(String userId, double amount, PurchaseDetails purchaseDetails) async {
    await _firestore.runTransaction((transaction) async {
      final userRef = _firestore.collection('users').doc(userId);
      final userDoc = await transaction.get(userRef);

      if (!userDoc.exists) {
        throw Exception('User not found');
      }

      final userData = userDoc.data()!;
      final currentBalance = (userData['balance'] as num?)?.toDouble() ?? 0.0;
      final newBalance = currentBalance + amount;

      // Update user balance
      transaction.update(userRef, {
        'balance': newBalance,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Create transaction record
      await _createTransactionRecord(
        userId: userId,
        type: 'wallet_topup',
        amount: amount,
        reason: 'Wallet top-up via Indus AppStore',
        purchaseDetails: purchaseDetails,
        previousBalance: currentBalance,
        newBalance: newBalance,
      );
    });

    debugPrint('IndusIAPService: Wallet topped up with \$${amount.toStringAsFixed(2)}');
  }

  /// Create transaction record
  Future<void> _createTransactionRecord({
    required String userId,
    required String type,
    required double amount,
    required String reason,
    required PurchaseDetails purchaseDetails,
    double? previousBalance,
    double? newBalance,
  }) async {
    await _firestore.collection('transactions').add({
      'userId': userId,
      'type': type,
      'amount': amount,
      'reason': reason,
      'metadata': {
        'purchaseId': purchaseDetails.purchaseID,
        'productId': purchaseDetails.productID,
        'platform': 'indus_appstore',
        'transactionDate': purchaseDetails.transactionDate,
        'verificationData': purchaseDetails.verificationData.localVerificationData,
      },
      'previousBalance': previousBalance,
      'newBalance': newBalance,
      'createdAt': FieldValue.serverTimestamp(),
    });
  }

  /// Get available products
  List<ProductDetails> get products => _products;

  /// Check if service is available
  bool get isAvailable => _isAvailable;

  /// Purchase a product
  Future<bool> purchaseProduct(ProductDetails product) async {
    if (!_isAvailable) {
      debugPrint('IndusIAPService: Service not available');
      return false;
    }

    try {
      final PurchaseParam purchaseParam = PurchaseParam(productDetails: product);
      final bool success = await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
      debugPrint('IndusIAPService: Purchase initiated: $success');
      return success;
    } catch (e) {
      debugPrint('IndusIAPService: Purchase error: $e');
      return false;
    }
  }

  /// Dispose resources
  void dispose() {
    _subscription.cancel();
  }
}
