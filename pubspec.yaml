name: asatu
description: "Asatu - Digital Asset Marketplace"
publish_to: 'none'

version: 0.1.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.8
  google_fonts: ^5.1.0
  font_awesome_flutter: ^10.5.0
  percent_indicator: ^4.2.3
  flutter_svg: ^2.0.7
  lottie: ^2.7.0
  flutter_animate: ^4.5.0
  shimmer: ^3.0.0
  cached_network_image: ^3.3.1
  fl_chart: ^0.65.0
  syncfusion_flutter_charts: ^29.1.40
  intl: ^0.19.0
  flutter_staggered_grid_view: ^0.7.0
  infinite_scroll_pagination: ^4.0.0
  carousel_slider: ^5.0.0
  smooth_page_indicator: ^1.1.0
  flutter_secure_storage: ^8.1.0
  provider: ^6.1.1
  shared_preferences: ^2.2.2

  # Firebase dependencies - using minimal web-compatible versions
  firebase_core: ^2.15.1
  firebase_auth: ^4.7.3
  cloud_firestore: ^4.8.5
  firebase_storage: ^11.2.6
  firebase_messaging: ^14.6.7
  firebase_analytics: ^10.4.5
  google_sign_in: ^6.1.6
  cloud_functions: ^4.3.5

  # Security and validation
  crypto: ^3.0.3
  email_validator: ^2.1.17

  # State management and utilities
  rxdart: ^0.27.7
  uuid: ^4.2.2
  # File handling packages
  image_picker: ^1.0.7
  file_picker: ^8.0.0+1
  path: ^1.8.3
  path_provider: ^2.1.2
  file_selector: ^1.0.1
  http: ^1.1.0

  # Compression utilities
  archive: ^3.4.9
  flutter_image_compress: ^2.1.0
  url_launcher: ^6.3.1

  # Payment processing - Indus AppStore In-App Purchase
  in_app_purchase: ^3.1.13
  in_app_purchase_android: ^0.3.0+18
  in_app_purchase_storekit: ^0.3.6+7
  share_plus: ^11.0.0
  app_links: ^3.4.5
  # Legacy payment systems (will be removed)
  flutter_stripe: ^9.5.0+1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  flutter_launcher_icons: ^0.13.1

flutter:
  uses-material-design: true

  assets:
    - assets/icons/
    - assets/images/

# Flutter Launcher Icons configuration
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/icons/app_icon.png"
  adaptive_icon_background: "#6A11CB"
  adaptive_icon_foreground: "assets/icons/app_icon_foreground.png"
  adaptive_icon_foreground_image_path: "assets/icons/app_icon_foreground.png"
  windows:
    generate: true
    image_path: "assets/icons/app_icon.png"
    icon_size: 48
  web:
    generate: true
    image_path: "assets/icons/app_icon.png"
    background_color: "#6A11CB"
    theme_color: "#4F46E5"