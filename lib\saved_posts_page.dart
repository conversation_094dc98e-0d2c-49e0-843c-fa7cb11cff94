import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import 'services/user_content_service.dart';
import 'services/post_update_service.dart';
import 'detailed_post_page.dart';

class SavedPostsPage extends StatefulWidget {
  const SavedPostsPage({super.key});

  @override
  State<SavedPostsPage> createState() => _SavedPostsPageState();
}

class _SavedPostsPageState extends State<SavedPostsPage> {
  List<Map<String, dynamic>> _savedPosts = [];
  bool _isLoading = true;
  final PostUpdateService _postUpdateService = PostUpdateService();
  StreamSubscription<Map<String, dynamic>>? _postUpdateSubscription;

  @override
  void initState() {
    super.initState();
    _loadSavedPosts();
    _setupPostUpdateListener();
  }

  // Setup listener for post updates from other pages
  void _setupPostUpdateListener() {
    _postUpdateSubscription = _postUpdateService.postUpdates.listen((updateData) {
      final String postId = updateData['postId'];
      final Map<String, dynamic> updates = updateData['updates'];

      // Find the post in the saved posts list and update it
      final postIndex = _savedPosts.indexWhere((post) => post['id'] == postId);
      if (postIndex != -1 && mounted) {
        setState(() {
          _savedPosts[postIndex].addAll(updates);
        });
        debugPrint('SavedPostsPage: Updated post $postId with $updates');
      }
    });
  }

  @override
  void dispose() {
    _postUpdateSubscription?.cancel();
    super.dispose();
  }

  Future<void> _loadSavedPosts() async {
    if (!mounted) return;

    try {
      setState(() {
        _isLoading = true;
      });

      final userContentService = Provider.of<UserContentService>(context, listen: false);

      // First check saved posts count
      final savedCount = await userContentService.getSavedPostsCount();
      debugPrint('SavedPostsPage: Total saved posts count: $savedCount');

      final savedPosts = await userContentService.getSavedPosts();

      debugPrint('SavedPostsPage: Loaded ${savedPosts.length} saved posts');

      // Debug: Print details of each saved post
      for (int i = 0; i < savedPosts.length; i++) {
        final post = savedPosts[i];
        final content = post['content']?.toString() ?? 'No content';
        final contentPreview = content.length > 50 ? '${content.substring(0, 50)}...' : content;
        debugPrint('SavedPostsPage: Post $i - ID: ${post['id']}, Author: ${post['author']}, Content: $contentPreview');
      }

      if (mounted) {
        setState(() {
          _savedPosts = savedPosts;
          _isLoading = false;
        });
        debugPrint('SavedPostsPage: State updated with ${_savedPosts.length} posts');
      }
    } catch (e) {
      debugPrint('SavedPostsPage: Error loading saved posts: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _showSnackBar('Error loading saved posts: $e', isError: true);
      }
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: GoogleFonts.poppins(color: Colors.white),
        ),
        backgroundColor: isError ? Colors.red[700] : Colors.green[700],
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black87),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Saved Posts',
          style: GoogleFonts.poppins(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.black87),
            onPressed: () {
              debugPrint('SavedPostsPage: Manual refresh triggered');
              _loadSavedPosts();
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _savedPosts.isEmpty
              ? _buildEmptyState()
              : _buildSavedPostsList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bookmark_border,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 24),
          Text(
            'No saved posts yet',
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Save posts from the community to view them here',
            style: GoogleFonts.poppins(
              fontSize: 16,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSavedPostsList() {
    return RefreshIndicator(
      onRefresh: _loadSavedPosts,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _savedPosts.length,
        itemBuilder: (context, index) {
          final post = _savedPosts[index];
          return _buildPostCard(post, index);
        },
      ),
    );
  }

  Widget _buildPostCard(Map<String, dynamic> post, int index) {
    return GestureDetector(
      onTap: () {
        // Navigate to detailed post view
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => DetailedPostPage(post: post),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(10),
              blurRadius: 10,
              spreadRadius: 0,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Post header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundImage: post['authorImage'] != null && post['authorImage'].isNotEmpty
                      ? CachedNetworkImageProvider(post['authorImage'])
                      : null,
                  backgroundColor: Colors.blue[100],
                  child: post['authorImage'] == null || post['authorImage'].isEmpty
                      ? Text(
                          (post['author'] ?? 'U').substring(0, 1).toUpperCase(),
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue[700],
                          ),
                        )
                      : null,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        post['author'] ?? 'Unknown',
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      Text(
                        post['time'] ?? 'Unknown time',
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          color: Colors.black54,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.bookmark, color: Colors.blue),
                  onPressed: () async {
                    try {
                      debugPrint('Removing post ${post['id']} from saved posts');
                      final userContentService = Provider.of<UserContentService>(context, listen: false);
                      await userContentService.unsavePost(post['id']);
                      await _loadSavedPosts(); // Refresh the list
                      _showSnackBar('Post removed from saved');
                    } catch (e) {
                      debugPrint('Error removing saved post: $e');
                      _showSnackBar('Error removing saved post: $e', isError: true);
                    }
                  },
                ),
              ],
            ),
          ),

          // Post content based on type
          _buildPostContent(post),

          // Post image if available
          if (post['hasImage'] == true && post['imageUrl'] != null)
            Padding(
              padding: const EdgeInsets.all(16),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: CachedNetworkImage(
                  imageUrl: post['imageUrl'],
                  width: double.infinity,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    height: 200,
                    color: Colors.grey[200],
                    child: const Center(child: CircularProgressIndicator()),
                  ),
                  errorWidget: (context, url, error) => Container(
                    height: 200,
                    color: Colors.grey[200],
                    child: const Icon(Icons.error),
                  ),
                ),
              ),
            ),

          // Post stats
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.favorite,
                  size: 18,
                  color: Colors.red[400],
                ),
                const SizedBox(width: 6),
                Text(
                  '${post['likes'] ?? 0}',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.black54,
                  ),
                ),
                const SizedBox(width: 20),
                Icon(
                  Icons.comment,
                  size: 18,
                  color: Colors.blue[400],
                ),
                const SizedBox(width: 6),
                Text(
                  '${post['comments'] ?? 0}',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: Colors.black54,
                  ),
                ),
                const Spacer(),
                Text(
                  'Post ${index + 1} of ${_savedPosts.length}',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          ),
        ],
        ),
      ),
    );
  }

  Widget _buildPostContent(Map<String, dynamic> post) {
    final String postType = post['type'] ?? 'regular';

    switch (postType) {
      case 'poll':
        return _buildPollContent(post);
      case 'code':
        return _buildCodeContent(post);
      default:
        return _buildRegularContent(post);
    }
  }

  Widget _buildRegularContent(Map<String, dynamic> post) {
    if (post['content'] == null || post['content'].isEmpty) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Text(
        post['content'],
        style: GoogleFonts.poppins(
          fontSize: 14,
          color: Colors.black87,
          height: 1.5,
        ),
      ),
    );
  }

  Widget _buildPollContent(Map<String, dynamic> post) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (post['content'] != null && post['content'].isNotEmpty)
            Text(
              post['content'],
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.black87,
                height: 1.5,
              ),
            ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Row(
              children: [
                Icon(Icons.poll, color: Colors.blue[600], size: 20),
                const SizedBox(width: 8),
                Text(
                  'Poll Post - Tap to view details',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.blue[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCodeContent(Map<String, dynamic> post) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (post['content'] != null && post['content'].isNotEmpty)
            Text(
              post['content'],
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: Colors.black87,
                height: 1.5,
              ),
            ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: Row(
              children: [
                Icon(Icons.code, color: Colors.grey[700], size: 20),
                const SizedBox(width: 8),
                Text(
                  'Code Post - Tap to view details',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: Colors.grey[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
